module.exports = {

"[project]/app/contact/layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>ContactLayout,
    "metadata": ()=>metadata
});
const metadata = {
    title: 'Contact Us - Get in Touch',
    description: 'Contact Vierla for support, partnerships, or general inquiries. We\'re here to help you with your beauty service needs and business questions.',
    openGraph: {
        title: 'Contact Us - Get in Touch | Vierla',
        description: 'Get in touch with <PERSON><PERSON><PERSON> for support, partnerships, or general inquiries. We\'re here to help.',
        url: 'https://vierla.com/contact',
        siteName: 'Vierla',
        locale: 'en_CA',
        type: 'website'
    },
    twitter: {
        card: 'summary_large_image',
        title: 'Contact Us - Get in Touch | Vierla',
        description: 'Get in touch with <PERSON><PERSON><PERSON> for support, partnerships, or general inquiries. We\'re here to help.'
    }
};
function ContactLayout({ children }) {
    return children;
}
}),

};

//# sourceMappingURL=app_contact_layout_tsx_7c60d7ef._.js.map