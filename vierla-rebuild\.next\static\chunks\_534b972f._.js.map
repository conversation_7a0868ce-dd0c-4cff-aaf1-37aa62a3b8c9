{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx"], "sourcesContent": ["\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport React, { memo, type ReactNode } from \"react\";\n\ninterface AuroraBackgroundProps extends React.HTMLProps<HTMLDivElement> {\n  children: ReactNode;\n  showRadialGradient?: boolean;\n}\n\n// Full page wrapper version\nexport const AuroraBackground = ({\n  className,\n  children,\n  showRadialGradient = true,\n  ...props\n}: AuroraBackgroundProps) => {\n  return (\n    <div\n      className={cn(\n        \"relative flex flex-col h-[100vh] items-center justify-center text-slate-950 transition-colors duration-300\",\n        className\n      )}\n      {...props}\n    >\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div\n          className={cn(\n            `\n            [--light-bg:#F4F1E8]\n            [--dark-bg:#2D2A26]\n            [--light-aurora:repeating-linear-gradient(100deg,#364035_10%,#8B9A8C_15%,#F0E6D9_20%,#7C9A85_25%,#364035_30%)]\n            [--dark-aurora:repeating-linear-gradient(100deg,#B8956A_10%,#4B3D6B_15%,#B8956A_20%,#4B3D6B_25%,#B8956A_30%)]\n            [--light-stripes:repeating-linear-gradient(100deg,var(--light-bg)_0%,var(--light-bg)_7%,transparent_10%,transparent_12%,var(--light-bg)_16%)]\n            [--dark-stripes:repeating-linear-gradient(100deg,var(--dark-bg)_0%,var(--dark-bg)_7%,transparent_10%,transparent_12%,var(--dark-bg)_16%)]\n            [background-image:var(--light-stripes),var(--light-aurora)]\n            dark:[background-image:var(--dark-stripes),var(--dark-aurora)]\n            [background-size:300%,_200%]\n            [background-position:50%_50%,50%_50%]\n            filter blur-[10px]\n            after:content-[\"\"] after:absolute after:inset-0 after:[background-image:var(--light-stripes),var(--light-aurora)]\n            after:dark:[background-image:var(--dark-stripes),var(--dark-aurora)]\n            after:[background-size:200%,_100%]\n            after:animate-aurora after:[background-attachment:fixed] after:mix-blend-difference\n            pointer-events-none\n            absolute -inset-[10px] opacity-50 will-change-transform`,\n            showRadialGradient && `[mask-image:radial-gradient(ellipse_at_100%_0%,black_10%,transparent_70%)]`\n          )}\n        ></div>\n      </div>\n      {children}\n    </div>\n  );\n};\n\n// Background-only version for existing layouts\nexport const AuroraBackgroundLayer = memo(\n  ({\n    className,\n    showRadialGradient = true,\n  }: {\n    className?: string;\n    showRadialGradient?: boolean;\n  }) => {\n    return (\n      <div className=\"fixed inset-0 -z-10 overflow-hidden\">\n        <div\n          className={cn(\n            `\n            [--light-bg:#F4F1E8]\n            [--dark-bg:#2D2A26]\n            [--light-aurora:repeating-linear-gradient(100deg,#7C9A85_10%,#8B9A8C_15%,#E8D5D5_20%,#7C9A85_25%,#8B9A8C_30%)]\n            [--dark-aurora:repeating-linear-gradient(100deg,#B8956A_10%,#4B3D6B_15%,#B8956A_20%,#4B3D6B_25%,#B8956A_30%)]\n            [--light-stripes:repeating-linear-gradient(100deg,var(--light-bg)_0%,var(--light-bg)_7%,transparent_10%,transparent_12%,var(--light-bg)_16%)]\n            [--dark-stripes:repeating-linear-gradient(100deg,var(--dark-bg)_0%,var(--dark-bg)_7%,transparent_10%,transparent_12%,var(--dark-bg)_16%)]\n            [background-image:var(--light-stripes),var(--light-aurora)]\n            dark:[background-image:var(--dark-stripes),var(--dark-aurora)]\n            [background-size:300%,_200%]\n            [background-position:50%_50%,50%_50%]\n            filter blur-[10px]\n            after:content-[\"\"] after:absolute after:inset-0 after:[background-image:var(--light-stripes),var(--light-aurora)]\n            after:dark:[background-image:var(--dark-stripes),var(--dark-aurora)]\n            after:[background-size:200%,_100%]\n            after:animate-aurora after:[background-attachment:fixed] after:mix-blend-difference\n            pointer-events-none\n            absolute -inset-[10px] opacity-50 will-change-transform`,\n            showRadialGradient && `[mask-image:radial-gradient(ellipse_at_100%_0%,black_10%,transparent_70%)]`,\n            className\n          )}\n        ></div>\n      </div>\n    );\n  }\n);\n\nAuroraBackgroundLayer.displayName = \"AuroraBackgroundLayer\";"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAWO,MAAM,mBAAmB;QAAC,EAC/B,SAAS,EACT,QAAQ,EACR,qBAAqB,IAAI,EACzB,GAAG,OACmB;IACtB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8GACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACT,00CAkBD,sBAAuB;;;;;;;;;;;YAI5B;;;;;;;AAGP;KA1Ca;AA6CN,MAAM,sCAAwB,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,QACtC;QAAC,EACC,SAAS,EACT,qBAAqB,IAAI,EAI1B;IACC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACT,00CAkBD,sBAAuB,8EACvB;;;;;;;;;;;AAKV;;AAGF,sBAAsB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/glowing-effect.tsx"], "sourcesContent": ["\"use client\";\n\nimport { memo, useCallback, useEffect, useRef } from \"react\";\nimport { cn } from \"@/lib/utils\";\nimport { animate } from \"motion/react\";\n\ninterface GlowingEffectProps {\n  blur?: number;\n  inactiveZone?: number;\n  proximity?: number;\n  spread?: number;\n  variant?: \"default\" | \"white\" | \"sage\";\n  glow?: boolean;\n  className?: string;\n  disabled?: boolean;\n  movementDuration?: number;\n  borderWidth?: number;\n}\n\nconst GlowingEffect = memo(\n  ({\n    blur = 0,\n    inactiveZone = 0.7,\n    proximity = 0,\n    spread = 20,\n    variant = \"sage\",\n    glow = false,\n    className,\n    movementDuration = 2,\n    borderWidth = 1,\n    disabled = true,\n  }: GlowingEffectProps) => {\n    const containerRef = useRef<HTMLDivElement>(null);\n    const lastPosition = useRef({ x: 0, y: 0 });\n    const animationFrameRef = useRef<number>(0);\n\n    const handleMove = useCallback(\n      (e?: MouseEvent | { x: number; y: number }) => {\n        if (!containerRef.current) return;\n\n        if (animationFrameRef.current) {\n          cancelAnimationFrame(animationFrameRef.current);\n        }\n\n        animationFrameRef.current = requestAnimationFrame(() => {\n          const element = containerRef.current;\n          if (!element) return;\n\n          const { left, top, width, height } = element.getBoundingClientRect();\n          const mouseX = e?.x ?? lastPosition.current.x;\n          const mouseY = e?.y ?? lastPosition.current.y;\n\n          if (e) {\n            lastPosition.current = { x: mouseX, y: mouseY };\n          }\n\n          const center = [left + width * 0.5, top + height * 0.5];\n          const distanceFromCenter = Math.hypot(\n            mouseX - center[0],\n            mouseY - center[1]\n          );\n          const inactiveRadius = 0.5 * Math.min(width, height) * inactiveZone;\n\n          if (distanceFromCenter < inactiveRadius) {\n            element.style.setProperty(\"--active\", \"0\");\n            return;\n          }\n\n          const isActive =\n            mouseX > left - proximity &&\n            mouseX < left + width + proximity &&\n            mouseY > top - proximity &&\n            mouseY < top + height + proximity;\n\n          element.style.setProperty(\"--active\", isActive ? \"1\" : \"0\");\n\n          if (!isActive) return;\n\n          const currentAngle =\n            parseFloat(element.style.getPropertyValue(\"--start\")) || 0;\n          let targetAngle =\n            (180 * Math.atan2(mouseY - center[1], mouseX - center[0])) /\n              Math.PI +\n            90;\n\n          const angleDiff = ((targetAngle - currentAngle + 180) % 360) - 180;\n          const newAngle = currentAngle + angleDiff;\n\n          animate(currentAngle, newAngle, {\n            duration: movementDuration,\n            ease: [0.16, 1, 0.3, 1],\n            onUpdate: (value) => {\n              element.style.setProperty(\"--start\", String(value));\n            },\n          });\n        });\n      },\n      [inactiveZone, proximity, movementDuration]\n    );\n\n    useEffect(() => {\n      if (disabled) return;\n\n      const handleScroll = () => handleMove();\n      const handlePointerMove = (e: PointerEvent) => handleMove(e);\n\n      window.addEventListener(\"scroll\", handleScroll, { passive: true });\n      document.body.addEventListener(\"pointermove\", handlePointerMove, {\n        passive: true,\n      });\n\n      return () => {\n        if (animationFrameRef.current) {\n          cancelAnimationFrame(animationFrameRef.current);\n        }\n        window.removeEventListener(\"scroll\", handleScroll);\n        document.body.removeEventListener(\"pointermove\", handlePointerMove);\n      };\n    }, [handleMove, disabled]);\n\n    // Define gradient variants - THEME-AWARE\n    const getGradient = () => {\n      switch (variant) {\n        case \"white\":\n          return `repeating-conic-gradient(\n            from 236.84deg at 50% 50%,\n            var(--black),\n            var(--black) calc(25% / var(--repeating-conic-gradient-times))\n          )`;\n        case \"sage\":\n          // Theme-aware sage variant: Light Mode = Forest/Sage, Dark Mode = Gold\n          return `radial-gradient(circle, var(--theme-primary) 10%, transparent 20%),\n            radial-gradient(circle at 40% 40%, #F4F1E8 5%, transparent 15%),\n            radial-gradient(circle at 60% 60%, var(--theme-primary) 10%, transparent 20%),\n            radial-gradient(circle at 40% 60%, var(--theme-secondary) 10%, transparent 20%),\n            repeating-conic-gradient(\n              from 236.84deg at 50% 50%,\n              var(--theme-primary) 0%,\n              #F4F1E8 calc(25% / var(--repeating-conic-gradient-times)),\n              var(--theme-primary) calc(50% / var(--repeating-conic-gradient-times)),\n              var(--theme-secondary) calc(75% / var(--repeating-conic-gradient-times)),\n              var(--theme-primary) calc(100% / var(--repeating-conic-gradient-times))\n            )`;\n        default:\n          return `radial-gradient(circle, #dd7bbb 10%, #dd7bbb00 20%),\n            radial-gradient(circle at 40% 40%, #d79f1e 5%, #d79f1e00 15%),\n            radial-gradient(circle at 60% 60%, #5a922c 10%, #5a922c00 20%),\n            radial-gradient(circle at 40% 60%, #4c7894 10%, #4c789400 20%),\n            repeating-conic-gradient(\n              from 236.84deg at 50% 50%,\n              #dd7bbb 0%,\n              #d79f1e calc(25% / var(--repeating-conic-gradient-times)),\n              #5a922c calc(50% / var(--repeating-conic-gradient-times)),\n              #4c7894 calc(75% / var(--repeating-conic-gradient-times)),\n              #dd7bbb calc(100% / var(--repeating-conic-gradient-times))\n            )`;\n      }\n    };\n\n    return (\n      <>\n        <div\n          className={cn(\n            \"pointer-events-none absolute -inset-px hidden rounded-[inherit] border opacity-0 transition-opacity\",\n            glow && \"opacity-100\",\n            variant === \"white\" && \"border-white\",\n            disabled && \"!block\"\n          )}\n        />\n        <div\n          ref={containerRef}\n          style={\n            {\n              \"--blur\": `${blur}px`,\n              \"--spread\": spread,\n              \"--start\": \"0\",\n              \"--active\": \"0\",\n              \"--glowingeffect-border-width\": `${borderWidth}px`,\n              \"--repeating-conic-gradient-times\": \"5\",\n              \"--gradient\": getGradient(),\n            } as React.CSSProperties\n          }\n          className={cn(\n            \"pointer-events-none absolute inset-0 rounded-[inherit] opacity-100 transition-opacity\",\n            glow && \"opacity-100\",\n            blur > 0 && \"blur-[var(--blur)] \",\n            className,\n            disabled && \"!hidden\"\n          )}\n        >\n          <div\n            className={cn(\n              \"glow\",\n              \"rounded-[inherit]\",\n              'after:content-[\"\"] after:rounded-[inherit] after:absolute after:inset-[calc(-1*var(--glowingeffect-border-width))]',\n              \"after:[border:var(--glowingeffect-border-width)_solid_transparent]\",\n              \"after:[background:var(--gradient)] after:[background-attachment:fixed]\",\n              \"after:opacity-[var(--active)] after:transition-opacity after:duration-300\",\n              \"after:[mask-clip:padding-box,border-box]\",\n              \"after:[mask-composite:intersect]\",\n              \"after:[mask-image:linear-gradient(#0000,#0000),conic-gradient(from_calc((var(--start)-var(--spread))*1deg),#00000000_0deg,#fff,#00000000_calc(var(--spread)*2deg))]\"\n            )}\n          />\n        </div>\n      </>\n    );\n  }\n);\n\nGlowingEffect.displayName = \"GlowingEffect\";\n\nexport { GlowingEffect };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAmBA,MAAM,8BAAgB,GAAA,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,UACvB;QAAC,EACC,OAAO,CAAC,EACR,eAAe,GAAG,EAClB,YAAY,CAAC,EACb,SAAS,EAAE,EACX,UAAU,MAAM,EAChB,OAAO,KAAK,EACZ,SAAS,EACT,mBAAmB,CAAC,EACpB,cAAc,CAAC,EACf,WAAW,IAAI,EACI;;IACnB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IACzC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU;IAEzC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAC3B,CAAC;YACC,IAAI,CAAC,aAAa,OAAO,EAAE;YAE3B,IAAI,kBAAkB,OAAO,EAAE;gBAC7B,qBAAqB,kBAAkB,OAAO;YAChD;YAEA,kBAAkB,OAAO,GAAG;yDAAsB;oBAChD,MAAM,UAAU,aAAa,OAAO;oBACpC,IAAI,CAAC,SAAS;oBAEd,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,QAAQ,qBAAqB;wBACnD;oBAAf,MAAM,SAAS,CAAA,OAAA,cAAA,wBAAA,EAAG,CAAC,cAAJ,kBAAA,OAAQ,aAAa,OAAO,CAAC,CAAC;wBAC9B;oBAAf,MAAM,SAAS,CAAA,OAAA,cAAA,wBAAA,EAAG,CAAC,cAAJ,kBAAA,OAAQ,aAAa,OAAO,CAAC,CAAC;oBAE7C,IAAI,GAAG;wBACL,aAAa,OAAO,GAAG;4BAAE,GAAG;4BAAQ,GAAG;wBAAO;oBAChD;oBAEA,MAAM,SAAS;wBAAC,OAAO,QAAQ;wBAAK,MAAM,SAAS;qBAAI;oBACvD,MAAM,qBAAqB,KAAK,KAAK,CACnC,SAAS,MAAM,CAAC,EAAE,EAClB,SAAS,MAAM,CAAC,EAAE;oBAEpB,MAAM,iBAAiB,MAAM,KAAK,GAAG,CAAC,OAAO,UAAU;oBAEvD,IAAI,qBAAqB,gBAAgB;wBACvC,QAAQ,KAAK,CAAC,WAAW,CAAC,YAAY;wBACtC;oBACF;oBAEA,MAAM,WACJ,SAAS,OAAO,aAChB,SAAS,OAAO,QAAQ,aACxB,SAAS,MAAM,aACf,SAAS,MAAM,SAAS;oBAE1B,QAAQ,KAAK,CAAC,WAAW,CAAC,YAAY,WAAW,MAAM;oBAEvD,IAAI,CAAC,UAAU;oBAEf,MAAM,eACJ,WAAW,QAAQ,KAAK,CAAC,gBAAgB,CAAC,eAAe;oBAC3D,IAAI,cACF,AAAC,MAAM,KAAK,KAAK,CAAC,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,MAAM,CAAC,EAAE,IACtD,KAAK,EAAE,GACT;oBAEF,MAAM,YAAY,AAAC,CAAC,cAAc,eAAe,GAAG,IAAI,MAAO;oBAC/D,MAAM,WAAW,eAAe;oBAEhC,CAAA,GAAA,mLAAA,CAAA,UAAO,AAAD,EAAE,cAAc,UAAU;wBAC9B,UAAU;wBACV,MAAM;4BAAC;4BAAM;4BAAG;4BAAK;yBAAE;wBACvB,QAAQ;qEAAE,CAAC;gCACT,QAAQ,KAAK,CAAC,WAAW,CAAC,WAAW,OAAO;4BAC9C;;oBACF;gBACF;;QACF;gDACA;QAAC;QAAc;QAAW;KAAiB;IAG7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,UAAU;YAEd,MAAM;wDAAe,IAAM;;YAC3B,MAAM;6DAAoB,CAAC,IAAoB,WAAW;;YAE1D,OAAO,gBAAgB,CAAC,UAAU,cAAc;gBAAE,SAAS;YAAK;YAChE,SAAS,IAAI,CAAC,gBAAgB,CAAC,eAAe,mBAAmB;gBAC/D,SAAS;YACX;YAEA;2CAAO;oBACL,IAAI,kBAAkB,OAAO,EAAE;wBAC7B,qBAAqB,kBAAkB,OAAO;oBAChD;oBACA,OAAO,mBAAmB,CAAC,UAAU;oBACrC,SAAS,IAAI,CAAC,mBAAmB,CAAC,eAAe;gBACnD;;QACF;kCAAG;QAAC;QAAY;KAAS;IAEzB,yCAAyC;IACzC,MAAM,cAAc;QAClB,OAAQ;YACN,KAAK;gBACH,OAAQ;YAKV,KAAK;gBACH,uEAAuE;gBACvE,OAAQ;YAYV;gBACE,OAAQ;QAYZ;IACF;IAEA,qBACE;;0BACE,6LAAC;gBACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uGACA,QAAQ,eACR,YAAY,WAAW,gBACvB,YAAY;;;;;;0BAGhB,6LAAC;gBACC,KAAK;gBACL,OACE;oBACE,UAAU,AAAC,GAAO,OAAL,MAAK;oBAClB,YAAY;oBACZ,WAAW;oBACX,YAAY;oBACZ,gCAAgC,AAAC,GAAc,OAAZ,aAAY;oBAC/C,oCAAoC;oBACpC,cAAc;gBAChB;gBAEF,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,yFACA,QAAQ,eACR,OAAO,KAAK,uBACZ,WACA,YAAY;0BAGd,cAAA,6LAAC;oBACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,QACA,qBACA,sHACA,sEACA,0EACA,6EACA,4CACA,oCACA;;;;;;;;;;;;;AAMZ;;AAGF,cAAc,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/golden-glowing-card-container.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { cn } from \"@/lib/utils\";\nimport { GlowingEffect } from \"./glowing-effect\";\n\ninterface GoldenGlowingCardContainerProps {\n  children: React.ReactNode;\n  className?: string;\n  interactive?: boolean;\n}\n\nexport const GoldenGlowingCardContainer: React.FC<GoldenGlowingCardContainerProps> = React.memo(({\n  children,\n  className,\n  interactive = true,\n}) => {\n  return (\n    <div className={cn(\"relative h-full group\", className)}>\n      {/* Outer container with glowing effect */}\n      <div className=\"relative h-full rounded-[1.25rem] border-[0.75px] border-white/20 p-2 md:rounded-[1.5rem] md:p-3\">\n        <GlowingEffect\n          spread={40}\n          glow={interactive}\n          disabled={!interactive}\n          proximity={interactive ? 64 : 0}\n          inactiveZone={interactive ? 0.01 : 1}\n          borderWidth={3}\n          variant=\"sage\"\n        />\n\n        {/* Inner container with margin - Consistent glassmorphism */}\n        <div className=\"relative flex h-full flex-col justify-between gap-6 overflow-hidden rounded-xl p-6 glassmorphism-card\">\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n});\n\nexport default GoldenGlowingCardContainer;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAYO,MAAM,2CAAwE,6JAAA,CAAA,UAAK,CAAC,IAAI,MAAC;QAAC,EAC/F,QAAQ,EACR,SAAS,EACT,cAAc,IAAI,EACnB;IACC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;kBAE1C,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,yIAAA,CAAA,gBAAa;oBACZ,QAAQ;oBACR,MAAM;oBACN,UAAU,CAAC;oBACX,WAAW,cAAc,KAAK;oBAC9B,cAAc,cAAc,OAAO;oBACnC,aAAa;oBACb,SAAQ;;;;;;8BAIV,6LAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX;;uCAEe", "debugId": null}}, {"offset": {"line": 320, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/shiny-button.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { motion } from \"framer-motion\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface ShinyButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  children: React.ReactNode;\n  className?: string;\n  size?: \"sm\" | \"md\" | \"lg\";\n  variant?: \"primary\" | \"secondary\" | \"accent\";\n  shimmerColor?: string;\n  backgroundColor?: string;\n  textColor?: string;\n}\n\nconst animationProps = {\n  initial: { \"--x\": \"100%\", scale: 0.8 },\n  animate: { \"--x\": \"-100%\", scale: 1 },\n  whileTap: { scale: 0.95 },\n  transition: {\n    repeat: Infinity,\n    repeatType: \"loop\" as const,\n    repeatDelay: 1,\n    type: \"spring\" as const,\n    stiffness: 20,\n    damping: 15,\n    mass: 2,\n    scale: {\n      type: \"spring\" as const,\n      stiffness: 200,\n      damping: 5,\n      mass: 0.5,\n    },\n  },\n};\n\nexport const ShinyButton: React.FC<ShinyButtonProps> = ({\n  children,\n  className,\n  size = \"md\",\n  variant = \"primary\",\n  shimmerColor,\n  backgroundColor,\n  textColor,\n  ...props\n}) => {\n  // Size-based styling\n  const sizeClasses = {\n    sm: \"px-3 py-2 text-sm rounded-md\",\n    md: \"px-6 py-2 text-base rounded-lg\",\n    lg: \"px-8 py-3 text-lg rounded-lg\"\n  };\n\n  // Color variants with customization support - THEME-AWARE COLORS\n  const getColors = () => {\n    const baseColors = {\n      primary: {\n        // Light Mode: Vierla Forest, Dark Mode: Vierla Gold\n        bg: backgroundColor || \"var(--theme-primary, #364035)\",\n        shimmer: shimmerColor || \"#F4F1E8\",  /* Warm Cream - Shimmer effect */\n        text: textColor || \"#F4F1E8\"         /* Warm Cream - Text on primary */\n      },\n      secondary: {\n        // Light Mode: Vierla Sage, Dark Mode: Muted Gold/Champagne\n        bg: backgroundColor || \"var(--theme-secondary, #8B9A8C)\",\n        shimmer: shimmerColor || \"#F4F1E8\",  /* Warm Cream - Shimmer effect */\n        text: textColor || \"#F4F1E8\"         /* Warm Cream - Text on secondary */\n      },\n      accent: {\n        // Light Mode: Vierla Forest, Dark Mode: Vierla Gold\n        bg: backgroundColor || \"var(--theme-primary, #364035)\",\n        shimmer: shimmerColor || \"var(--theme-secondary, #8B9A8C)\",\n        text: textColor || \"#F4F1E8\"         /* Warm Cream - Text on accent */\n      }\n    };\n    return baseColors[variant];\n  };\n\n  const colors = getColors();\n\n  const {\n    onClick,\n    onMouseEnter,\n    onMouseLeave,\n    onFocus,\n    onBlur,\n    disabled,\n    type,\n    id,\n    name,\n    value\n  } = props;\n\n  return (\n    <motion.button\n      {...animationProps}\n      onClick={onClick}\n      onMouseEnter={onMouseEnter}\n      onMouseLeave={onMouseLeave}\n      onFocus={onFocus}\n      onBlur={onBlur}\n      disabled={disabled}\n      type={type}\n      id={id}\n      name={name}\n      value={value}\n      className={cn(\n        \"relative font-medium backdrop-blur-xl transition-shadow duration-300 ease-in-out hover:shadow\",\n        \"dark:hover:shadow-[0_0_20px_hsl(var(--primary)/10%)]\",\n        \"flex items-center justify-center\",\n        sizeClasses[size],\n        className\n      )}\n      style={{\n        backgroundColor: colors.bg,\n        \"--primary\": colors.shimmer,\n      } as React.CSSProperties}\n    >\n      <span\n        className=\"relative flex items-center justify-center size-full uppercase tracking-wide dark:font-light\"\n        style={{\n          ...(textColor && { color: colors.text }),\n          maskImage:\n            `linear-gradient(-75deg,${colors.shimmer} calc(var(--x) + 20%),transparent calc(var(--x) + 30%),${colors.shimmer} calc(var(--x) + 100%))`,\n        }}\n      >\n        {children}\n      </span>\n      <span\n        style={{\n          mask: \"linear-gradient(var(--mask-black), var(--mask-black)) content-box,linear-gradient(var(--mask-black), var(--mask-black))\",\n          maskComposite: \"exclude\",\n          background: `linear-gradient(-75deg,${colors.shimmer}10 calc(var(--x)+20%),${colors.shimmer}80 calc(var(--x)+25%),${colors.shimmer}10 calc(var(--x)+100%))`,\n        }}\n        className=\"absolute inset-0 z-10 block rounded-[inherit] p-px\"\n      ></span>\n    </motion.button>\n  );\n};\n\nexport default ShinyButton;\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAiBA,MAAM,iBAAiB;IACrB,SAAS;QAAE,OAAO;QAAQ,OAAO;IAAI;IACrC,SAAS;QAAE,OAAO;QAAS,OAAO;IAAE;IACpC,UAAU;QAAE,OAAO;IAAK;IACxB,YAAY;QACV,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;QACN,OAAO;YACL,MAAM;YACN,WAAW;YACX,SAAS;YACT,MAAM;QACR;IACF;AACF;AAEO,MAAM,cAA0C;QAAC,EACtD,QAAQ,EACR,SAAS,EACT,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,YAAY,EACZ,eAAe,EACf,SAAS,EACT,GAAG,OACJ;IACC,qBAAqB;IACrB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,iEAAiE;IACjE,MAAM,YAAY;QAChB,MAAM,aAAa;YACjB,SAAS;gBACP,oDAAoD;gBACpD,IAAI,mBAAmB;gBACvB,SAAS,gBAAgB;gBAAY,+BAA+B,GACpE,MAAM,aAAa,UAAkB,gCAAgC;YACvE;YACA,WAAW;gBACT,2DAA2D;gBAC3D,IAAI,mBAAmB;gBACvB,SAAS,gBAAgB;gBAAY,+BAA+B,GACpE,MAAM,aAAa,UAAkB,kCAAkC;YACzE;YACA,QAAQ;gBACN,oDAAoD;gBACpD,IAAI,mBAAmB;gBACvB,SAAS,gBAAgB;gBACzB,MAAM,aAAa,UAAkB,+BAA+B;YACtE;QACF;QACA,OAAO,UAAU,CAAC,QAAQ;IAC5B;IAEA,MAAM,SAAS;IAEf,MAAM,EACJ,OAAO,EACP,YAAY,EACZ,YAAY,EACZ,OAAO,EACP,MAAM,EACN,QAAQ,EACR,IAAI,EACJ,EAAE,EACF,IAAI,EACJ,KAAK,EACN,GAAG;IAEJ,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACX,GAAG,cAAc;QAClB,SAAS;QACT,cAAc;QACd,cAAc;QACd,SAAS;QACT,QAAQ;QACR,UAAU;QACV,MAAM;QACN,IAAI;QACJ,MAAM;QACN,OAAO;QACP,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,iGACA,wDACA,oCACA,WAAW,CAAC,KAAK,EACjB;QAEF,OAAO;YACL,iBAAiB,OAAO,EAAE;YAC1B,aAAa,OAAO,OAAO;QAC7B;;0BAEA,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,GAAI,aAAa;wBAAE,OAAO,OAAO,IAAI;oBAAC,CAAC;oBACvC,WACE,AAAC,0BAAiG,OAAxE,OAAO,OAAO,EAAC,2DAAwE,OAAf,OAAO,OAAO,EAAC;gBACrH;0BAEC;;;;;;0BAEH,6LAAC;gBACC,OAAO;oBACL,MAAM;oBACN,eAAe;oBACf,YAAY,AAAC,0BAAgE,OAAvC,OAAO,OAAO,EAAC,0BAA+D,OAAvC,OAAO,OAAO,EAAC,0BAAuC,OAAf,OAAO,OAAO,EAAC;gBACrI;gBACA,WAAU;;;;;;;;;;;;AAIlB;KAtGa;uCAwGE", "debugId": null}}, {"offset": {"line": 457, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/app/about/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { AuroraBackgroundLayer } from \"@/components/ui/aurora-background\";\nimport { GoldenGlowingCardContainer } from \"@/components/ui/golden-glowing-card-container\";\nimport ShinyButton from \"@/components/ui/shiny-button\";\nimport { Users, Target, Lightbulb, Heart } from \"lucide-react\";\nimport { useState } from \"react\";\n\nexport default function About() {\n  // Team section variables - commented out since team section is disabled\n  // const [selectedMember, setSelectedMember] = useState<number | null>(null);\n\n  /* const teamMembers = [\n    {\n      id: 1,\n      name: \"<PERSON>\",\n      position: \"Founder & CEO\",\n      bio: \"Former tech executive with 10+ years in product development. Passionate about empowering small businesses through technology.\",\n      image: \"/team/alex.jpg\"\n    },\n    {\n      id: 2,\n      name: \"<PERSON>\",\n      position: \"Head of Design\",\n      bio: \"Award-winning UX designer with expertise in creating intuitive interfaces for service-based businesses.\",\n      image: \"/team/sarah.jpg\"\n    },\n    {\n      id: 3,\n      name: \"<PERSON>\",\n      position: \"Lead Developer\",\n      bio: \"Full-stack engineer specializing in scalable platforms and AI integration for business automation.\",\n      image: \"/team/marcus.jpg\"\n    },\n    {\n      id: 4,\n      name: \"Priya Patel\",\n      position: \"Customer Success\",\n      bio: \"Beauty industry veteran dedicated to helping professionals maximize their business potential through Vierla.\",\n      image: \"/team/priya.jpg\"\n    },\n    {\n      id: 5,\n      name: \"Jordan Taylor\",\n      position: \"Marketing Director\",\n      bio: \"Growth marketing specialist with deep understanding of the beauty and wellness industry landscape.\",\n      image: \"/team/jordan.jpg\"\n    },\n    {\n      id: 6,\n      name: \"Maya Rodriguez\",\n      position: \"Operations Manager\",\n      bio: \"Operations expert focused on streamlining processes and ensuring exceptional service delivery.\",\n      image: \"/team/maya.jpg\"\n    }\n  ]; */\n\n  return (\n    <div className=\"page-about relative overflow-hidden\">\n      <AuroraBackgroundLayer />\n\n      {/* Hero Section */}\n      <section className=\"relative z-10 w-full px-4 py-20 pt-36\">\n        <div className=\"text-center max-w-6xl mx-auto\">\n          <h1 className=\"text-4xl md:text-6xl font-black mb-6 leading-none drop-shadow-lg font-sans text-[#2D2A26] dark:text-[#F4F1E8]\">\n            WE'RE ON A MISSION TO EMPOWER BEAUTY & WELLNESS PROFESSIONALS\n          </h1>\n          <p className=\"text-xl md:text-2xl mb-8 leading-relaxed max-w-4xl mx-auto drop-shadow-sm font-inter text-[#2D2A26] dark:text-[#A9A299]\">\n            Building the future of beauty and wellness business operations, one professional at a time.\n          </p>\n        </div>\n      </section>\n\n      {/* The Future of Wellness, Simplified */}\n      <section className=\"relative z-10 py-20 border-t border-white/20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-6xl mx-auto\">\n            <div className=\"text-center mb-12\">\n              <h2 className=\"text-4xl md:text-5xl font-black text-[#2D2A26] dark:text-[#F4F1E8] mb-6 drop-shadow-lg font-jost\">\n                Our Story\n              </h2>\n            </div>\n\n            <GoldenGlowingCardContainer>\n              <div className=\"p-8 md:p-12\">\n                <div className=\"prose prose-xl max-w-none\">\n                  <p className=\"text-xl leading-relaxed mb-8 text-[#2D2A26] dark:text-[#A9A299] drop-shadow-sm font-inter\">\n                    Our founder's journey began with a clear mission: finding exceptional beauty professionals who could deliver the perfect fade and master the art of protective styling. Hours of research through social media, reviews, and recommendations revealed the challenge that talented professionals and discerning clients face daily—connecting with confidence and clarity.\n                  </p>\n\n                  <p className=\"text-xl leading-relaxed mb-8 text-[#2D2A26] dark:text-[#A9A299] drop-shadow-sm font-inter\">\n                    This experience illuminated a transformative opportunity in the beauty and wellness industry. Skilled professionals needed better ways to showcase their expertise and connect with ideal clients. Clients deserved transparent access to verified professionals, clear pricing, and authentic portfolios. The market was ready for a comprehensive solution that would serve both sides with excellence.\n                  </p>\n\n                  <p className=\"text-xl leading-relaxed mb-8 text-[#2D2A26] dark:text-[#A9A299] drop-shadow-sm font-inter\">\n                    Founded in 2025, Vierla emerged from a vision to revolutionize how beauty service providers connect with customers. We recognized the need for a comprehensive platform that empowers providers while simplifying discovery and booking for customers.\n                  </p>\n\n                  <p className=\"text-xl leading-relaxed mb-8 text-[#2D2A26] dark:text-[#A9A299] drop-shadow-sm font-inter\">\n                    We're building an ecosystem that benefits both sides of the beauty industry, helping providers grow their businesses while giving customers easy access to quality beauty and self-care services in their area. Our platform builds trust through transparency and empowers success through comprehensive tools. Service providers gain access to complete business management systems covering booking, payments, client relationships, and marketing. Clients discover verified professionals through curated marketplaces featuring authentic portfolios and confident booking experiences.\n                  </p>\n\n                  <p className=\"text-xl leading-relaxed mb-8 text-[#2D2A26] dark:text-[#A9A299] drop-shadow-sm font-inter\">\n                    Today, Vierla empowers beauty and wellness professionals to excel in their craft while we streamline their business operations. Master barbers perfect their techniques, braiding artists expand their clientele, and wellness professionals grow sustainable businesses. We provide the foundation that transforms passion into prosperity.\n                  </p>\n\n                  <div className=\"text-center mt-12\">\n                    <p className=\"text-2xl font-semibold text-[#2D2A26] dark:text-[#F4F1E8] drop-shadow-lg font-tai-heritage\">\n                      We build the future where every beauty professional thrives through innovation, connection, and excellence.\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </GoldenGlowingCardContainer>\n          </div>\n        </div>\n      </section>\n\n      {/* Meet the Team Section - COMMENTED OUT */}\n      {/*\n      <section className=\"relative z-10 py-20 border-t border-white/20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-6xl mx-auto\">\n            <div className=\"text-center mb-16\">\n              <div className=\"flex items-center justify-center space-x-3 mb-6\">\n                <Users className=\"h-10 w-10 drop-shadow-lg\" style={{ color: 'var(--icon-accent)' }} />\n                <h2 className=\"text-4xl md:text-5xl font-black text-[#2D2A26] dark:text-[#F4F1E8] drop-shadow-lg font-jost\">Meet the Team</h2>\n              </div>\n              <p className=\"text-xl text-[#2D2A26] dark:text-[#A9A299] max-w-3xl mx-auto drop-shadow-sm font-inter\">\n                We're a passionate group of entrepreneurs, developers, and designers united by our mission to empower beauty professionals.\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8 mb-12\">\n              {teamMembers.map((member) => (\n                <div key={member.id} className=\"flex flex-col items-center\">\n                  <div\n                    className=\"relative group cursor-pointer\"\n                    onMouseEnter={() => setSelectedMember(member.id)}\n                    onMouseLeave={() => setSelectedMember(null)}\n                    onClick={() => setSelectedMember(selectedMember === member.id ? null : member.id)}\n                  >\n                    <div className=\"w-24 h-24 md:w-32 md:h-32 rounded-full overflow-hidden border-4 border-theme-primary/30 group-hover:border-theme-primary transition-all duration-300 shadow-lg group-hover:shadow-xl\">\n                      <div className=\"w-full h-full bg-gradient-to-br from-theme-primary/20 to-theme-primary/10 flex items-center justify-center\">\n                        <Users className=\"w-8 h-8 md:w-12 md:h-12\" style={{ color: 'var(--theme-primary)' }} />\n                      </div>\n                    </div>\n                    <div className=\"absolute -inset-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-md\"\n                         style={{ background: 'linear-gradient(45deg, var(--theme-primary)/20, var(--theme-primary)/40, var(--theme-primary)/20)' }} />\n                  </div>\n                  <h3 className=\"mt-4 text-lg font-semibold text-[#2D2A26] dark:text-[#F4F1E8] text-center font-tai-heritage\">\n                    {member.name}\n                  </h3>\n                  <p className=\"text-sm text-[#2D2A26] dark:text-[#A9A299] text-center font-inter\">\n                    {member.position}\n                  </p>\n                </div>\n              ))}\n            </div>\n\n            <div className=\"mt-8\">\n              <GoldenGlowingCardContainer>\n                <div className=\"p-8 text-center\">\n                  {(() => {\n                    const member = teamMembers.find(m => m.id === selectedMember);\n                    return member ? (\n                      <div>\n                        <h3 className=\"text-2xl font-bold text-[#2D2A26] dark:text-[#F4F1E8] mb-2 font-tai-heritage\">\n                          {member.name}\n                        </h3>\n                        <p className=\"text-lg text-theme-primary mb-4 font-inter\">\n                          {member.position}\n                        </p>\n                        <p className=\"text-lg text-[#2D2A26] dark:text-[#A9A299] leading-relaxed max-w-3xl mx-auto font-inter\">\n                          {member.bio}\n                        </p>\n                      </div>\n                    ) : null;\n                  })()}\n                </div>\n              </GoldenGlowingCardContainer>\n            </div>\n          </div>\n        </div>\n      </section>\n      */}\n\n      {/* CTA Section */}\n      <section className=\"relative z-10 py-12 border-t border-white/20\">\n        <div className=\"container mx-auto px-4\">\n        <div className=\"max-w-4xl mx-auto\">\n          <GoldenGlowingCardContainer interactive={false}>\n            <div className=\"text-center pt-8 pb-4 px-6\">\n              <h2 className=\"font-heading text-3xl leading-[1.1] sm:text-4xl md:text-6xl drop-shadow-lg text-center font-tai-heritage text-[#2D2A26] dark:text-[#F4F1E8] mb-6\">\n                Ready to join our mission?\n              </h2>\n              <p className=\"max-w-[42rem] leading-normal sm:text-xl sm:leading-8 drop-shadow-sm text-center font-sans text-[#2D2A26] dark:text-[#A9A299] mb-8 mx-auto\">\n                Be part of the future of business operations. Start your journey with Vierla today.\n              </p>\n              <div className=\"flex justify-center\">\n                <ShinyButton size=\"lg\" className=\"px-10 py-5 text-xl text-white dark:text-[var(--master-text-primary-dark)]\">\n                  <a href=\"/apply\">Get Started</a>\n                </ShinyButton>\n              </div>\n            </div>\n          </GoldenGlowingCardContainer>\n        </div>\n        </div>\n      </section>\n\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAIA;AACA;AACA;AANA;;;;;AAUe,SAAS;IACtB,wEAAwE;IACxE,6EAA6E;IAE7E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA2CG,GAEH,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,4IAAA,CAAA,wBAAqB;;;;;0BAGtB,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAgH;;;;;;sCAG9H,6LAAC;4BAAE,WAAU;sCAA0H;;;;;;;;;;;;;;;;;0BAO3I,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CAAmG;;;;;;;;;;;0CAKnH,6LAAC,8JAAA,CAAA,6BAA0B;0CACzB,cAAA,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAA4F;;;;;;0DAIzG,6LAAC;gDAAE,WAAU;0DAA4F;;;;;;0DAIzG,6LAAC;gDAAE,WAAU;0DAA4F;;;;;;0DAIzG,6LAAC;gDAAE,WAAU;0DAA4F;;;;;;0DAIzG,6LAAC;gDAAE,WAAU;0DAA4F;;;;;;0DAIzG,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;8DAA6F;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAiFxH,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC;oBAAI,WAAU;8BACf,cAAA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,8JAAA,CAAA,6BAA0B;4BAAC,aAAa;sCACvC,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAmJ;;;;;;kDAGjK,6LAAC;wCAAE,WAAU;kDAA4I;;;;;;kDAGzJ,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,uIAAA,CAAA,UAAW;4CAAC,MAAK;4CAAK,WAAU;sDAC/B,cAAA,6LAAC;gDAAE,MAAK;0DAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnC;KA5MwB", "debugId": null}}]}