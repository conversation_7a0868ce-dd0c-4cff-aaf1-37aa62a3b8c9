{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx"], "sourcesContent": ["\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport React, { memo, type ReactNode } from \"react\";\n\ninterface AuroraBackgroundProps extends React.HTMLProps<HTMLDivElement> {\n  children: ReactNode;\n  showRadialGradient?: boolean;\n}\n\n// Full page wrapper version\nexport const AuroraBackground = ({\n  className,\n  children,\n  showRadialGradient = true,\n  ...props\n}: AuroraBackgroundProps) => {\n  return (\n    <div\n      className={cn(\n        \"relative flex flex-col h-[100vh] items-center justify-center text-slate-950 transition-colors duration-300\",\n        className\n      )}\n      {...props}\n    >\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div\n          className={cn(\n            `\n            [--light-bg:#F4F1E8]\n            [--dark-bg:#2D2A26]\n            [--light-aurora:repeating-linear-gradient(100deg,#364035_10%,#8B9A8C_15%,#F0E6D9_20%,#7C9A85_25%,#364035_30%)]\n            [--dark-aurora:repeating-linear-gradient(100deg,#B8956A_10%,#4B3D6B_15%,#B8956A_20%,#4B3D6B_25%,#B8956A_30%)]\n            [--light-stripes:repeating-linear-gradient(100deg,var(--light-bg)_0%,var(--light-bg)_7%,transparent_10%,transparent_12%,var(--light-bg)_16%)]\n            [--dark-stripes:repeating-linear-gradient(100deg,var(--dark-bg)_0%,var(--dark-bg)_7%,transparent_10%,transparent_12%,var(--dark-bg)_16%)]\n            [background-image:var(--light-stripes),var(--light-aurora)]\n            dark:[background-image:var(--dark-stripes),var(--dark-aurora)]\n            [background-size:300%,_200%]\n            [background-position:50%_50%,50%_50%]\n            filter blur-[10px]\n            after:content-[\"\"] after:absolute after:inset-0 after:[background-image:var(--light-stripes),var(--light-aurora)]\n            after:dark:[background-image:var(--dark-stripes),var(--dark-aurora)]\n            after:[background-size:200%,_100%]\n            after:animate-aurora after:[background-attachment:fixed] after:mix-blend-difference\n            pointer-events-none\n            absolute -inset-[10px] opacity-50 will-change-transform`,\n            showRadialGradient && `[mask-image:radial-gradient(ellipse_at_100%_0%,black_10%,transparent_70%)]`\n          )}\n        ></div>\n      </div>\n      {children}\n    </div>\n  );\n};\n\n// Background-only version for existing layouts\nexport const AuroraBackgroundLayer = memo(\n  ({\n    className,\n    showRadialGradient = true,\n  }: {\n    className?: string;\n    showRadialGradient?: boolean;\n  }) => {\n    return (\n      <div className=\"fixed inset-0 -z-10 overflow-hidden\">\n        <div\n          className={cn(\n            `\n            [--light-bg:#F4F1E8]\n            [--dark-bg:#2D2A26]\n            [--light-aurora:repeating-linear-gradient(100deg,#7C9A85_10%,#8B9A8C_15%,#E8D5D5_20%,#7C9A85_25%,#8B9A8C_30%)]\n            [--dark-aurora:repeating-linear-gradient(100deg,#B8956A_10%,#4B3D6B_15%,#B8956A_20%,#4B3D6B_25%,#B8956A_30%)]\n            [--light-stripes:repeating-linear-gradient(100deg,var(--light-bg)_0%,var(--light-bg)_7%,transparent_10%,transparent_12%,var(--light-bg)_16%)]\n            [--dark-stripes:repeating-linear-gradient(100deg,var(--dark-bg)_0%,var(--dark-bg)_7%,transparent_10%,transparent_12%,var(--dark-bg)_16%)]\n            [background-image:var(--light-stripes),var(--light-aurora)]\n            dark:[background-image:var(--dark-stripes),var(--dark-aurora)]\n            [background-size:300%,_200%]\n            [background-position:50%_50%,50%_50%]\n            filter blur-[10px]\n            after:content-[\"\"] after:absolute after:inset-0 after:[background-image:var(--light-stripes),var(--light-aurora)]\n            after:dark:[background-image:var(--dark-stripes),var(--dark-aurora)]\n            after:[background-size:200%,_100%]\n            after:animate-aurora after:[background-attachment:fixed] after:mix-blend-difference\n            pointer-events-none\n            absolute -inset-[10px] opacity-50 will-change-transform`,\n            showRadialGradient && `[mask-image:radial-gradient(ellipse_at_100%_0%,black_10%,transparent_70%)]`,\n            className\n          )}\n        ></div>\n      </div>\n    );\n  }\n);\n\nAuroraBackgroundLayer.displayName = \"AuroraBackgroundLayer\";"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAWO,MAAM,mBAAmB,CAAC,EAC/B,SAAS,EACT,QAAQ,EACR,qBAAqB,IAAI,EACzB,GAAG,OACmB;IACtB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8GACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;;;;;;;;;;;;;;;mEAiBsD,CAAC,EACxD,sBAAsB,CAAC,0EAA0E,CAAC;;;;;;;;;;;YAIvG;;;;;;;AAGP;AAGO,MAAM,sCAAwB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EACtC,CAAC,EACC,SAAS,EACT,qBAAqB,IAAI,EAI1B;IACC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;;;;;;;;;;;;;;;mEAiBsD,CAAC,EACxD,sBAAsB,CAAC,0EAA0E,CAAC,EAClG;;;;;;;;;;;AAKV;AAGF,sBAAsB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/shiny-button.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { motion } from \"framer-motion\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface ShinyButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  children: React.ReactNode;\n  className?: string;\n  size?: \"sm\" | \"md\" | \"lg\";\n  variant?: \"primary\" | \"secondary\" | \"accent\";\n  shimmerColor?: string;\n  backgroundColor?: string;\n  textColor?: string;\n}\n\nconst animationProps = {\n  initial: { \"--x\": \"100%\", scale: 0.8 },\n  animate: { \"--x\": \"-100%\", scale: 1 },\n  whileTap: { scale: 0.95 },\n  transition: {\n    repeat: Infinity,\n    repeatType: \"loop\" as const,\n    repeatDelay: 1,\n    type: \"spring\" as const,\n    stiffness: 20,\n    damping: 15,\n    mass: 2,\n    scale: {\n      type: \"spring\" as const,\n      stiffness: 200,\n      damping: 5,\n      mass: 0.5,\n    },\n  },\n};\n\nexport const ShinyButton: React.FC<ShinyButtonProps> = ({\n  children,\n  className,\n  size = \"md\",\n  variant = \"primary\",\n  shimmerColor,\n  backgroundColor,\n  textColor,\n  ...props\n}) => {\n  // Size-based styling\n  const sizeClasses = {\n    sm: \"px-3 py-2 text-sm rounded-md\",\n    md: \"px-6 py-2 text-base rounded-lg\",\n    lg: \"px-8 py-3 text-lg rounded-lg\"\n  };\n\n  // Color variants with customization support - THEME-AWARE COLORS\n  const getColors = () => {\n    const baseColors = {\n      primary: {\n        // Light Mode: Vierla Forest, Dark Mode: Vierla Gold\n        bg: backgroundColor || \"var(--theme-primary, #364035)\",\n        shimmer: shimmerColor || \"#F4F1E8\",  /* Warm Cream - Shimmer effect */\n        text: textColor || \"#F4F1E8\"         /* Warm Cream - Text on primary */\n      },\n      secondary: {\n        // Light Mode: Vierla Sage, Dark Mode: Muted Gold/Champagne\n        bg: backgroundColor || \"var(--theme-secondary, #8B9A8C)\",\n        shimmer: shimmerColor || \"#F4F1E8\",  /* Warm Cream - Shimmer effect */\n        text: textColor || \"#F4F1E8\"         /* Warm Cream - Text on secondary */\n      },\n      accent: {\n        // Light Mode: Vierla Forest, Dark Mode: Vierla Gold\n        bg: backgroundColor || \"var(--theme-primary, #364035)\",\n        shimmer: shimmerColor || \"var(--theme-secondary, #8B9A8C)\",\n        text: textColor || \"#F4F1E8\"         /* Warm Cream - Text on accent */\n      }\n    };\n    return baseColors[variant];\n  };\n\n  const colors = getColors();\n\n  const {\n    onClick,\n    onMouseEnter,\n    onMouseLeave,\n    onFocus,\n    onBlur,\n    disabled,\n    type,\n    id,\n    name,\n    value\n  } = props;\n\n  return (\n    <motion.button\n      {...animationProps}\n      onClick={onClick}\n      onMouseEnter={onMouseEnter}\n      onMouseLeave={onMouseLeave}\n      onFocus={onFocus}\n      onBlur={onBlur}\n      disabled={disabled}\n      type={type}\n      id={id}\n      name={name}\n      value={value}\n      className={cn(\n        \"relative font-medium backdrop-blur-xl transition-shadow duration-300 ease-in-out hover:shadow\",\n        \"dark:hover:shadow-[0_0_20px_hsl(var(--primary)/10%)]\",\n        \"flex items-center justify-center\",\n        sizeClasses[size],\n        className\n      )}\n      style={{\n        backgroundColor: colors.bg,\n        \"--primary\": colors.shimmer,\n      } as React.CSSProperties}\n    >\n      <span\n        className=\"relative flex items-center justify-center size-full uppercase tracking-wide dark:font-light\"\n        style={{\n          ...(textColor && { color: colors.text }),\n          maskImage:\n            `linear-gradient(-75deg,${colors.shimmer} calc(var(--x) + 20%),transparent calc(var(--x) + 30%),${colors.shimmer} calc(var(--x) + 100%))`,\n        }}\n      >\n        {children}\n      </span>\n      <span\n        style={{\n          mask: \"linear-gradient(var(--mask-black), var(--mask-black)) content-box,linear-gradient(var(--mask-black), var(--mask-black))\",\n          maskComposite: \"exclude\",\n          background: `linear-gradient(-75deg,${colors.shimmer}10 calc(var(--x)+20%),${colors.shimmer}80 calc(var(--x)+25%),${colors.shimmer}10 calc(var(--x)+100%))`,\n        }}\n        className=\"absolute inset-0 z-10 block rounded-[inherit] p-px\"\n      ></span>\n    </motion.button>\n  );\n};\n\nexport default ShinyButton;\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAiBA,MAAM,iBAAiB;IACrB,SAAS;QAAE,OAAO;QAAQ,OAAO;IAAI;IACrC,SAAS;QAAE,OAAO;QAAS,OAAO;IAAE;IACpC,UAAU;QAAE,OAAO;IAAK;IACxB,YAAY;QACV,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;QACN,OAAO;YACL,MAAM;YACN,WAAW;YACX,SAAS;YACT,MAAM;QACR;IACF;AACF;AAEO,MAAM,cAA0C,CAAC,EACtD,QAAQ,EACR,SAAS,EACT,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,YAAY,EACZ,eAAe,EACf,SAAS,EACT,GAAG,OACJ;IACC,qBAAqB;IACrB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,iEAAiE;IACjE,MAAM,YAAY;QAChB,MAAM,aAAa;YACjB,SAAS;gBACP,oDAAoD;gBACpD,IAAI,mBAAmB;gBACvB,SAAS,gBAAgB;gBAAY,+BAA+B,GACpE,MAAM,aAAa,UAAkB,gCAAgC;YACvE;YACA,WAAW;gBACT,2DAA2D;gBAC3D,IAAI,mBAAmB;gBACvB,SAAS,gBAAgB;gBAAY,+BAA+B,GACpE,MAAM,aAAa,UAAkB,kCAAkC;YACzE;YACA,QAAQ;gBACN,oDAAoD;gBACpD,IAAI,mBAAmB;gBACvB,SAAS,gBAAgB;gBACzB,MAAM,aAAa,UAAkB,+BAA+B;YACtE;QACF;QACA,OAAO,UAAU,CAAC,QAAQ;IAC5B;IAEA,MAAM,SAAS;IAEf,MAAM,EACJ,OAAO,EACP,YAAY,EACZ,YAAY,EACZ,OAAO,EACP,MAAM,EACN,QAAQ,EACR,IAAI,EACJ,EAAE,EACF,IAAI,EACJ,KAAK,EACN,GAAG;IAEJ,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACX,GAAG,cAAc;QAClB,SAAS;QACT,cAAc;QACd,cAAc;QACd,SAAS;QACT,QAAQ;QACR,UAAU;QACV,MAAM;QACN,IAAI;QACJ,MAAM;QACN,OAAO;QACP,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iGACA,wDACA,oCACA,WAAW,CAAC,KAAK,EACjB;QAEF,OAAO;YACL,iBAAiB,OAAO,EAAE;YAC1B,aAAa,OAAO,OAAO;QAC7B;;0BAEA,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,GAAI,aAAa;wBAAE,OAAO,OAAO,IAAI;oBAAC,CAAC;oBACvC,WACE,CAAC,uBAAuB,EAAE,OAAO,OAAO,CAAC,uDAAuD,EAAE,OAAO,OAAO,CAAC,uBAAuB,CAAC;gBAC7I;0BAEC;;;;;;0BAEH,8OAAC;gBACC,OAAO;oBACL,MAAM;oBACN,eAAe;oBACf,YAAY,CAAC,uBAAuB,EAAE,OAAO,OAAO,CAAC,sBAAsB,EAAE,OAAO,OAAO,CAAC,sBAAsB,EAAE,OAAO,OAAO,CAAC,uBAAuB,CAAC;gBAC7J;gBACA,WAAU;;;;;;;;;;;;AAIlB;uCAEe", "debugId": null}}, {"offset": {"line": 226, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          // Updated to use new Vierla color specifications\n          \"flex h-10 w-full rounded-md border transition-all duration-200\",\n          \"bg-[var(--master-input-bg-light)] dark:bg-[var(--master-input-bg-dark)]\",\n          \"border-[var(--master-input-border)] text-[var(--master-text-primary-light)] dark:text-[var(--master-text-primary-dark)]\",\n          \"px-3 py-2 text-base font-[var(--font-family-body)]\",\n          \"placeholder:text-[var(--master-text-secondary-light)] dark:placeholder:text-[var(--master-text-secondary-dark)]\",\n          \"focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[var(--master-input-border-focus)] focus-visible:border-[var(--master-input-border-focus)]\",\n          \"disabled:cursor-not-allowed disabled:opacity-50\",\n          \"file:border-0 file:bg-transparent file:text-sm file:font-medium\",\n          \"md:text-sm\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,qMAAA,CAAA,aAAgB,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iDAAiD;QACjD,kEACA,2EACA,2HACA,sDACA,mHACA,iKACA,mDACA,mEACA,cACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 254, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-[var(--master-label-color)] font-[var(--font-family-body)]\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,qMAAA,CAAA,aAAgB,CAI5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 284, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,qMAAA,CAAA,aAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,aAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 363, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { Check } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Checkbox = React.forwardRef<\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <CheckboxPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\n      className\n    )}\n    {...props}\n  >\n    <CheckboxPrimitive.Indicator\n      className={cn(\"flex items-center justify-center text-current\")}\n    >\n      <Check className=\"h-4 w-4\" />\n    </CheckboxPrimitive.Indicator>\n  </CheckboxPrimitive.Root>\n))\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,yBAAW,qMAAA,CAAA,aAAgB,CAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,oKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kTACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE;sBAEd,cAAA,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,oKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 406, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Textarea = React.forwardRef<\n  HTMLTextAreaElement,\n  React.ComponentProps<\"textarea\">\n>(({ className, ...props }, ref) => {\n  return (\n    <textarea\n      className={cn(\n        // Updated to use new Vierla color specifications\n        \"flex min-h-[80px] w-full rounded-md border transition-all duration-200\",\n        \"bg-[var(--master-input-bg-light)] dark:bg-[var(--master-input-bg-dark)]\",\n        \"border-[var(--master-input-border)] text-[var(--master-text-primary-light)] dark:text-[var(--master-text-primary-dark)]\",\n        \"px-3 py-2 text-base font-[var(--font-family-body)]\",\n        \"placeholder:text-[var(--master-text-secondary-light)] dark:placeholder:text-[var(--master-text-secondary-dark)]\",\n        \"focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[var(--master-input-border-focus)] focus-visible:border-[var(--master-input-border-focus)]\",\n        \"disabled:cursor-not-allowed disabled:opacity-50\",\n        \"md:text-sm\",\n        className\n      )}\n      ref={ref}\n      {...props}\n    />\n  )\n})\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,yBAAW,qMAAA,CAAA,aAAgB,CAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iDAAiD;QACjD,0EACA,2EACA,2HACA,sDACA,mHACA,iKACA,mDACA,cACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AACA,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 433, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/dropdown-select.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport { cn } from '@/lib/utils';\nimport { ChevronDown, Check } from 'lucide-react';\n\nexport interface DropdownOption {\n  value: string;\n  label: string;\n  disabled?: boolean;\n}\n\nexport interface DropdownSelectProps {\n  options: DropdownOption[];\n  value?: string;\n  placeholder?: string;\n  onChange?: (value: string) => void;\n  className?: string;\n  disabled?: boolean;\n  error?: string;\n  label?: string;\n  required?: boolean;\n}\n\nexport function DropdownSelect({\n  options,\n  value,\n  placeholder = \"Select an option\",\n  onChange,\n  className,\n  disabled = false,\n  error,\n  label,\n  required = false,\n}: DropdownSelectProps) {\n  const [isOpen, setIsOpen] = useState(false);\n  const [selectedValue, setSelectedValue] = useState(value || '');\n  const dropdownRef = useRef<HTMLDivElement>(null);\n\n  const selectedOption = options.find(option => option.value === selectedValue);\n\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => document.removeEventListener('mousedown', handleClickOutside);\n  }, []);\n\n  useEffect(() => {\n    if (value !== undefined) {\n      setSelectedValue(value);\n    }\n  }, [value]);\n\n  const handleSelect = (optionValue: string) => {\n    setSelectedValue(optionValue);\n    setIsOpen(false);\n    onChange?.(optionValue);\n  };\n\n  const handleKeyDown = (event: React.KeyboardEvent) => {\n    if (disabled) return;\n\n    switch (event.key) {\n      case 'Enter':\n      case ' ':\n        event.preventDefault();\n        setIsOpen(!isOpen);\n        break;\n      case 'Escape':\n        setIsOpen(false);\n        break;\n      case 'ArrowDown':\n        event.preventDefault();\n        if (!isOpen) {\n          setIsOpen(true);\n        } else {\n          // Focus next option\n          const currentIndex = options.findIndex(opt => opt.value === selectedValue);\n          const nextIndex = Math.min(currentIndex + 1, options.length - 1);\n          if (options[nextIndex] && !options[nextIndex].disabled) {\n            handleSelect(options[nextIndex].value);\n          }\n        }\n        break;\n      case 'ArrowUp':\n        event.preventDefault();\n        if (isOpen) {\n          const currentIndex = options.findIndex(opt => opt.value === selectedValue);\n          const prevIndex = Math.max(currentIndex - 1, 0);\n          if (options[prevIndex] && !options[prevIndex].disabled) {\n            handleSelect(options[prevIndex].value);\n          }\n        }\n        break;\n    }\n  };\n\n  return (\n    <div className={cn(\"relative\", className)}>\n      {label && (\n        <label className=\"block text-sm font-medium mb-2 font-sans text-[#2D2A26] dark:text-[#F0E6D9]\">\n          {label}\n          {required && <span className=\"text-error ml-1\">*</span>}\n        </label>\n      )}\n      \n      <div ref={dropdownRef} className=\"relative\">\n        <button\n          type=\"button\"\n          onClick={() => !disabled && setIsOpen(!isOpen)}\n          onKeyDown={handleKeyDown}\n          disabled={disabled}\n          className={cn(\n            \"relative w-full rounded-lg px-4 py-3 text-left font-sans transition-all duration-200\",\n            \"bg-light-charcoal border border-sage/30 text-light-off-white\",\n            \"focus:outline-none focus:ring-2 focus:ring-muted-gold/30 focus:border-muted-gold\",\n            \"hover:border-sage/50\",\n            disabled && \"opacity-50 cursor-not-allowed\",\n            error && \"border-error focus:ring-error/30\",\n            isOpen && \"ring-2 ring-muted-gold/30 border-muted-gold\"\n          )}\n          aria-haspopup=\"listbox\"\n          aria-expanded={isOpen}\n          aria-labelledby={label ? `${label}-label` : undefined}\n        >\n          <span className=\"block truncate\">\n            {selectedOption ? selectedOption.label : placeholder}\n          </span>\n          <span className=\"absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none\">\n            <ChevronDown\n              className={cn(\n                \"w-4 h-4 text-mantle-300 transition-transform duration-200\",\n                isOpen && \"rotate-180\"\n              )}\n            />\n          </span>\n        </button>\n\n        {isOpen && (\n          <div className=\"absolute z-50 w-full mt-1 bg-neutral-charcoal-dark/80 backdrop-blur-lg supports-[backdrop-filter]:bg-neutral-charcoal-dark/70 border border-brand-sage/20 rounded-lg shadow-2xl max-h-60 overflow-auto\">\n            <ul role=\"listbox\" className=\"py-1\">\n              {options.map((option) => (\n                <li\n                  key={option.value}\n                  role=\"option\"\n                  aria-selected={selectedValue === option.value}\n                  className={cn(\n                    \"relative cursor-pointer select-none py-2 pl-10 pr-4 text-[#F4F1E8] dark:text-light-off-white font-sans\",\n                    \"hover:bg-light-charcoal focus:bg-light-charcoal\",\n                    option.disabled && \"opacity-50 cursor-not-allowed\",\n                    selectedValue === option.value && \"bg-light-charcoal\"\n                  )}\n                  onClick={() => !option.disabled && handleSelect(option.value)}\n                >\n                  <span className=\"block truncate\">{option.label}</span>\n                  {selectedValue === option.value && (\n                    <span className=\"absolute inset-y-0 left-0 flex items-center pl-3\">\n                      <Check className=\"w-4 h-4 text-terracotta\" />\n                    </span>\n                  )}\n                </li>\n              ))}\n            </ul>\n          </div>\n        )}\n      </div>\n\n      {error && (\n        <p className=\"mt-1 text-sm text-error font-sans\">{error}</p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAJA;;;;;AAwBO,SAAS,eAAe,EAC7B,OAAO,EACP,KAAK,EACL,cAAc,kBAAkB,EAChC,QAAQ,EACR,SAAS,EACT,WAAW,KAAK,EAChB,KAAK,EACL,KAAK,EACL,WAAW,KAAK,EACI;IACpB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;IAC5D,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,MAAM,iBAAiB,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,KAAK,KAAK;IAE/D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,UAAU;YACZ;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,WAAW;YACvB,iBAAiB;QACnB;IACF,GAAG;QAAC;KAAM;IAEV,MAAM,eAAe,CAAC;QACpB,iBAAiB;QACjB,UAAU;QACV,WAAW;IACb;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,UAAU;QAEd,OAAQ,MAAM,GAAG;YACf,KAAK;YACL,KAAK;gBACH,MAAM,cAAc;gBACpB,UAAU,CAAC;gBACX;YACF,KAAK;gBACH,UAAU;gBACV;YACF,KAAK;gBACH,MAAM,cAAc;gBACpB,IAAI,CAAC,QAAQ;oBACX,UAAU;gBACZ,OAAO;oBACL,oBAAoB;oBACpB,MAAM,eAAe,QAAQ,SAAS,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK;oBAC5D,MAAM,YAAY,KAAK,GAAG,CAAC,eAAe,GAAG,QAAQ,MAAM,GAAG;oBAC9D,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,EAAE;wBACtD,aAAa,OAAO,CAAC,UAAU,CAAC,KAAK;oBACvC;gBACF;gBACA;YACF,KAAK;gBACH,MAAM,cAAc;gBACpB,IAAI,QAAQ;oBACV,MAAM,eAAe,QAAQ,SAAS,CAAC,CAAA,MAAO,IAAI,KAAK,KAAK;oBAC5D,MAAM,YAAY,KAAK,GAAG,CAAC,eAAe,GAAG;oBAC7C,IAAI,OAAO,CAAC,UAAU,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,QAAQ,EAAE;wBACtD,aAAa,OAAO,CAAC,UAAU,CAAC,KAAK;oBACvC;gBACF;gBACA;QACJ;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;;YAC5B,uBACC,8OAAC;gBAAM,WAAU;;oBACd;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAkB;;;;;;;;;;;;0BAInD,8OAAC;gBAAI,KAAK;gBAAa,WAAU;;kCAC/B,8OAAC;wBACC,MAAK;wBACL,SAAS,IAAM,CAAC,YAAY,UAAU,CAAC;wBACvC,WAAW;wBACX,UAAU;wBACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,wFACA,gEACA,oFACA,wBACA,YAAY,iCACZ,SAAS,oCACT,UAAU;wBAEZ,iBAAc;wBACd,iBAAe;wBACf,mBAAiB,QAAQ,GAAG,MAAM,MAAM,CAAC,GAAG;;0CAE5C,8OAAC;gCAAK,WAAU;0CACb,iBAAiB,eAAe,KAAK,GAAG;;;;;;0CAE3C,8OAAC;gCAAK,WAAU;0CACd,cAAA,8OAAC,oNAAA,CAAA,cAAW;oCACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,6DACA,UAAU;;;;;;;;;;;;;;;;;oBAMjB,wBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,MAAK;4BAAU,WAAU;sCAC1B,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;oCAEC,MAAK;oCACL,iBAAe,kBAAkB,OAAO,KAAK;oCAC7C,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0GACA,mDACA,OAAO,QAAQ,IAAI,iCACnB,kBAAkB,OAAO,KAAK,IAAI;oCAEpC,SAAS,IAAM,CAAC,OAAO,QAAQ,IAAI,aAAa,OAAO,KAAK;;sDAE5D,8OAAC;4CAAK,WAAU;sDAAkB,OAAO,KAAK;;;;;;wCAC7C,kBAAkB,OAAO,KAAK,kBAC7B,8OAAC;4CAAK,WAAU;sDACd,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;mCAdhB,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;YAwB5B,uBACC,8OAAC;gBAAE,WAAU;0BAAqC;;;;;;;;;;;;AAI1D", "debugId": null}}, {"offset": {"line": 645, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/marketing/multistep-form.tsx"], "sourcesContent": ["\"use client\";\nimport { useState } from \"react\";\nimport { motion, AnimatePresence } from \"framer-motion\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { ShimmerButton } from \"@/components/ui/shimmer-button\";\nimport ShinyButton from \"@/components/ui/shiny-button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Checkbox } from \"@/components/ui/checkbox\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { cn } from \"@/lib/utils\";\nimport { DropdownSelect, type DropdownOption } from \"@/components/ui/dropdown-select\";\n\ninterface ApplicationData {\n  // Personal Information\n  firstName: string;\n  lastName: string;\n  email: string;\n  phone: string;\n\n  // Professional Information\n  businessName: string;\n  services: string[];\n  experience: string;\n  certifications: string[];\n\n  // Location & Availability\n  serviceAreas: string[];\n  availability: string[];\n  travelRadius: string;\n\n  // Business Details\n  insurance: boolean;\n  license: string;\n  portfolio: string;\n  rates: string;\n\n  // Additional Information\n  motivation: string;\n  references: string;\n}\n\nconst serviceOptions = [\n  'Hair Styling', 'Hair Cutting', 'Hair Coloring', 'Blowouts',\n  'Makeup Application', 'Bridal Makeup', 'Special Event Makeup',\n  'Manicures', 'Pedicures', 'Nail Art', 'Gel Polish',\n  'Eyebrow Shaping', 'Eyebrow Threading', 'Eyebrow Tinting',\n  'Eyelash Extensions', 'Lash Lifts', 'Lash Tinting',\n  'Braiding', 'Box Braids', 'Cornrows', 'Protective Styles',\n  'Loc Maintenance', 'Loc Styling', 'Retwisting',\n  'Beard Trimming', 'Hot Towel Shaves', 'Men\\'s Grooming'\n];\n\nconst fadeInUp = {\n  hidden: { opacity: 0, y: 20 },\n  visible: { opacity: 1, y: 0, transition: { duration: 0.3 } },\n};\n\nconst contentVariants = {\n  hidden: { opacity: 0, x: 50 },\n  visible: { opacity: 1, x: 0, transition: { duration: 0.3 } },\n  exit: { opacity: 0, x: -50, transition: { duration: 0.2 } },\n};\n\nconst steps = [\n  { id: 1, name: \"Personal Information\", stepWord: \"Personal\", fields: [\"firstName\", \"lastName\", \"email\", \"phone\"] },\n  { id: 2, name: \"Professional Details\", stepWord: \"Professional\", fields: [\"businessName\", \"services\", \"experience\", \"certifications\"] },\n  { id: 3, name: \"Service Areas\", stepWord: \"Location\", fields: [\"serviceAreas\", \"availability\", \"travelRadius\"] },\n  { id: 4, name: \"Business Information\", stepWord: \"Business\", fields: [\"insurance\", \"license\", \"portfolio\", \"rates\"] },\n  { id: 5, name: \"Additional Details\", stepWord: \"Details\", fields: [\"motivation\", \"references\"] },\n  { id: 6, name: \"Review & Submit\", stepWord: \"Review\", fields: [] },\n];\n\nexport function MultiStepForm() {\n  const [currentStep, setCurrentStep] = useState(0);\n\n  // Experience options for dropdown\n  const experienceOptions: DropdownOption[] = [\n    { value: '0-1', label: 'Less than 1 year' },\n    { value: '1-3', label: '1-3 years' },\n    { value: '3-5', label: '3-5 years' },\n    { value: '5-10', label: '5-10 years' },\n    { value: '10+', label: '10+ years' }\n  ];\n\n  const [formData, setFormData] = useState<ApplicationData>({\n    firstName: \"\",\n    lastName: \"\",\n    email: \"\",\n    phone: \"\",\n    businessName: \"\",\n    services: [],\n    experience: \"\",\n    certifications: [],\n    serviceAreas: [],\n    availability: [],\n    travelRadius: \"\",\n    insurance: false,\n    license: \"\",\n    portfolio: \"\",\n    rates: \"\",\n    motivation: \"\",\n    references: \"\",\n  });\n\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitSuccess, setSubmitSuccess] = useState(false);\n\n  const next = () => setCurrentStep((prev) => (prev < steps.length - 1 ? prev + 1 : prev));\n  const prev = () => setCurrentStep((prev) => (prev > 0 ? prev - 1 : prev));\n\n  const updateFormData = (field: string, value: any) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n  };\n\n  const handleArrayToggle = (field: keyof ApplicationData, value: string) => {\n    const currentArray = formData[field] as string[];\n    const newArray = currentArray.includes(value)\n      ? currentArray.filter(item => item !== value)\n      : [...currentArray, value];\n    updateFormData(field, newArray);\n  };\n\n  // Check if step is valid for next button\n  const isStepValid = () => {\n    switch (currentStep) {\n      case 0:\n        return formData.firstName.trim() !== \"\" && formData.lastName.trim() !== \"\" && formData.email.trim() !== \"\";\n      case 1:\n        return formData.businessName.trim() !== \"\" && formData.services.length > 0;\n      case 2:\n        return formData.serviceAreas.length > 0;\n      case 3:\n        return formData.portfolio.trim() !== \"\";\n      case 4:\n        return formData.motivation.trim() !== \"\";\n      default:\n        return true;\n    }\n  };\n\n  const handleSubmit = async () => {\n    setIsSubmitting(true);\n    try {\n      // Submit to the exact same API endpoint as SOURCE\n      const response = await fetch('/api/professional-application', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          ...formData,\n          timestamp: new Date().toISOString()\n        }),\n      });\n\n      if (response.ok) {\n        setSubmitSuccess(true);\n      } else {\n        throw new Error('Submission failed');\n      }\n    } catch (error) {\n      // Fallback to localStorage (same as SOURCE)\n      const existingApplications = JSON.parse(localStorage.getItem('vierla-applications') || '[]');\n      existingApplications.push({\n        ...formData,\n        timestamp: new Date().toISOString(),\n        status: 'pending'\n      });\n      localStorage.setItem('vierla-applications', JSON.stringify(existingApplications));\n      setSubmitSuccess(true);\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  if (submitSuccess) {\n    return (\n      <Card className=\"w-full max-w-2xl mx-auto bg-white/10 backdrop-blur-md border border-white/20\">\n        <CardContent className=\"p-8 text-center\">\n          <h2 className=\"text-2xl font-bold mb-4 text-primary drop-shadow-lg\">Application Submitted!</h2>\n          <p className=\"text-vierla-text/80 mb-6 drop-shadow-sm\">\n            Thank you for your application. We'll review it and get back to you within 2-3 business days.\n          </p>\n          <Button asChild>\n            <a href=\"/\">Return to Homepage</a>\n          </Button>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"w-full max-w-4xl mx-auto\">\n\n      {/* Progress Indicator */}\n      <motion.div\n        className=\"mb-8\"\n        initial={{ opacity: 0, y: -20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ duration: 0.5 }}\n      >\n        <div className=\"flex justify-between mb-2\">\n          {steps.map((step, index) => (\n            <motion.div\n              key={index}\n              className=\"flex flex-col items-center\"\n              whileHover={{ scale: 1.1 }}\n            >\n              <motion.div\n                className={cn(\n                  \"w-5 h-5 rounded-full cursor-pointer transition-all duration-300 border-2\",\n                  index < currentStep\n                    ? \"ring-2\"\n                    : index === currentStep\n                      ? \"ring-4\"\n                      : \"opacity-50\",\n                )}\n                style={{\n                  backgroundColor: index <= currentStep ? 'var(--icon-accent)' : 'transparent',\n                  borderColor: 'var(--icon-accent)',\n                  ringColor: index <= currentStep ? 'var(--accent-hover-overlay)' : 'transparent'\n                }}\n                onClick={() => {\n                  // Only allow going back or to completed steps\n                  if (index <= currentStep) {\n                    setCurrentStep(index);\n                  }\n                }}\n                whileTap={{ scale: 0.95 }}\n              />\n              <motion.span\n                className={cn(\n                  \"text-xs mt-1.5 hidden sm:block drop-shadow-sm font-sans transition-colors duration-300\",\n                  index === currentStep ? \"font-medium\" : \"opacity-60\",\n                )}\n                style={{ color: 'var(--text-primary)' }}\n              >\n                {step.stepWord}\n              </motion.span>\n            </motion.div>\n          ))}\n        </div>\n        <div className=\"w-full h-1.5 rounded-full overflow-hidden mt-2\"\n             style={{ backgroundColor: 'var(--border-subtle)' }}>\n          <motion.div\n            className=\"h-full\"\n            style={{ backgroundColor: 'var(--icon-accent)' }}\n            initial={{ width: 0 }}\n            animate={{ width: `${(currentStep / (steps.length - 1)) * 100}%` }}\n            transition={{ duration: 0.3 }}\n          />\n        </div>\n      </motion.div>\n\n      <Card className=\"w-full shadow-2xl rounded-3xl\"\n            style={{\n              backgroundColor: 'var(--card-bg)',\n              borderColor: 'var(--border-subtle)',\n              boxShadow: 'var(--shadow-card)'\n            }}>\n        <CardHeader>\n          {/* Step Indicator - Top Right */}\n          <div className=\"flex justify-end mb-4\">\n            <p className=\"text-sm drop-shadow-sm font-sans px-4 py-2 rounded-lg\"\n               style={{\n                 color: 'var(--text-primary)',\n                 backgroundColor: 'var(--card-bg)',\n                 border: '1px solid var(--border-subtle)'\n               }}>\n              Step {currentStep + 1} of {steps.length}: {steps[currentStep].name}\n            </p>\n          </div>\n          <CardTitle className=\"drop-shadow-lg font-tai-heritage\" style={{ color: 'var(--text-primary)' }}>\n            {steps[currentStep].name}\n          </CardTitle>\n          <CardDescription className=\"drop-shadow-sm font-sans\" style={{ color: 'var(--text-secondary)' }}>\n            {currentStep === 0 && \"Let's start with your basic information\"}\n            {currentStep === 1 && \"Tell us about your professional background\"}\n            {currentStep === 2 && \"Where do you provide services?\"}\n            {currentStep === 3 && \"Business credentials and portfolio\"}\n            {currentStep === 4 && \"Help us understand your motivation\"}\n            {currentStep === 5 && \"Review your information before submitting\"}\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-6\">\n          <AnimatePresence mode=\"wait\">\n            <motion.div\n              key={currentStep}\n              initial={{ x: 300, opacity: 0 }}\n              animate={{ x: 0, opacity: 1 }}\n              exit={{ x: -300, opacity: 0 }}\n              transition={{ duration: 0.3 }}\n            >\n              {/* Step 1: Personal Information */}\n              {currentStep === 0 && (\n                <div className=\"space-y-4\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"firstName\" className=\"drop-shadow-sm\" style={{ color: 'var(--text-primary)' }}>First Name</Label>\n                      <Input\n                        id=\"firstName\"\n                        value={formData.firstName}\n                        onChange={(e) => updateFormData('firstName', e.target.value)}\n                        placeholder=\"Enter your first name\"\n                        className=\"theme-input\"\n                        required\n                      />\n                    </div>\n                    <div className=\"space-y-2\">\n                      <Label htmlFor=\"lastName\" className=\"drop-shadow-sm\" style={{ color: 'var(--text-primary)' }}>Last Name</Label>\n                      <Input\n                        id=\"lastName\"\n                        value={formData.lastName}\n                        onChange={(e) => updateFormData('lastName', e.target.value)}\n                        placeholder=\"Enter your last name\"\n                        className=\"theme-input\"\n                        required\n                      />\n                    </div>\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"email\" className=\"drop-shadow-sm\" style={{ color: 'var(--text-primary)' }}>Email Address</Label>\n                    <Input\n                      id=\"email\"\n                      type=\"email\"\n                      value={formData.email}\n                      onChange={(e) => updateFormData('email', e.target.value)}\n                      placeholder=\"Enter your email address\"\n                      className=\"theme-input\"\n                      required\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"phone\" className=\"drop-shadow-sm\" style={{ color: 'var(--text-primary)' }}>Phone Number</Label>\n                    <Input\n                      id=\"phone\"\n                      type=\"tel\"\n                      value={formData.phone}\n                      onChange={(e) => updateFormData('phone', e.target.value)}\n                      placeholder=\"Enter your phone number\"\n                      className=\"theme-input\"\n                      required\n                    />\n                  </div>\n                </div>\n              )}\n\n              {/* Step 2: Professional Details */}\n              {currentStep === 1 && (\n                <div className=\"space-y-4\">\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"businessName\" className=\"text-vierla-text drop-shadow-sm\">Business Name</Label>\n                    <Input\n                      id=\"businessName\"\n                      value={formData.businessName}\n                      onChange={(e) => updateFormData('businessName', e.target.value)}\n                      placeholder=\"Enter your business name\"\n                      className=\"text-vierla-text placeholder:text-[#F5F5DC] bg-white/10 border-white/20\"\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label className=\"text-vierla-text drop-shadow-sm\">Services Offered</Label>\n                    <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 max-h-48 overflow-y-auto\">\n                      {serviceOptions.map((service) => (\n                        <div key={service} className=\"flex items-center space-x-2 p-2 rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition-colors\">\n                          <Checkbox\n                            id={service}\n                            checked={formData.services.includes(service)}\n                            onCheckedChange={() => handleArrayToggle('services', service)}\n                          />\n                          <Label htmlFor={service} className=\"text-sm text-vierla-text drop-shadow-sm cursor-pointer\">{service}</Label>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                  <div className=\"space-y-2\">\n                    <DropdownSelect\n                      label=\"Years of Experience\"\n                      options={experienceOptions}\n                      value={formData.experience}\n                      onChange={(value) => updateFormData('experience', value)}\n                      placeholder=\"Select your experience level\"\n                      required\n                    />\n                  </div>\n                </div>\n              )}\n\n              {/* Step 3: Service Areas */}\n              {currentStep === 2 && (\n                <div className=\"space-y-4\">\n                  <div className=\"space-y-2\">\n                    <Label className=\"text-vierla-text drop-shadow-sm\">Service Areas</Label>\n                    <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3\">\n                      {['Toronto Downtown', 'North York', 'Scarborough', 'Etobicoke', 'Mississauga', 'Brampton', 'Ottawa Downtown', 'Kanata', 'Orleans', 'Nepean'].map((area) => (\n                        <div key={area} className=\"flex items-center space-x-2 p-2 rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition-colors\">\n                          <Checkbox\n                            id={area}\n                            checked={formData.serviceAreas.includes(area)}\n                            onCheckedChange={() => handleArrayToggle('serviceAreas', area)}\n                          />\n                          <Label htmlFor={area} className=\"text-sm text-vierla-text drop-shadow-sm cursor-pointer\">{area}</Label>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"travelRadius\" className=\"text-vierla-text drop-shadow-sm\">Travel Radius (km)</Label>\n                    <Input\n                      id=\"travelRadius\"\n                      value={formData.travelRadius}\n                      onChange={(e) => updateFormData('travelRadius', e.target.value)}\n                      placeholder=\"e.g., 25\"\n                      className=\"text-vierla-text placeholder:text-[#F5F5DC] bg-white/10 border-white/20\"\n                    />\n                  </div>\n                </div>\n              )}\n\n              {/* Step 4: Business Information */}\n              {currentStep === 3 && (\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center space-x-2\">\n                    <Checkbox\n                      id=\"insurance\"\n                      checked={formData.insurance}\n                      onCheckedChange={(checked) => updateFormData('insurance', checked)}\n                    />\n                    <Label htmlFor=\"insurance\" className=\"text-vierla-text drop-shadow-sm\">I have professional liability insurance</Label>\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"license\" className=\"text-vierla-text drop-shadow-sm\">License/Certification Numbers</Label>\n                    <Input\n                      id=\"license\"\n                      value={formData.license}\n                      onChange={(e) => updateFormData('license', e.target.value)}\n                      placeholder=\"Enter relevant license numbers\"\n                      className=\"text-vierla-text placeholder:text-[#F5F5DC] bg-white/10 border-white/20\"\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"portfolio\" className=\"text-vierla-text drop-shadow-sm\">Portfolio/Website URL</Label>\n                    <Input\n                      id=\"portfolio\"\n                      value={formData.portfolio}\n                      onChange={(e) => updateFormData('portfolio', e.target.value)}\n                      placeholder=\"https://your-portfolio.com\"\n                      className=\"text-vierla-text placeholder:text-[#F5F5DC] bg-white/10 border-white/20\"\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"rates\" className=\"text-vierla-text drop-shadow-sm\">Starting Rates</Label>\n                    <Textarea\n                      id=\"rates\"\n                      value={formData.rates}\n                      onChange={(e) => updateFormData('rates', e.target.value)}\n                      placeholder=\"Describe your pricing structure\"\n                      className=\"text-vierla-text placeholder:text-[#F5F5DC] bg-white/10 border-white/20\"\n                      rows={3}\n                    />\n                  </div>\n                </div>\n              )}\n\n              {/* Step 5: Additional Details */}\n              {currentStep === 4 && (\n                <div className=\"space-y-4\">\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"motivation\" className=\"text-vierla-text drop-shadow-sm\">Why do you want to join Vierla?</Label>\n                    <Textarea\n                      id=\"motivation\"\n                      value={formData.motivation}\n                      onChange={(e) => updateFormData('motivation', e.target.value)}\n                      placeholder=\"Tell us about your motivation and goals\"\n                      className=\"text-vierla-text placeholder:text-[#F5F5DC] bg-white/10 border-white/20\"\n                      rows={4}\n                    />\n                  </div>\n                  <div className=\"space-y-2\">\n                    <Label htmlFor=\"references\" className=\"text-vierla-text drop-shadow-sm\">References (Optional)</Label>\n                    <Textarea\n                      id=\"references\"\n                      value={formData.references}\n                      onChange={(e) => updateFormData('references', e.target.value)}\n                      placeholder=\"Professional references or client testimonials\"\n                      className=\"text-vierla-text placeholder:text-[#F5F5DC] bg-white/10 border-white/20\"\n                      rows={3}\n                    />\n                  </div>\n                </div>\n              )}\n\n              {/* Step 6: Review & Submit */}\n              {currentStep === 5 && (\n                <div className=\"space-y-4\">\n                  <h3 className=\"text-lg font-semibold text-vierla-text drop-shadow-lg\">Review Your Application</h3>\n                  <div className=\"space-y-2 text-sm text-vierla-text/90 drop-shadow-sm\">\n                    <p><strong>Name:</strong> {formData.firstName} {formData.lastName}</p>\n                    <p><strong>Email:</strong> {formData.email}</p>\n                    <p><strong>Phone:</strong> {formData.phone}</p>\n                    <p><strong>Business:</strong> {formData.businessName || 'Not specified'}</p>\n                    <p><strong>Services:</strong> {formData.services.join(', ') || 'None selected'}</p>\n                    <p><strong>Experience:</strong> {formData.experience || 'Not specified'}</p>\n                    <p><strong>Service Areas:</strong> {formData.serviceAreas.join(', ') || 'None selected'}</p>\n                    <p><strong>Insurance:</strong> {formData.insurance ? 'Yes' : 'No'}</p>\n                  </div>\n                  <div className=\"bg-white/10 p-4 rounded-lg border border-white/20\">\n                    <p className=\"text-sm text-vierla-text/70 drop-shadow-sm\">\n                      By submitting this application, you agree to our Terms of Service and Privacy Policy.\n                      We'll review your application and contact you within 2-3 business days.\n                    </p>\n                  </div>\n                </div>\n              )}\n            </motion.div>\n          </AnimatePresence>\n\n          <div className=\"flex justify-between pt-6 pb-4\">\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <button\n                type=\"button\"\n                onClick={prev}\n                disabled={currentStep === 0}\n                className=\"px-8 py-3 text-lg font-medium rounded-2xl bg-transparent border-2 border-[var(--master-brand-primary-light)] dark:border-[var(--master-brand-primary-dark)] text-[var(--master-brand-primary-light)] dark:text-[var(--master-brand-primary-dark)] hover:bg-[var(--master-brand-primary-light)] hover:dark:bg-[var(--master-brand-primary-dark)] hover:text-[var(--master-text-primary-light)] hover:dark:text-[var(--master-text-primary-dark)] transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed\"\n              >\n                Back\n              </button>\n            </motion.div>\n            <motion.div\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              {currentStep === steps.length - 1 ? (\n                <ShimmerButton\n                  type=\"button\"\n                  size=\"lg\"\n                  background=\"#B8956A\"\n                  shimmerColor=\"#E5D4A1\"\n                  className=\"px-8 py-3 text-lg font-medium text-[#2D2A26] rounded-2xl\"\n                  onClick={handleSubmit}\n                  disabled={isSubmitting}\n                >\n                  {isSubmitting ? \"Submitting...\" : \"Submit Application\"}\n                </ShimmerButton>\n              ) : (\n                <ShinyButton\n                  type=\"button\"\n                  onClick={next}\n                  disabled={!isStepValid()}\n                  size=\"lg\"\n                  className=\"px-8 py-3 text-lg font-medium rounded-2xl\"\n                >\n                  Next\n                </ShinyButton>\n              )}\n            </motion.div>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AAbA;;;;;;;;;;;;;;AA4CA,MAAM,iBAAiB;IACrB;IAAgB;IAAgB;IAAiB;IACjD;IAAsB;IAAiB;IACvC;IAAa;IAAa;IAAY;IACtC;IAAmB;IAAqB;IACxC;IAAsB;IAAc;IACpC;IAAY;IAAc;IAAY;IACtC;IAAmB;IAAe;IAClC;IAAkB;IAAoB;CACvC;AAED,MAAM,WAAW;IACf,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,SAAS;QAAE,SAAS;QAAG,GAAG;QAAG,YAAY;YAAE,UAAU;QAAI;IAAE;AAC7D;AAEA,MAAM,kBAAkB;IACtB,QAAQ;QAAE,SAAS;QAAG,GAAG;IAAG;IAC5B,SAAS;QAAE,SAAS;QAAG,GAAG;QAAG,YAAY;YAAE,UAAU;QAAI;IAAE;IAC3D,MAAM;QAAE,SAAS;QAAG,GAAG,CAAC;QAAI,YAAY;YAAE,UAAU;QAAI;IAAE;AAC5D;AAEA,MAAM,QAAQ;IACZ;QAAE,IAAI;QAAG,MAAM;QAAwB,UAAU;QAAY,QAAQ;YAAC;YAAa;YAAY;YAAS;SAAQ;IAAC;IACjH;QAAE,IAAI;QAAG,MAAM;QAAwB,UAAU;QAAgB,QAAQ;YAAC;YAAgB;YAAY;YAAc;SAAiB;IAAC;IACtI;QAAE,IAAI;QAAG,MAAM;QAAiB,UAAU;QAAY,QAAQ;YAAC;YAAgB;YAAgB;SAAe;IAAC;IAC/G;QAAE,IAAI;QAAG,MAAM;QAAwB,UAAU;QAAY,QAAQ;YAAC;YAAa;YAAW;YAAa;SAAQ;IAAC;IACpH;QAAE,IAAI;QAAG,MAAM;QAAsB,UAAU;QAAW,QAAQ;YAAC;YAAc;SAAa;IAAC;IAC/F;QAAE,IAAI;QAAG,MAAM;QAAmB,UAAU;QAAU,QAAQ,EAAE;IAAC;CAClE;AAEM,SAAS;IACd,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,kCAAkC;IAClC,MAAM,oBAAsC;QAC1C;YAAE,OAAO;YAAO,OAAO;QAAmB;QAC1C;YAAE,OAAO;YAAO,OAAO;QAAY;QACnC;YAAE,OAAO;YAAO,OAAO;QAAY;QACnC;YAAE,OAAO;YAAQ,OAAO;QAAa;QACrC;YAAE,OAAO;YAAO,OAAO;QAAY;KACpC;IAED,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;QACxD,WAAW;QACX,UAAU;QACV,OAAO;QACP,OAAO;QACP,cAAc;QACd,UAAU,EAAE;QACZ,YAAY;QACZ,gBAAgB,EAAE;QAClB,cAAc,EAAE;QAChB,cAAc,EAAE;QAChB,cAAc;QACd,WAAW;QACX,SAAS;QACT,WAAW;QACX,OAAO;QACP,YAAY;QACZ,YAAY;IACd;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,OAAO,IAAM,eAAe,CAAC,OAAU,OAAO,MAAM,MAAM,GAAG,IAAI,OAAO,IAAI;IAClF,MAAM,OAAO,IAAM,eAAe,CAAC,OAAU,OAAO,IAAI,OAAO,IAAI;IAEnE,MAAM,iBAAiB,CAAC,OAAe;QACrC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;IAClD;IAEA,MAAM,oBAAoB,CAAC,OAA8B;QACvD,MAAM,eAAe,QAAQ,CAAC,MAAM;QACpC,MAAM,WAAW,aAAa,QAAQ,CAAC,SACnC,aAAa,MAAM,CAAC,CAAA,OAAQ,SAAS,SACrC;eAAI;YAAc;SAAM;QAC5B,eAAe,OAAO;IACxB;IAEA,yCAAyC;IACzC,MAAM,cAAc;QAClB,OAAQ;YACN,KAAK;gBACH,OAAO,SAAS,SAAS,CAAC,IAAI,OAAO,MAAM,SAAS,QAAQ,CAAC,IAAI,OAAO,MAAM,SAAS,KAAK,CAAC,IAAI,OAAO;YAC1G,KAAK;gBACH,OAAO,SAAS,YAAY,CAAC,IAAI,OAAO,MAAM,SAAS,QAAQ,CAAC,MAAM,GAAG;YAC3E,KAAK;gBACH,OAAO,SAAS,YAAY,CAAC,MAAM,GAAG;YACxC,KAAK;gBACH,OAAO,SAAS,SAAS,CAAC,IAAI,OAAO;YACvC,KAAK;gBACH,OAAO,SAAS,UAAU,CAAC,IAAI,OAAO;YACxC;gBACE,OAAO;QACX;IACF;IAEA,MAAM,eAAe;QACnB,gBAAgB;QAChB,IAAI;YACF,kDAAkD;YAClD,MAAM,WAAW,MAAM,MAAM,iCAAiC;gBAC5D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,GAAG,QAAQ;oBACX,WAAW,IAAI,OAAO,WAAW;gBACnC;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,iBAAiB;YACnB,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,4CAA4C;YAC5C,MAAM,uBAAuB,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,0BAA0B;YACvF,qBAAqB,IAAI,CAAC;gBACxB,GAAG,QAAQ;gBACX,WAAW,IAAI,OAAO,WAAW;gBACjC,QAAQ;YACV;YACA,aAAa,OAAO,CAAC,uBAAuB,KAAK,SAAS,CAAC;YAC3D,iBAAiB;QACnB,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,IAAI,eAAe;QACjB,qBACE,8OAAC,yHAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,8OAAC,yHAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,8OAAC;wBAAG,WAAU;kCAAsD;;;;;;kCACpE,8OAAC;wBAAE,WAAU;kCAA0C;;;;;;kCAGvD,8OAAC,2HAAA,CAAA,SAAM;wBAAC,OAAO;kCACb,cAAA,8OAAC;4BAAE,MAAK;sCAAI;;;;;;;;;;;;;;;;;;;;;;IAKtB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAGb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;;kCAE5B,8OAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAI;;kDAEzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4EACA,QAAQ,cACJ,WACA,UAAU,cACR,WACA;wCAER,OAAO;4CACL,iBAAiB,SAAS,cAAc,uBAAuB;4CAC/D,aAAa;4CACb,WAAW,SAAS,cAAc,gCAAgC;wCACpE;wCACA,SAAS;4CACP,8CAA8C;4CAC9C,IAAI,SAAS,aAAa;gDACxB,eAAe;4CACjB;wCACF;wCACA,UAAU;4CAAE,OAAO;wCAAK;;;;;;kDAE1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;wCACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,0FACA,UAAU,cAAc,gBAAgB;wCAE1C,OAAO;4CAAE,OAAO;wCAAsB;kDAErC,KAAK,QAAQ;;;;;;;+BAjCX;;;;;;;;;;kCAsCX,8OAAC;wBAAI,WAAU;wBACV,OAAO;4BAAE,iBAAiB;wBAAuB;kCACpD,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,OAAO;gCAAE,iBAAiB;4BAAqB;4BAC/C,SAAS;gCAAE,OAAO;4BAAE;4BACpB,SAAS;gCAAE,OAAO,GAAG,AAAC,cAAc,CAAC,MAAM,MAAM,GAAG,CAAC,IAAK,IAAI,CAAC,CAAC;4BAAC;4BACjE,YAAY;gCAAE,UAAU;4BAAI;;;;;;;;;;;;;;;;;0BAKlC,8OAAC,yHAAA,CAAA,OAAI;gBAAC,WAAU;gBACV,OAAO;oBACL,iBAAiB;oBACjB,aAAa;oBACb,WAAW;gBACb;;kCACJ,8OAAC,yHAAA,CAAA,aAAU;;0CAET,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;oCACV,OAAO;wCACL,OAAO;wCACP,iBAAiB;wCACjB,QAAQ;oCACV;;wCAAG;wCACE,cAAc;wCAAE;wCAAK,MAAM,MAAM;wCAAC;wCAAG,KAAK,CAAC,YAAY,CAAC,IAAI;;;;;;;;;;;;0CAGtE,8OAAC,yHAAA,CAAA,YAAS;gCAAC,WAAU;gCAAmC,OAAO;oCAAE,OAAO;gCAAsB;0CAC3F,KAAK,CAAC,YAAY,CAAC,IAAI;;;;;;0CAE1B,8OAAC,yHAAA,CAAA,kBAAe;gCAAC,WAAU;gCAA2B,OAAO;oCAAE,OAAO;gCAAwB;;oCAC3F,gBAAgB,KAAK;oCACrB,gBAAgB,KAAK;oCACrB,gBAAgB,KAAK;oCACrB,gBAAgB,KAAK;oCACrB,gBAAgB,KAAK;oCACrB,gBAAgB,KAAK;;;;;;;;;;;;;kCAG1B,8OAAC,yHAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC,yLAAA,CAAA,kBAAe;gCAAC,MAAK;0CACpB,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,SAAS;wCAAE,GAAG;wCAAK,SAAS;oCAAE;oCAC9B,SAAS;wCAAE,GAAG;wCAAG,SAAS;oCAAE;oCAC5B,MAAM;wCAAE,GAAG,CAAC;wCAAK,SAAS;oCAAE;oCAC5B,YAAY;wCAAE,UAAU;oCAAI;;wCAG3B,gBAAgB,mBACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0HAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAY,WAAU;oEAAiB,OAAO;wEAAE,OAAO;oEAAsB;8EAAG;;;;;;8EAC/F,8OAAC,0HAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,OAAO,SAAS,SAAS;oEACzB,UAAU,CAAC,IAAM,eAAe,aAAa,EAAE,MAAM,CAAC,KAAK;oEAC3D,aAAY;oEACZ,WAAU;oEACV,QAAQ;;;;;;;;;;;;sEAGZ,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0HAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAW,WAAU;oEAAiB,OAAO;wEAAE,OAAO;oEAAsB;8EAAG;;;;;;8EAC9F,8OAAC,0HAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,OAAO,SAAS,QAAQ;oEACxB,UAAU,CAAC,IAAM,eAAe,YAAY,EAAE,MAAM,CAAC,KAAK;oEAC1D,aAAY;oEACZ,WAAU;oEACV,QAAQ;;;;;;;;;;;;;;;;;;8DAId,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0HAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAQ,WAAU;4DAAiB,OAAO;gEAAE,OAAO;4DAAsB;sEAAG;;;;;;sEAC3F,8OAAC,0HAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,KAAK;4DACrB,UAAU,CAAC,IAAM,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;4DACvD,aAAY;4DACZ,WAAU;4DACV,QAAQ;;;;;;;;;;;;8DAGZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0HAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAQ,WAAU;4DAAiB,OAAO;gEAAE,OAAO;4DAAsB;sEAAG;;;;;;sEAC3F,8OAAC,0HAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,KAAK;4DACrB,UAAU,CAAC,IAAM,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;4DACvD,aAAY;4DACZ,WAAU;4DACV,QAAQ;;;;;;;;;;;;;;;;;;wCAOf,gBAAgB,mBACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0HAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAe,WAAU;sEAAkC;;;;;;sEAC1E,8OAAC,0HAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO,SAAS,YAAY;4DAC5B,UAAU,CAAC,IAAM,eAAe,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DAC9D,aAAY;4DACZ,WAAU;;;;;;;;;;;;8DAGd,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0HAAA,CAAA,QAAK;4DAAC,WAAU;sEAAkC;;;;;;sEACnD,8OAAC;4DAAI,WAAU;sEACZ,eAAe,GAAG,CAAC,CAAC,wBACnB,8OAAC;oEAAkB,WAAU;;sFAC3B,8OAAC,6HAAA,CAAA,WAAQ;4EACP,IAAI;4EACJ,SAAS,SAAS,QAAQ,CAAC,QAAQ,CAAC;4EACpC,iBAAiB,IAAM,kBAAkB,YAAY;;;;;;sFAEvD,8OAAC,0HAAA,CAAA,QAAK;4EAAC,SAAS;4EAAS,WAAU;sFAA0D;;;;;;;mEANrF;;;;;;;;;;;;;;;;8DAWhB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,uIAAA,CAAA,iBAAc;wDACb,OAAM;wDACN,SAAS;wDACT,OAAO,SAAS,UAAU;wDAC1B,UAAU,CAAC,QAAU,eAAe,cAAc;wDAClD,aAAY;wDACZ,QAAQ;;;;;;;;;;;;;;;;;wCAOf,gBAAgB,mBACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0HAAA,CAAA,QAAK;4DAAC,WAAU;sEAAkC;;;;;;sEACnD,8OAAC;4DAAI,WAAU;sEACZ;gEAAC;gEAAoB;gEAAc;gEAAe;gEAAa;gEAAe;gEAAY;gEAAmB;gEAAU;gEAAW;6DAAS,CAAC,GAAG,CAAC,CAAC,qBAChJ,8OAAC;oEAAe,WAAU;;sFACxB,8OAAC,6HAAA,CAAA,WAAQ;4EACP,IAAI;4EACJ,SAAS,SAAS,YAAY,CAAC,QAAQ,CAAC;4EACxC,iBAAiB,IAAM,kBAAkB,gBAAgB;;;;;;sFAE3D,8OAAC,0HAAA,CAAA,QAAK;4EAAC,SAAS;4EAAM,WAAU;sFAA0D;;;;;;;mEANlF;;;;;;;;;;;;;;;;8DAWhB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0HAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAe,WAAU;sEAAkC;;;;;;sEAC1E,8OAAC,0HAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO,SAAS,YAAY;4DAC5B,UAAU,CAAC,IAAM,eAAe,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DAC9D,aAAY;4DACZ,WAAU;;;;;;;;;;;;;;;;;;wCAOjB,gBAAgB,mBACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,6HAAA,CAAA,WAAQ;4DACP,IAAG;4DACH,SAAS,SAAS,SAAS;4DAC3B,iBAAiB,CAAC,UAAY,eAAe,aAAa;;;;;;sEAE5D,8OAAC,0HAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAU;sEAAkC;;;;;;;;;;;;8DAEzE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0HAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAU,WAAU;sEAAkC;;;;;;sEACrE,8OAAC,0HAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO,SAAS,OAAO;4DACvB,UAAU,CAAC,IAAM,eAAe,WAAW,EAAE,MAAM,CAAC,KAAK;4DACzD,aAAY;4DACZ,WAAU;;;;;;;;;;;;8DAGd,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0HAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAY,WAAU;sEAAkC;;;;;;sEACvE,8OAAC,0HAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,OAAO,SAAS,SAAS;4DACzB,UAAU,CAAC,IAAM,eAAe,aAAa,EAAE,MAAM,CAAC,KAAK;4DAC3D,aAAY;4DACZ,WAAU;;;;;;;;;;;;8DAGd,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0HAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAQ,WAAU;sEAAkC;;;;;;sEACnE,8OAAC,6HAAA,CAAA,WAAQ;4DACP,IAAG;4DACH,OAAO,SAAS,KAAK;4DACrB,UAAU,CAAC,IAAM,eAAe,SAAS,EAAE,MAAM,CAAC,KAAK;4DACvD,aAAY;4DACZ,WAAU;4DACV,MAAM;;;;;;;;;;;;;;;;;;wCAOb,gBAAgB,mBACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0HAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAa,WAAU;sEAAkC;;;;;;sEACxE,8OAAC,6HAAA,CAAA,WAAQ;4DACP,IAAG;4DACH,OAAO,SAAS,UAAU;4DAC1B,UAAU,CAAC,IAAM,eAAe,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC5D,aAAY;4DACZ,WAAU;4DACV,MAAM;;;;;;;;;;;;8DAGV,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0HAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAa,WAAU;sEAAkC;;;;;;sEACxE,8OAAC,6HAAA,CAAA,WAAQ;4DACP,IAAG;4DACH,OAAO,SAAS,UAAU;4DAC1B,UAAU,CAAC,IAAM,eAAe,cAAc,EAAE,MAAM,CAAC,KAAK;4DAC5D,aAAY;4DACZ,WAAU;4DACV,MAAM;;;;;;;;;;;;;;;;;;wCAOb,gBAAgB,mBACf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAwD;;;;;;8DACtE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EAAE,8OAAC;8EAAO;;;;;;gEAAc;gEAAE,SAAS,SAAS;gEAAC;gEAAE,SAAS,QAAQ;;;;;;;sEACjE,8OAAC;;8EAAE,8OAAC;8EAAO;;;;;;gEAAe;gEAAE,SAAS,KAAK;;;;;;;sEAC1C,8OAAC;;8EAAE,8OAAC;8EAAO;;;;;;gEAAe;gEAAE,SAAS,KAAK;;;;;;;sEAC1C,8OAAC;;8EAAE,8OAAC;8EAAO;;;;;;gEAAkB;gEAAE,SAAS,YAAY,IAAI;;;;;;;sEACxD,8OAAC;;8EAAE,8OAAC;8EAAO;;;;;;gEAAkB;gEAAE,SAAS,QAAQ,CAAC,IAAI,CAAC,SAAS;;;;;;;sEAC/D,8OAAC;;8EAAE,8OAAC;8EAAO;;;;;;gEAAoB;gEAAE,SAAS,UAAU,IAAI;;;;;;;sEACxD,8OAAC;;8EAAE,8OAAC;8EAAO;;;;;;gEAAuB;gEAAE,SAAS,YAAY,CAAC,IAAI,CAAC,SAAS;;;;;;;sEACxE,8OAAC;;8EAAE,8OAAC;8EAAO;;;;;;gEAAmB;gEAAE,SAAS,SAAS,GAAG,QAAQ;;;;;;;;;;;;;8DAE/D,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;kEAA6C;;;;;;;;;;;;;;;;;;mCA5N3D;;;;;;;;;;0CAsOT,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDAExB,cAAA,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU,gBAAgB;4CAC1B,WAAU;sDACX;;;;;;;;;;;kDAIH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;kDAEvB,gBAAgB,MAAM,MAAM,GAAG,kBAC9B,8OAAC,sIAAA,CAAA,gBAAa;4CACZ,MAAK;4CACL,MAAK;4CACL,YAAW;4CACX,cAAa;4CACb,WAAU;4CACV,SAAS;4CACT,UAAU;sDAET,eAAe,kBAAkB;;;;;iEAGpC,8OAAC,oIAAA,CAAA,UAAW;4CACV,MAAK;4CACL,SAAS;4CACT,UAAU,CAAC;4CACX,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}]}