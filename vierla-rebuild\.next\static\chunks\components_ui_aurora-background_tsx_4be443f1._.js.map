{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx"], "sourcesContent": ["\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport React, { memo, type ReactNode } from \"react\";\n\ninterface AuroraBackgroundProps extends React.HTMLProps<HTMLDivElement> {\n  children: ReactNode;\n  showRadialGradient?: boolean;\n}\n\n// Full page wrapper version\nexport const AuroraBackground = ({\n  className,\n  children,\n  showRadialGradient = true,\n  ...props\n}: AuroraBackgroundProps) => {\n  return (\n    <div\n      className={cn(\n        \"relative flex flex-col h-[100vh] items-center justify-center text-slate-950 transition-colors duration-300\",\n        className\n      )}\n      {...props}\n    >\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div\n          className={cn(\n            `\n            [--light-bg:#F4F1E8]\n            [--dark-bg:#2D2A26]\n            [--light-aurora:repeating-linear-gradient(100deg,#364035_10%,#8B9A8C_15%,#F0E6D9_20%,#7C9A85_25%,#364035_30%)]\n            [--dark-aurora:repeating-linear-gradient(100deg,#B8956A_10%,#4B3D6B_15%,#B8956A_20%,#4B3D6B_25%,#B8956A_30%)]\n            [--light-stripes:repeating-linear-gradient(100deg,var(--light-bg)_0%,var(--light-bg)_7%,transparent_10%,transparent_12%,var(--light-bg)_16%)]\n            [--dark-stripes:repeating-linear-gradient(100deg,var(--dark-bg)_0%,var(--dark-bg)_7%,transparent_10%,transparent_12%,var(--dark-bg)_16%)]\n            [background-image:var(--light-stripes),var(--light-aurora)]\n            dark:[background-image:var(--dark-stripes),var(--dark-aurora)]\n            [background-size:300%,_200%]\n            [background-position:50%_50%,50%_50%]\n            filter blur-[10px]\n            after:content-[\"\"] after:absolute after:inset-0 after:[background-image:var(--light-stripes),var(--light-aurora)]\n            after:dark:[background-image:var(--dark-stripes),var(--dark-aurora)]\n            after:[background-size:200%,_100%]\n            after:animate-aurora after:[background-attachment:fixed] after:mix-blend-difference\n            pointer-events-none\n            absolute -inset-[10px] opacity-50 will-change-transform`,\n            showRadialGradient && `[mask-image:radial-gradient(ellipse_at_100%_0%,black_10%,transparent_70%)]`\n          )}\n        ></div>\n      </div>\n      {children}\n    </div>\n  );\n};\n\n// Background-only version for existing layouts\nexport const AuroraBackgroundLayer = memo(\n  ({\n    className,\n    showRadialGradient = true,\n  }: {\n    className?: string;\n    showRadialGradient?: boolean;\n  }) => {\n    return (\n      <div className=\"fixed inset-0 -z-10 overflow-hidden\">\n        <div\n          className={cn(\n            `\n            [--light-bg:#F4F1E8]\n            [--dark-bg:#2D2A26]\n            [--light-aurora:repeating-linear-gradient(100deg,#7C9A85_10%,#8B9A8C_15%,#E8D5D5_20%,#7C9A85_25%,#8B9A8C_30%)]\n            [--dark-aurora:repeating-linear-gradient(100deg,#B8956A_10%,#4B3D6B_15%,#B8956A_20%,#4B3D6B_25%,#B8956A_30%)]\n            [--light-stripes:repeating-linear-gradient(100deg,var(--light-bg)_0%,var(--light-bg)_7%,transparent_10%,transparent_12%,var(--light-bg)_16%)]\n            [--dark-stripes:repeating-linear-gradient(100deg,var(--dark-bg)_0%,var(--dark-bg)_7%,transparent_10%,transparent_12%,var(--dark-bg)_16%)]\n            [background-image:var(--light-stripes),var(--light-aurora)]\n            dark:[background-image:var(--dark-stripes),var(--dark-aurora)]\n            [background-size:300%,_200%]\n            [background-position:50%_50%,50%_50%]\n            filter blur-[10px]\n            after:content-[\"\"] after:absolute after:inset-0 after:[background-image:var(--light-stripes),var(--light-aurora)]\n            after:dark:[background-image:var(--dark-stripes),var(--dark-aurora)]\n            after:[background-size:200%,_100%]\n            after:animate-aurora after:[background-attachment:fixed] after:mix-blend-difference\n            pointer-events-none\n            absolute -inset-[10px] opacity-50 will-change-transform`,\n            showRadialGradient && `[mask-image:radial-gradient(ellipse_at_100%_0%,black_10%,transparent_70%)]`,\n            className\n          )}\n        ></div>\n      </div>\n    );\n  }\n);\n\nAuroraBackgroundLayer.displayName = \"AuroraBackgroundLayer\";"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAWO,MAAM,mBAAmB;QAAC,EAC/B,SAAS,EACT,QAAQ,EACR,qBAAqB,IAAI,EACzB,GAAG,OACmB;IACtB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8GACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACT,00CAkBD,sBAAuB;;;;;;;;;;;YAI5B;;;;;;;AAGP;KA1Ca;AA6CN,MAAM,sCAAwB,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,QACtC;QAAC,EACC,SAAS,EACT,qBAAqB,IAAI,EAI1B;IACC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACT,00CAkBD,sBAAuB,8EACvB;;;;;;;;;;;AAKV;;AAGF,sBAAsB,WAAW,GAAG", "debugId": null}}]}