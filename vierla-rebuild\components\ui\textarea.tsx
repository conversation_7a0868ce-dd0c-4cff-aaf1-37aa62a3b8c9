import * as React from "react"

import { cn } from "@/lib/utils"

const Textarea = React.forwardRef<
  HTMLTextAreaElement,
  React.ComponentProps<"textarea">
>(({ className, ...props }, ref) => {
  return (
    <textarea
      className={cn(
        // Updated to use new Vierla color specifications
        "flex min-h-[80px] w-full rounded-md border transition-all duration-200",
        "bg-[var(--master-input-bg-light)] dark:bg-[var(--master-input-bg-dark)]",
        "border-[var(--master-input-border)] text-[var(--master-text-primary-light)] dark:text-[var(--master-text-primary-dark)]",
        "px-3 py-2 text-base font-[var(--font-family-body)]",
        "placeholder:text-[var(--master-text-secondary-light)] dark:placeholder:text-[var(--master-text-secondary-dark)]",
        "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[var(--master-input-border-focus)] focus-visible:border-[var(--master-input-border-focus)]",
        "disabled:cursor-not-allowed disabled:opacity-50",
        "md:text-sm",
        className
      )}
      ref={ref}
      {...props}
    />
  )
})
Textarea.displayName = "Textarea"

export { Textarea }
