import { AuroraBackgroundLayer } from "@/components/ui/aurora-background";
import { GoldenGlowingCardContainer } from "@/components/ui/golden-glowing-card-container";
import { BentoCard, BentoGrid } from "@/components/ui/bento-grid";
import ShinyButton from "@/components/ui/shiny-button";
import { TextShimmer } from "@/components/ui/text-shimmer";
import { WordPullUp } from "@/components/ui/word-pull-up";
import { MorphingText } from "@/components/ui/morphing-text";
import { LayoutTemplate, Search, Calendar, Sparkles, Smartphone, Clock, Home as HomeIcon, Star, Shield, Scissors, Palette, Sparkle, Flower2, Brush, Heart, Waves, Layers, Eye, Zap } from "lucide-react";
import Link from "next/link";

export default function Home() {
  return (
    <div className="page-home min-h-screen relative overflow-hidden">
      <AuroraBackgroundLayer />

      {/* Hero Section */}
      <section className="relative z-10 w-full px-4 sm:px-6 lg:px-8 py-12 sm:py-16 lg:py-20 pt-28 sm:pt-32 lg:pt-36">
        <div className="text-center max-w-7xl mx-auto">
          {/* Text Shimmer for main heading */}
          <div className="mb-6 sm:mb-8">
            <TextShimmer
              as="h1"
              duration={3}
              className="hero-title text-4xl sm:text-5xl md:text-6xl lg:text-7xl xl:text-8xl font-black leading-tight sm:leading-none text-[#2D2A26] dark:text-[#F4F1E8] drop-shadow-lg font-sans"
            >
              SELF-CARE, SIMPLIFIED
            </TextShimmer>
          </div>

          {/* Text Shimmer for description */}
          <div className="mb-6 sm:mb-8">
            <WordPullUp
              as="p"
              words="The ultimate marketplace connecting you with top beauty professionals, and the all-in-one tool for providers to manage and grow their business."
              className="hero-subtitle text-lg sm:text-xl md:text-2xl text-[#2D2A26] dark:text-[#A9A299] leading-relaxed font-light max-w-4xl mx-auto drop-shadow-sm font-inter px-4"
              delay={0.5}
            />
          </div>

          {/* Morphing text effect for launching soon - theme-aware and responsive */}
          <div className="mb-8 sm:mb-12 py-2 sm:py-4">
            <MorphingText
              texts={[
                "Launching Soon",
                "Toronto",
                "Ottawa",
                "Montreal",
                "Vancouver",
                "Calgary",
                "Edmonton",
                "Winnipeg",
                
              ]}
              className="text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold drop-shadow-lg text-[#2D2A26] dark:text-[#F4F1E8]"
            />
          </div>


          {/* Dual Call-to-Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center items-center mb-12 sm:mb-16 px-4">
            {/* Commented out Find Your Perfect Stylist button */}
            {/* <Link
              href="/customer-app"
              className="group flex items-center px-8 py-4 rounded-full font-medium transition-all duration-300 text-lg hover:scale-105 min-w-[280px] justify-center bg-primary text-primary-foreground"
            >
              <Search className="mr-3 w-6 h-6" />
              Find Your Perfect Stylist
              <Sparkles className="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" />
            </Link> */}

            <Link href="/providers" className="w-full sm:w-auto">
              <ShinyButton size="lg" className="group gap-2 w-full sm:w-auto min-w-[280px] sm:min-w-[320px] justify-center text-white dark:text-[var(--master-text-primary-dark)]">
                <LayoutTemplate className="w-5 h-5 md:w-6 md:h-6 flex-shrink-0" />
                <span className="flex-1 px-2 sm:px-4 text-base sm:text-lg">Grow Your Business</span>
                <svg className="w-4 h-4 md:w-5 md:h-5 group-hover:translate-x-1 transition-transform flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </ShinyButton>
            </Link>
          </div>
        </div>
      </section>

      {/* How It Works Section - Expanded */}
      <section className="relative z-10 py-8 sm:py-10 lg:py-12 border-t border-white/20">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12 sm:mb-16">
            <h2 className="text-3xl sm:text-4xl lg:text-5xl font-black text-[#2D2A26] dark:text-[#F4F1E8] mb-4 sm:mb-6 drop-shadow-lg font-jost">
              How Vierla Works
            </h2>
            <p className="text-lg sm:text-xl text-warm-beige max-w-4xl mx-auto drop-shadow-sm mb-6 sm:mb-8 font-sans px-4">
              Experience beauty services like never before. Our platform connects you with verified professionals who deliver exceptional results, whether at your location or their studio.
            </p>
            <div className="flex justify-center items-center px-4">
              <Link href="/contact" className="w-full sm:w-auto max-w-xs">
                <ShinyButton size="md" className="px-6 sm:px-8 py-3 text-base sm:text-lg font-semibold w-full sm:w-auto text-white dark:text-[var(--master-text-primary-dark)]">
                  Request a Demo
                </ShinyButton>
              </Link>
            </div>
          </div>

          {/* Main Process Steps - Responsive Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2 gap-4 sm:gap-6 lg:gap-8 max-w-6xl mx-auto mb-12 sm:mb-16">
            {/* 1. Discover */}
            <GoldenGlowingCardContainer>
              <div className="text-center p-4 sm:p-6">
                <div className="w-16 h-16 sm:w-20 sm:h-20 mx-auto mb-4 sm:mb-6 rounded-full flex items-center justify-center bg-gradient-to-br from-sage/20 to-sage/10 border-2 border-sage/30">
                  <Search className="w-8 h-8 sm:w-10 sm:h-10 text-sage" />
                </div>
                <h3 className="text-xl sm:text-2xl font-bold text-light-off-white mb-3 sm:mb-4 drop-shadow-lg font-tai-heritage">1. Discover</h3>
                <p className="text-sm sm:text-base text-warm-beige leading-relaxed mb-3 sm:mb-4 drop-shadow-md font-sans">
                  Browse our curated network of verified beauty professionals. Filter by service type, location, availability, and ratings.
                </p>
                <ul className="text-xs sm:text-sm text-warm-beige/80 text-left space-y-1 font-sans">
                  <li>• 500+ verified professionals</li>
                  <li>• Real customer reviews</li>
                  <li>• Portfolio galleries</li>
                  <li>• Instant availability</li>
                </ul>
              </div>
            </GoldenGlowingCardContainer>

            {/* 2. Book */}
            <GoldenGlowingCardContainer>
              <div className="text-center p-4 sm:p-6">
                <div className="w-16 h-16 sm:w-20 sm:h-20 mx-auto mb-4 sm:mb-6 rounded-full flex items-center justify-center bg-gradient-to-br from-sage/20 to-sage/10 border-2 border-sage/30">
                  <Calendar className="w-8 h-8 sm:w-10 sm:h-10 text-sage" />
                </div>
                <h3 className="text-xl sm:text-2xl font-bold text-light-off-white mb-3 sm:mb-4 drop-shadow-lg font-tai-heritage">2. Book</h3>
                <p className="text-sm sm:text-base text-warm-beige leading-relaxed mb-3 sm:mb-4 drop-shadow-md font-sans">
                  Select your preferred service, date, and time. Choose between mobile service or studio visit based on your preference.
                </p>
                <ul className="text-xs sm:text-sm text-warm-beige/80 text-left space-y-1 font-sans">
                  <li>• Real-time scheduling</li>
                  <li>• Mobile or studio options</li>
                  <li>• Instant confirmation</li>
                  <li>• Easy rescheduling</li>
                </ul>
              </div>
            </GoldenGlowingCardContainer>

            {/* 3. Pay */}
            <GoldenGlowingCardContainer>
              <div className="text-center p-4 sm:p-6">
                <div className="w-16 h-16 sm:w-20 sm:h-20 mx-auto mb-4 sm:mb-6 rounded-full flex items-center justify-center bg-gradient-to-br from-sage/20 to-sage/10 border-2 border-sage/30">
                  <svg className="w-8 h-8 sm:w-10 sm:h-10 text-sage" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                  </svg>
                </div>
                <h3 className="text-xl sm:text-2xl font-bold text-light-off-white mb-3 sm:mb-4 drop-shadow-lg font-tai-heritage">3. Secure Payment</h3>
                <p className="text-sm sm:text-base text-warm-beige leading-relaxed mb-3 sm:mb-4 drop-shadow-md font-sans">
                  Complete your booking with our secure payment system. Multiple payment options with full protection and easy refunds.
                </p>
                <ul className="text-xs sm:text-sm text-warm-beige/80 text-left space-y-1 font-sans">
                  <li>• Bank-level security</li>
                  <li>• Multiple payment methods</li>
                  <li>• Transparent pricing</li>
                  <li>• Money-back guarantee</li>
                </ul>
              </div>
            </GoldenGlowingCardContainer>

            {/* 4. Relax */}
            <GoldenGlowingCardContainer>
              <div className="text-center p-4 sm:p-6">
                <div className="w-16 h-16 sm:w-20 sm:h-20 mx-auto mb-4 sm:mb-6 rounded-full flex items-center justify-center bg-gradient-to-br from-sage/20 to-sage/10 border-2 border-sage/30">
                  <Sparkles className="w-8 h-8 sm:w-10 sm:h-10 text-sage" />
                </div>
                <h3 className="text-xl sm:text-2xl font-bold text-light-off-white mb-3 sm:mb-4 drop-shadow-lg font-tai-heritage">4. Enjoy</h3>
                <p className="text-sm sm:text-base text-warm-beige leading-relaxed mb-3 sm:mb-4 drop-shadow-md font-sans">
                  Relax and enjoy your premium beauty service. Our professionals arrive with everything needed for an exceptional experience.
                </p>
                <ul className="text-xs sm:text-sm text-warm-beige/80 text-left space-y-1 font-sans">
                  <li>• Professional equipment</li>
                  <li>• Premium products</li>
                  <li>• Personalized service</li>
                  <li>• Follow-up care tips</li>
                </ul>
              </div>
            </GoldenGlowingCardContainer>
          </div>

          {/* Additional Benefits */}
          <div className="max-w-6xl mx-auto">
            <GoldenGlowingCardContainer>
              <div className="p-4 sm:p-6 lg:p-8 text-center">
                <h3 className="text-2xl sm:text-3xl font-bold text-[#2D2A26] dark:text-[#F4F1E8] mb-4 sm:mb-6 drop-shadow-lg font-jost">Your Trusted Partner in Self-Care</h3>
                <p className="text-lg text-[#2D2A26] dark:text-[#A9A299] max-w-3xl mx-auto mb-8 drop-shadow-sm font-inter">
                  We understand that finding the right beauty professional can be challenging. That's why we've created a platform where trust, quality, and convenience come together seamlessly.
                </p>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
                  <div>
                    <div className="w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-3 sm:mb-4 rounded-full flex items-center justify-center bg-gradient-to-br from-sage/20 to-sage/10 border border-sage/30">
                      <Shield className="w-6 h-6 sm:w-8 sm:h-8 text-sage" />
                    </div>
                    <h4 className="text-lg sm:text-xl font-semibold text-light-off-white mb-2 font-tai-heritage">Verified Professionals</h4>
                    <p className="text-sm sm:text-base text-warm-beige font-sans">Every professional undergoes thorough background checks, license verification, and insurance validation before joining our platform.</p>
                  </div>
                  <div>
                    <div className="w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-3 sm:mb-4 rounded-full flex items-center justify-center bg-gradient-to-br from-sage/20 to-sage/10 border border-sage/30">
                      <svg className="w-6 h-6 sm:w-8 sm:h-8 text-sage" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <h4 className="text-lg sm:text-xl font-semibold text-light-off-white mb-2 font-tai-heritage">Flexible Scheduling</h4>
                    <p className="text-sm sm:text-base text-warm-beige font-sans">Book services that fit your schedule, with same-day availability and easy rescheduling options that respect your time.</p>
                  </div>
                  <div>
                    <div className="w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-3 sm:mb-4 rounded-full flex items-center justify-center bg-gradient-to-br from-sage/20 to-sage/10 border border-sage/30">
                      <Star className="w-6 h-6 sm:w-8 sm:h-8 text-sage" />
                    </div>
                    <h4 className="text-lg sm:text-xl font-semibold text-light-off-white mb-2 font-tai-heritage">Satisfaction Guaranteed</h4>
                    <p className="text-sm sm:text-base text-warm-beige font-sans">Your satisfaction is our priority. We stand behind every service with our 100% satisfaction guarantee and dedicated support.</p>
                  </div>
                </div>
              </div>
            </GoldenGlowingCardContainer>
          </div>
        </div>
      </section>



      {/* Featured Services Section */}
      <section className="relative z-10 py-12 border-t border-white/20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-5xl font-black text-[#2D2A26] dark:text-[#F4F1E8] mb-4 drop-shadow-lg font-jost">PREMIUM BEAUTY SERVICES</h2>
            <h3 className="text-2xl font-medium text-[#2D2A26] dark:text-[#A9A299] mb-6 drop-shadow-sm font-inter">
              From trusted barbers to master braiders, Vierla connects you with vetted specialists who understand your unique needs. We make it simple to find the right professional you can trust, again and again.
            </h3>
            <div className="flex flex-wrap justify-center gap-4 text-sm text-warm-beige/80">
              <span className="px-3 py-1 bg-sage/10 rounded-full border border-sage/20 flex items-center gap-2 font-sans">
                <Clock className="w-4 h-4" /> Same-day availability
              </span>
              <span className="px-3 py-1 bg-sage/10 rounded-full border border-sage/20 flex items-center gap-2 font-sans">
                <HomeIcon className="w-4 h-4" /> Mobile & studio options
              </span>
              <span className="px-3 py-1 bg-sage/10 rounded-full border border-sage/20 flex items-center gap-2 font-sans">
                <Star className="w-4 h-4" /> 4.9+ average rating
              </span>
              <span className="px-3 py-1 bg-sage/10 rounded-full border border-sage/20 flex items-center gap-2 font-sans">
                <Shield className="w-4 h-4" /> Fully insured professionals
              </span>
            </div>
          </div>

          {/* Bento Grid for Premium Beauty Services - 10 Categories */}
          <BentoGrid className="lg:grid-rows-5 max-w-7xl mx-auto min-h-[1200px]">
            {[
              {
                Icon: Scissors,
                name: "Master Barbers",
                description: "Precision cuts, expert fades, and luxury hot towel shaves for the modern gentleman.",
                href: "#",
                cta: "Learn More",
                className: "lg:row-start-1 lg:row-end-3 lg:col-start-1 lg:col-end-2",
              },
              {
                Icon: Palette,
                name: "Glam Makeup",
                description: "Flawless, red-carpet ready looks for any occasion, from weddings to special events.",
                href: "#",
                cta: "Learn More",
                className: "lg:row-start-1 lg:row-end-2 lg:col-start-2 lg:col-end-4",
              },
              {
                Icon: Sparkle,
                name: "Hair Salons",
                description: "Complete hair transformations including color, luxury blowouts, and keratin treatments.",
                href: "#",
                cta: "Learn More",
                className: "lg:row-start-3 lg:row-end-4 lg:col-start-1 lg:col-end-2",
              },
              {
                Icon: Waves,
                name: "Loc Specialists",
                description: "Expert loc maintenance, precision retwisting, creative styling, and restoration.",
                href: "#",
                cta: "Learn More",
                className: "lg:row-start-2 lg:row-end-3 lg:col-start-2 lg:col-end-3",
              },
              {
                Icon: Layers,
                name: "Braid Artists",
                description: "Intricate cornrows, designer box braids, and custom protective styling by expert artists.",
                href: "#",
                cta: "Learn More",
                className: "lg:row-start-2 lg:row-end-4 lg:col-start-3 lg:col-end-4",
              },
              {
                Icon: Flower2,
                name: "Nail Studios",
                description: "Manicure and pedicure perfection, featuring luxury spa treatments and custom nail art.",
                href: "#",
                cta: "Learn More",
                className: "lg:row-start-4 lg:row-end-5 lg:col-start-1 lg:col-end-2",
              },
              {
                Icon: Brush,
                name: "Skincare Experts",
                description: "Rejuvenating, clinical-grade treatments like deep-cleansing facials and microneedling.",
                href: "#",
                cta: "Learn More",
                className: "lg:row-start-3 lg:row-end-5 lg:col-start-2 lg:col-end-3",
              },
              {
                Icon: Heart,
                name: "Henna",
                description: "Traditional henna artistry, bridal designs, and custom body art patterns for special occasions.",
                href: "#",
                cta: "Learn More",
                className: "lg:row-start-4 lg:row-end-5 lg:col-start-3 lg:col-end-4",
              },
              {
                Icon: Eye,
                name: "Eyebrows",
                description: "Expert eyebrow shaping, threading, tinting, and microblading for perfectly sculpted brows.",
                href: "#",
                cta: "Learn More",
                className: "lg:row-start-5 lg:row-end-6 lg:col-start-1 lg:col-end-2",
              },
              {
                Icon: Zap,
                name: "Eyelashes",
                description: "Lash extensions, lifts, and tinting for dramatic, long-lasting beautiful lashes.",
                href: "#",
                cta: "Learn More",
                className: "lg:row-start-5 lg:row-end-6 lg:col-start-2 lg:col-end-4",
              },
            ].map((service) => (
              <BentoCard key={service.name} {...service} />
            ))}
          </BentoGrid>
        </div>
      </section>

      {/* Coming Soon to Mobile Apps Section */}
      <section className="relative z-10 py-12 border-t border-white/20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <GoldenGlowingCardContainer>
              <div className="text-center pt-4 pb-2">
                <div className="w-20 h-20 mx-auto mb-6 rounded-full flex items-center justify-center bg-sage/20 border-2 border-sage/30">
                  <Smartphone className="w-10 h-10 text-sage" />
                </div>
                <h2 className="text-3xl md:text-4xl font-black text-light-off-white mb-4 drop-shadow-lg font-tai-heritage">
                  Coming Soon to Mobile
                </h2>
                <p className="text-lg md:text-xl text-warm-beige mb-6 max-w-2xl mx-auto drop-shadow-sm font-sans">
                  Get ready for the ultimate beauty experience on your phone. Our mobile apps for Android and iOS are launching soon!
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                  <div className="flex items-center px-6 py-3 bg-charcoal/50 rounded-xl border border-neutral-off-white/20 cursor-not-allowed opacity-75">
                    <svg className="w-8 h-8 mr-3 text-neutral-off-white" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                    </svg>
                    <div className="text-left">
                      <div className="text-xs text-brand-beige/70">Coming Soon to</div>
                      <div className="text-lg font-semibold text-neutral-off-white">Apple App Store</div>
                    </div>
                  </div>
                  <div className="flex items-center px-6 py-3 bg-charcoal/50 rounded-xl border border-neutral-off-white/20 cursor-not-allowed opacity-75">
                    <svg className="w-8 h-8 mr-3 text-neutral-off-white" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.6 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z"/>
                    </svg>
                    <div className="text-left">
                      <div className="text-xs text-brand-beige/70">Coming Soon to</div>
                      <div className="text-lg font-semibold text-neutral-off-white">Google Play Store</div>
                    </div>
                  </div>
                </div>
              </div>
            </GoldenGlowingCardContainer>
          </div>
        </div>
      </section>
    </div>
  );
}
