"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AuroraBackgroundLayer } from "@/components/ui/aurora-background";
import { GoldenGlowingCardContainer } from "@/components/ui/golden-glowing-card-container";
import ShinyButton from "@/components/ui/shiny-button";
import { Users, Target, Lightbulb, Heart } from "lucide-react";
import { useState } from "react";

export default function About() {
  // Team section variables - commented out since team section is disabled
  // const [selectedMember, setSelectedMember] = useState<number | null>(null);

  /* const teamMembers = [
    {
      id: 1,
      name: "<PERSON>",
      position: "Founder & CEO",
      bio: "Former tech executive with 10+ years in product development. Passionate about empowering small businesses through technology.",
      image: "/team/alex.jpg"
    },
    {
      id: 2,
      name: "<PERSON>",
      position: "Head of Design",
      bio: "Award-winning UX designer with expertise in creating intuitive interfaces for service-based businesses.",
      image: "/team/sarah.jpg"
    },
    {
      id: 3,
      name: "<PERSON>",
      position: "Lead Developer",
      bio: "Full-stack engineer specializing in scalable platforms and AI integration for business automation.",
      image: "/team/marcus.jpg"
    },
    {
      id: 4,
      name: "Priya Patel",
      position: "Customer Success",
      bio: "Beauty industry veteran dedicated to helping professionals maximize their business potential through Vierla.",
      image: "/team/priya.jpg"
    },
    {
      id: 5,
      name: "Jordan Taylor",
      position: "Marketing Director",
      bio: "Growth marketing specialist with deep understanding of the beauty and wellness industry landscape.",
      image: "/team/jordan.jpg"
    },
    {
      id: 6,
      name: "Maya Rodriguez",
      position: "Operations Manager",
      bio: "Operations expert focused on streamlining processes and ensuring exceptional service delivery.",
      image: "/team/maya.jpg"
    }
  ]; */

  return (
    <div className="page-about relative overflow-hidden">
      <AuroraBackgroundLayer />

      {/* Hero Section */}
      <section className="relative z-10 w-full px-4 py-20 pt-36">
        <div className="text-center max-w-6xl mx-auto">
          <h1 className="text-4xl md:text-6xl font-black mb-6 leading-none drop-shadow-lg font-sans text-[#2D2A26] dark:text-[#F4F1E8]">
            WE'RE ON A MISSION TO EMPOWER BEAUTY & WELLNESS PROFESSIONALS
          </h1>
          <p className="text-xl md:text-2xl mb-8 leading-relaxed max-w-4xl mx-auto drop-shadow-sm font-inter text-[#2D2A26] dark:text-[#A9A299]">
            Building the future of beauty and wellness business operations, one professional at a time.
          </p>
        </div>
      </section>

      {/* The Future of Wellness, Simplified */}
      <section className="relative z-10 py-20 border-t border-white/20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-12">
              <h2 className="text-4xl md:text-5xl font-black text-[#2D2A26] dark:text-[#F4F1E8] mb-6 drop-shadow-lg font-jost">
                Our Story
              </h2>
            </div>

            <GoldenGlowingCardContainer>
              <div className="p-8 md:p-12">
                <div className="prose prose-xl max-w-none">
                  <p className="text-xl leading-relaxed mb-8 text-[#2D2A26] dark:text-[#A9A299] drop-shadow-sm font-inter">
                    Our founder's journey began with a clear mission: finding exceptional beauty professionals who could deliver the perfect fade and master the art of protective styling. Hours of research through social media, reviews, and recommendations revealed the challenge that talented professionals and discerning clients face daily—connecting with confidence and clarity.
                  </p>

                  <p className="text-xl leading-relaxed mb-8 text-[#2D2A26] dark:text-[#A9A299] drop-shadow-sm font-inter">
                    This experience illuminated a transformative opportunity in the beauty and wellness industry. Skilled professionals needed better ways to showcase their expertise and connect with ideal clients. Clients deserved transparent access to verified professionals, clear pricing, and authentic portfolios. The market was ready for a comprehensive solution that would serve both sides with excellence.
                  </p>

                  <p className="text-xl leading-relaxed mb-8 text-[#2D2A26] dark:text-[#A9A299] drop-shadow-sm font-inter">
                    Founded in 2025, Vierla emerged from a vision to revolutionize how beauty service providers connect with customers. We recognized the need for a comprehensive platform that empowers providers while simplifying discovery and booking for customers.
                  </p>

                  <p className="text-xl leading-relaxed mb-8 text-[#2D2A26] dark:text-[#A9A299] drop-shadow-sm font-inter">
                    We're building an ecosystem that benefits both sides of the beauty industry, helping providers grow their businesses while giving customers easy access to quality beauty and self-care services in their area. Our platform builds trust through transparency and empowers success through comprehensive tools. Service providers gain access to complete business management systems covering booking, payments, client relationships, and marketing. Clients discover verified professionals through curated marketplaces featuring authentic portfolios and confident booking experiences.
                  </p>

                  <p className="text-xl leading-relaxed mb-8 text-[#2D2A26] dark:text-[#A9A299] drop-shadow-sm font-inter">
                    Today, Vierla empowers beauty and wellness professionals to excel in their craft while we streamline their business operations. Master barbers perfect their techniques, braiding artists expand their clientele, and wellness professionals grow sustainable businesses. We provide the foundation that transforms passion into prosperity.
                  </p>

                  <div className="text-center mt-12">
                    <p className="text-2xl font-semibold text-[#2D2A26] dark:text-[#F4F1E8] drop-shadow-lg font-tai-heritage">
                      We build the future where every beauty professional thrives through innovation, connection, and excellence.
                    </p>
                  </div>
                </div>
              </div>
            </GoldenGlowingCardContainer>
          </div>
        </div>
      </section>

      {/* Meet the Team Section - COMMENTED OUT */}
      {/*
      <section className="relative z-10 py-20 border-t border-white/20">
        <div className="container mx-auto px-4">
          <div className="max-w-6xl mx-auto">
            <div className="text-center mb-16">
              <div className="flex items-center justify-center space-x-3 mb-6">
                <Users className="h-10 w-10 drop-shadow-lg" style={{ color: 'var(--icon-accent)' }} />
                <h2 className="text-4xl md:text-5xl font-black text-[#2D2A26] dark:text-[#F4F1E8] drop-shadow-lg font-jost">Meet the Team</h2>
              </div>
              <p className="text-xl text-[#2D2A26] dark:text-[#A9A299] max-w-3xl mx-auto drop-shadow-sm font-inter">
                We're a passionate group of entrepreneurs, developers, and designers united by our mission to empower beauty professionals.
              </p>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-8 mb-12">
              {teamMembers.map((member) => (
                <div key={member.id} className="flex flex-col items-center">
                  <div
                    className="relative group cursor-pointer"
                    onMouseEnter={() => setSelectedMember(member.id)}
                    onMouseLeave={() => setSelectedMember(null)}
                    onClick={() => setSelectedMember(selectedMember === member.id ? null : member.id)}
                  >
                    <div className="w-24 h-24 md:w-32 md:h-32 rounded-full overflow-hidden border-4 border-theme-primary/30 group-hover:border-theme-primary transition-all duration-300 shadow-lg group-hover:shadow-xl">
                      <div className="w-full h-full bg-gradient-to-br from-theme-primary/20 to-theme-primary/10 flex items-center justify-center">
                        <Users className="w-8 h-8 md:w-12 md:h-12" style={{ color: 'var(--theme-primary)' }} />
                      </div>
                    </div>
                    <div className="absolute -inset-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-md"
                         style={{ background: 'linear-gradient(45deg, var(--theme-primary)/20, var(--theme-primary)/40, var(--theme-primary)/20)' }} />
                  </div>
                  <h3 className="mt-4 text-lg font-semibold text-[#2D2A26] dark:text-[#F4F1E8] text-center font-tai-heritage">
                    {member.name}
                  </h3>
                  <p className="text-sm text-[#2D2A26] dark:text-[#A9A299] text-center font-inter">
                    {member.position}
                  </p>
                </div>
              ))}
            </div>

            <div className="mt-8">
              <GoldenGlowingCardContainer>
                <div className="p-8 text-center">
                  {(() => {
                    const member = teamMembers.find(m => m.id === selectedMember);
                    return member ? (
                      <div>
                        <h3 className="text-2xl font-bold text-[#2D2A26] dark:text-[#F4F1E8] mb-2 font-tai-heritage">
                          {member.name}
                        </h3>
                        <p className="text-lg text-theme-primary mb-4 font-inter">
                          {member.position}
                        </p>
                        <p className="text-lg text-[#2D2A26] dark:text-[#A9A299] leading-relaxed max-w-3xl mx-auto font-inter">
                          {member.bio}
                        </p>
                      </div>
                    ) : null;
                  })()}
                </div>
              </GoldenGlowingCardContainer>
            </div>
          </div>
        </div>
      </section>
      */}

      {/* CTA Section */}
      <section className="relative z-10 py-12 border-t border-white/20">
        <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <GoldenGlowingCardContainer interactive={false}>
            <div className="text-center pt-8 pb-4 px-6">
              <h2 className="font-heading text-3xl leading-[1.1] sm:text-4xl md:text-6xl drop-shadow-lg text-center font-tai-heritage text-[#2D2A26] dark:text-[#F4F1E8] mb-6">
                Ready to join our mission?
              </h2>
              <p className="max-w-[42rem] leading-normal sm:text-xl sm:leading-8 drop-shadow-sm text-center font-sans text-[#2D2A26] dark:text-[#A9A299] mb-8 mx-auto">
                Be part of the future of business operations. Start your journey with Vierla today.
              </p>
              <div className="flex justify-center">
                <ShinyButton size="lg" className="px-10 py-5 text-xl text-white dark:text-[var(--master-text-primary-dark)]">
                  <a href="/apply">Get Started</a>
                </ShinyButton>
              </div>
            </div>
          </GoldenGlowingCardContainer>
        </div>
        </div>
      </section>

    </div>
  );
}
