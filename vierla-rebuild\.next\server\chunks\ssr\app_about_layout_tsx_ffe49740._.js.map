{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/app/about/layout.tsx"], "sourcesContent": ["import { Metadata } from 'next'\n\nexport const metadata: Metadata = {\n  title: 'About Us - Our Mission & Story',\n  description: 'Learn about <PERSON><PERSON><PERSON>\\'s mission to simplify self-care by connecting clients with top beauty professionals. Discover our story, values, and commitment to excellence.',\n  openGraph: {\n    title: 'About Us - Our Mission & Story | Vierla',\n    description: 'Discover Vierla\\'s mission to revolutionize the beauty industry by connecting clients with verified professionals.',\n    url: 'https://vierla.com/about',\n    siteName: 'Vierla',\n    locale: 'en_CA',\n    type: 'website',\n  },\n  twitter: {\n    card: 'summary_large_image',\n    title: 'About Us - Our Mission & Story | Vierla',\n    description: 'Discover Vierla\\'s mission to revolutionize the beauty industry by connecting clients with verified professionals.',\n  },\n}\n\nexport default function AboutLayout({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  return children\n}\n"], "names": [], "mappings": ";;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,WAAW;QACT,OAAO;QACP,aAAa;QACb,KAAK;QACL,UAAU;QACV,QAAQ;QACR,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;IACf;AACF;AAEe,SAAS,YAAY,EAClC,QAAQ,EAGT;IACC,OAAO;AACT", "debugId": null}}]}