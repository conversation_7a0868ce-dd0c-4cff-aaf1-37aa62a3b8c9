{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AuroraBackground = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackground() from the server but AuroraBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx <module evaluation>\",\n    \"AuroraBackground\",\n);\nexport const AuroraBackgroundLayer = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackgroundLayer() from the server but AuroraBackgroundLayer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx <module evaluation>\",\n    \"AuroraBackgroundLayer\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,qEACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,qEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AuroraBackground = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackground() from the server but AuroraBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx\",\n    \"AuroraBackground\",\n);\nexport const AuroraBackgroundLayer = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackgroundLayer() from the server but AuroraBackgroundLayer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx\",\n    \"AuroraBackgroundLayer\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,iDACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,iDACA", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/app/terms/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\r\nimport { AuroraBackgroundLayer } from \"@/components/ui/aurora-background\";\r\n\r\nexport const metadata: Metadata = {\r\n  title: 'Terms of Service | Vierla',\r\n  description: 'Vierla\\'s terms of service outlining the rules and regulations for using our platform.',\r\n  openGraph: {\r\n    title: 'Terms of Service | Vierla',\r\n    description: 'Learn about the terms and conditions for using Vierla\\'s services.',\r\n    url: 'https://vierla.com/terms',\r\n    siteName: 'Vierla',\r\n    locale: 'en_CA',\r\n    type: 'website',\r\n  },\r\n}\r\n\r\nexport default function TermsOfService() {\r\n  return (\r\n    <div className=\"page-terms min-h-screen relative overflow-hidden\">\r\n      <AuroraBackgroundLayer />\r\n\r\n      {/* Main Content */}\r\n      <main className=\"relative z-10 container mx-auto px-4 py-20 pt-36\">\r\n        <div className=\"max-w-4xl mx-auto\">\r\n          <div className=\"p-8 md:p-12\">\r\n            <h1 className=\"text-4xl md:text-6xl font-black text-light-off-white mb-8 drop-shadow-lg font-sans\">\r\n              TERMS OF SERVICE\r\n            </h1>\r\n\r\n            <div className=\"prose prose-xl prose-invert max-w-none\">\r\n              <p className=\"text-vierla-text/90 text-xl mb-8\">\r\n                <strong>Effective Date:</strong> January 2025\r\n              </p>\r\n\r\n              <p className=\"text-vierla-text/90 text-lg mb-8\">\r\n                Welcome to Vierla. These Terms of Service (\"Terms\") govern your use of our website www.vierla.com and our beauty services platform. By accessing or using our services, you agree to be bound by these Terms.\r\n              </p>\r\n\r\n              <h2 className=\"text-3xl md:text-4xl font-bold text-vierla-text mt-10 mb-6 drop-shadow-lg\">1. Service Description</h2>\r\n              <p className=\"text-vierla-text/90 text-lg mb-8\">\r\n                Vierla is a platform that connects customers with vetted beauty professionals for in-home services including hair styling, makeup, nail care, and other beauty treatments. We facilitate bookings but do not directly provide beauty services.\r\n              </p>\r\n\r\n              <h2 className=\"text-3xl md:text-4xl font-bold text-vierla-text mt-10 mb-6 drop-shadow-lg\">2. User Accounts</h2>\r\n              <p className=\"text-vierla-text/90 text-lg mb-6\">To use our services, you must:</p>\r\n              <ul className=\"text-vierla-text/90 text-lg mb-8 space-y-3\">\r\n                <li>• Be at least 18 years old or have parental consent</li>\r\n                <li>• Provide accurate and complete information</li>\r\n                <li>• Maintain the security of your account credentials</li>\r\n                <li>• Notify us immediately of any unauthorized use</li>\r\n              </ul>\r\n\r\n              <h2 className=\"text-3xl md:text-4xl font-bold text-vierla-text mt-10 mb-6 drop-shadow-lg\">3. Booking and Payment</h2>\r\n              <p className=\"text-vierla-text/90 text-lg mb-6\">When you book services through Vierla:</p>\r\n              <ul className=\"text-vierla-text/90 text-lg mb-8 space-y-3\">\r\n                <li>• You enter into a contract with the beauty professional</li>\r\n                <li>• Payment is processed securely through our platform</li>\r\n                <li>• Cancellation policies vary by professional</li>\r\n                <li>• Refunds are subject to our refund policy</li>\r\n              </ul>\r\n\r\n              <h2 className=\"text-3xl md:text-4xl font-bold text-vierla-text mt-10 mb-6 drop-shadow-lg\">4. Professional Standards</h2>\r\n              <p className=\"text-vierla-text/90 text-lg mb-6\">All beauty professionals on our platform must:</p>\r\n              <ul className=\"text-vierla-text/90 text-lg mb-8 space-y-3\">\r\n                <li>• Hold valid licenses and certifications</li>\r\n                <li>• Maintain professional liability insurance</li>\r\n                <li>• Follow health and safety protocols</li>\r\n                <li>• Provide services as described</li>\r\n              </ul>\r\n\r\n              <h2 className=\"text-3xl md:text-4xl font-bold text-vierla-text mt-10 mb-6 drop-shadow-lg\">5. User Conduct</h2>\r\n              <p className=\"text-vierla-text/90 text-lg mb-6\">Users must not:</p>\r\n              <ul className=\"text-vierla-text/90 text-lg mb-8 space-y-3\">\r\n                <li>• Violate any laws or regulations</li>\r\n                <li>• Harass or discriminate against others</li>\r\n                <li>• Share false or misleading information</li>\r\n                <li>• Attempt to circumvent our platform</li>\r\n              </ul>\r\n\r\n              <h2 className=\"text-3xl md:text-4xl font-bold text-vierla-text mt-10 mb-6 drop-shadow-lg\">6. Liability and Disclaimers</h2>\r\n              <p className=\"text-vierla-text/90 text-lg mb-8\">\r\n                Vierla acts as an intermediary platform. While we vet our professionals, we cannot guarantee the quality of services. Beauty professionals are independent contractors responsible for their own services, insurance, and compliance with local regulations.\r\n              </p>\r\n\r\n              <h2 className=\"text-3xl md:text-4xl font-bold text-vierla-text mt-10 mb-6 drop-shadow-lg\">7. Intellectual Property</h2>\r\n              <p className=\"text-vierla-text/90 text-lg mb-8\">\r\n                All content on our platform, including logos, text, images, and software, is owned by Vierla or our licensors and protected by intellectual property laws.\r\n              </p>\r\n\r\n              <h2 className=\"text-3xl md:text-4xl font-bold text-vierla-text mt-10 mb-6 drop-shadow-lg\">8. Privacy</h2>\r\n              <p className=\"text-vierla-text/90 text-lg mb-8\">\r\n                Your privacy is important to us. Please review our Privacy Policy to understand how we collect, use, and protect your information.\r\n              </p>\r\n\r\n              <h2 className=\"text-3xl md:text-4xl font-bold text-vierla-text mt-10 mb-6 drop-shadow-lg\">9. Termination</h2>\r\n              <p className=\"text-vierla-text/90 text-lg mb-8\">\r\n                We may suspend or terminate your account for violations of these Terms or for any reason with appropriate notice. You may close your account at any time.\r\n              </p>\r\n\r\n              <h2 className=\"text-3xl md:text-4xl font-bold text-vierla-text mt-10 mb-6 drop-shadow-lg\">10. Dispute Resolution</h2>\r\n              <p className=\"text-vierla-text/90 text-lg mb-8\">\r\n                Any disputes arising from these Terms will be resolved through binding arbitration in accordance with the rules of the American Arbitration Association.\r\n              </p>\r\n\r\n              <h2 className=\"text-3xl md:text-4xl font-bold text-vierla-text mt-10 mb-6 drop-shadow-lg\">11. Changes to Terms</h2>\r\n              <p className=\"text-vierla-text/90 text-lg mb-8\">\r\n                We may update these Terms periodically. Continued use of our services after changes constitutes acceptance of the new Terms.\r\n              </p>\r\n\r\n              <h2 className=\"text-3xl md:text-4xl font-bold text-vierla-text mt-10 mb-6 drop-shadow-lg\">Contact Information</h2>\r\n              <p className=\"text-vierla-text/90 text-lg mb-8\">\r\n                For questions about these Terms, contact us at:\r\n                <br />\r\n                Email: <EMAIL>\r\n              </p>\r\n\r\n              <p className=\"text-vierla-text/90 text-base mt-10\">\r\n                These Terms of Service constitute the entire agreement between you and Vierla regarding your use of our services.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </main>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,WAAW;QACT,OAAO;QACP,aAAa;QACb,KAAK;QACL,UAAU;QACV,QAAQ;QACR,MAAM;IACR;AACF;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,yIAAA,CAAA,wBAAqB;;;;;0BAGtB,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqF;;;;;;0CAInG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;;0DACX,8OAAC;0DAAO;;;;;;4CAAwB;;;;;;;kDAGlC,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAIhD,8OAAC;wCAAG,WAAU;kDAA4E;;;;;;kDAC1F,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAIhD,8OAAC;wCAAG,WAAU;kDAA4E;;;;;;kDAC1F,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAChD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;kDAGN,8OAAC;wCAAG,WAAU;kDAA4E;;;;;;kDAC1F,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAChD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;kDAGN,8OAAC;wCAAG,WAAU;kDAA4E;;;;;;kDAC1F,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAChD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;kDAGN,8OAAC;wCAAG,WAAU;kDAA4E;;;;;;kDAC1F,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAChD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;kDAGN,8OAAC;wCAAG,WAAU;kDAA4E;;;;;;kDAC1F,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAIhD,8OAAC;wCAAG,WAAU;kDAA4E;;;;;;kDAC1F,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAIhD,8OAAC;wCAAG,WAAU;kDAA4E;;;;;;kDAC1F,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAIhD,8OAAC;wCAAG,WAAU;kDAA4E;;;;;;kDAC1F,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAIhD,8OAAC;wCAAG,WAAU;kDAA4E;;;;;;kDAC1F,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAIhD,8OAAC;wCAAG,WAAU;kDAA4E;;;;;;kDAC1F,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAIhD,8OAAC;wCAAG,WAAU;kDAA4E;;;;;;kDAC1F,8OAAC;wCAAE,WAAU;;4CAAmC;0DAE9C,8OAAC;;;;;4CAAK;;;;;;;kDAIR,8OAAC;wCAAE,WAAU;kDAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjE", "debugId": null}}]}