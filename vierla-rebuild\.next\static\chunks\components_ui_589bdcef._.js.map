{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx"], "sourcesContent": ["\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport React, { memo, type ReactNode } from \"react\";\n\ninterface AuroraBackgroundProps extends React.HTMLProps<HTMLDivElement> {\n  children: ReactNode;\n  showRadialGradient?: boolean;\n}\n\n// Full page wrapper version\nexport const AuroraBackground = ({\n  className,\n  children,\n  showRadialGradient = true,\n  ...props\n}: AuroraBackgroundProps) => {\n  return (\n    <div\n      className={cn(\n        \"relative flex flex-col h-[100vh] items-center justify-center text-slate-950 transition-colors duration-300\",\n        className\n      )}\n      {...props}\n    >\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div\n          className={cn(\n            `\n            [--light-bg:#F4F1E8]\n            [--dark-bg:#2D2A26]\n            [--light-aurora:repeating-linear-gradient(100deg,#364035_10%,#8B9A8C_15%,#F0E6D9_20%,#7C9A85_25%,#364035_30%)]\n            [--dark-aurora:repeating-linear-gradient(100deg,#B8956A_10%,#4B3D6B_15%,#B8956A_20%,#4B3D6B_25%,#B8956A_30%)]\n            [--light-stripes:repeating-linear-gradient(100deg,var(--light-bg)_0%,var(--light-bg)_7%,transparent_10%,transparent_12%,var(--light-bg)_16%)]\n            [--dark-stripes:repeating-linear-gradient(100deg,var(--dark-bg)_0%,var(--dark-bg)_7%,transparent_10%,transparent_12%,var(--dark-bg)_16%)]\n            [background-image:var(--light-stripes),var(--light-aurora)]\n            dark:[background-image:var(--dark-stripes),var(--dark-aurora)]\n            [background-size:300%,_200%]\n            [background-position:50%_50%,50%_50%]\n            filter blur-[10px]\n            after:content-[\"\"] after:absolute after:inset-0 after:[background-image:var(--light-stripes),var(--light-aurora)]\n            after:dark:[background-image:var(--dark-stripes),var(--dark-aurora)]\n            after:[background-size:200%,_100%]\n            after:animate-aurora after:[background-attachment:fixed] after:mix-blend-difference\n            pointer-events-none\n            absolute -inset-[10px] opacity-50 will-change-transform`,\n            showRadialGradient && `[mask-image:radial-gradient(ellipse_at_100%_0%,black_10%,transparent_70%)]`\n          )}\n        ></div>\n      </div>\n      {children}\n    </div>\n  );\n};\n\n// Background-only version for existing layouts\nexport const AuroraBackgroundLayer = memo(\n  ({\n    className,\n    showRadialGradient = true,\n  }: {\n    className?: string;\n    showRadialGradient?: boolean;\n  }) => {\n    return (\n      <div className=\"fixed inset-0 -z-10 overflow-hidden\">\n        <div\n          className={cn(\n            `\n            [--light-bg:#F4F1E8]\n            [--dark-bg:#2D2A26]\n            [--light-aurora:repeating-linear-gradient(100deg,#364035_10%,#8B9A8C_15%,#F0E6D9_20%,#7C9A85_25%,#364035_30%)]\n            [--dark-aurora:repeating-linear-gradient(100deg,#B8956A_10%,#4B3D6B_15%,#B8956A_20%,#4B3D6B_25%,#B8956A_30%)]\n            [--light-stripes:repeating-linear-gradient(100deg,var(--light-bg)_0%,var(--light-bg)_7%,transparent_10%,transparent_12%,var(--light-bg)_16%)]\n            [--dark-stripes:repeating-linear-gradient(100deg,var(--dark-bg)_0%,var(--dark-bg)_7%,transparent_10%,transparent_12%,var(--dark-bg)_16%)]\n            [background-image:var(--light-stripes),var(--light-aurora)]\n            dark:[background-image:var(--dark-stripes),var(--dark-aurora)]\n            [background-size:300%,_200%]\n            [background-position:50%_50%,50%_50%]\n            filter blur-[10px]\n            after:content-[\"\"] after:absolute after:inset-0 after:[background-image:var(--light-stripes),var(--light-aurora)]\n            after:dark:[background-image:var(--dark-stripes),var(--dark-aurora)]\n            after:[background-size:200%,_100%]\n            after:animate-aurora after:[background-attachment:fixed] after:mix-blend-difference\n            pointer-events-none\n            absolute -inset-[10px] opacity-50 will-change-transform`,\n            showRadialGradient && `[mask-image:radial-gradient(ellipse_at_100%_0%,black_10%,transparent_70%)]`,\n            className\n          )}\n        ></div>\n      </div>\n    );\n  }\n);\n\nAuroraBackgroundLayer.displayName = \"AuroraBackgroundLayer\";"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAWO,MAAM,mBAAmB;QAAC,EAC/B,SAAS,EACT,QAAQ,EACR,qBAAqB,IAAI,EACzB,GAAG,OACmB;IACtB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8GACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACT,00CAkBD,sBAAuB;;;;;;;;;;;YAI5B;;;;;;;AAGP;KA1Ca;AA6CN,MAAM,sCAAwB,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,QACtC;QAAC,EACC,SAAS,EACT,qBAAqB,IAAI,EAI1B;IACC,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACT,00CAkBD,sBAAuB,8EACvB;;;;;;;;;;;AAKV;;AAGF,sBAAsB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 79, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/glowing-effect.tsx"], "sourcesContent": ["\"use client\";\n\nimport { memo, useCallback, useEffect, useRef } from \"react\";\nimport { cn } from \"@/lib/utils\";\nimport { animate } from \"motion/react\";\n\ninterface GlowingEffectProps {\n  blur?: number;\n  inactiveZone?: number;\n  proximity?: number;\n  spread?: number;\n  variant?: \"default\" | \"white\" | \"sage\";\n  glow?: boolean;\n  className?: string;\n  disabled?: boolean;\n  movementDuration?: number;\n  borderWidth?: number;\n}\n\nconst GlowingEffect = memo(\n  ({\n    blur = 0,\n    inactiveZone = 0.7,\n    proximity = 0,\n    spread = 20,\n    variant = \"sage\",\n    glow = false,\n    className,\n    movementDuration = 2,\n    borderWidth = 1,\n    disabled = true,\n  }: GlowingEffectProps) => {\n    const containerRef = useRef<HTMLDivElement>(null);\n    const lastPosition = useRef({ x: 0, y: 0 });\n    const animationFrameRef = useRef<number>(0);\n\n    const handleMove = useCallback(\n      (e?: MouseEvent | { x: number; y: number }) => {\n        if (!containerRef.current) return;\n\n        if (animationFrameRef.current) {\n          cancelAnimationFrame(animationFrameRef.current);\n        }\n\n        animationFrameRef.current = requestAnimationFrame(() => {\n          const element = containerRef.current;\n          if (!element) return;\n\n          const { left, top, width, height } = element.getBoundingClientRect();\n          const mouseX = e?.x ?? lastPosition.current.x;\n          const mouseY = e?.y ?? lastPosition.current.y;\n\n          if (e) {\n            lastPosition.current = { x: mouseX, y: mouseY };\n          }\n\n          const center = [left + width * 0.5, top + height * 0.5];\n          const distanceFromCenter = Math.hypot(\n            mouseX - center[0],\n            mouseY - center[1]\n          );\n          const inactiveRadius = 0.5 * Math.min(width, height) * inactiveZone;\n\n          if (distanceFromCenter < inactiveRadius) {\n            element.style.setProperty(\"--active\", \"0\");\n            return;\n          }\n\n          const isActive =\n            mouseX > left - proximity &&\n            mouseX < left + width + proximity &&\n            mouseY > top - proximity &&\n            mouseY < top + height + proximity;\n\n          element.style.setProperty(\"--active\", isActive ? \"1\" : \"0\");\n\n          if (!isActive) return;\n\n          const currentAngle =\n            parseFloat(element.style.getPropertyValue(\"--start\")) || 0;\n          let targetAngle =\n            (180 * Math.atan2(mouseY - center[1], mouseX - center[0])) /\n              Math.PI +\n            90;\n\n          const angleDiff = ((targetAngle - currentAngle + 180) % 360) - 180;\n          const newAngle = currentAngle + angleDiff;\n\n          animate(currentAngle, newAngle, {\n            duration: movementDuration,\n            ease: [0.16, 1, 0.3, 1],\n            onUpdate: (value) => {\n              element.style.setProperty(\"--start\", String(value));\n            },\n          });\n        });\n      },\n      [inactiveZone, proximity, movementDuration]\n    );\n\n    useEffect(() => {\n      if (disabled) return;\n\n      const handleScroll = () => handleMove();\n      const handlePointerMove = (e: PointerEvent) => handleMove(e);\n\n      window.addEventListener(\"scroll\", handleScroll, { passive: true });\n      document.body.addEventListener(\"pointermove\", handlePointerMove, {\n        passive: true,\n      });\n\n      return () => {\n        if (animationFrameRef.current) {\n          cancelAnimationFrame(animationFrameRef.current);\n        }\n        window.removeEventListener(\"scroll\", handleScroll);\n        document.body.removeEventListener(\"pointermove\", handlePointerMove);\n      };\n    }, [handleMove, disabled]);\n\n    // Define gradient variants - THEME-AWARE\n    const getGradient = () => {\n      switch (variant) {\n        case \"white\":\n          return `repeating-conic-gradient(\n            from 236.84deg at 50% 50%,\n            var(--black),\n            var(--black) calc(25% / var(--repeating-conic-gradient-times))\n          )`;\n        case \"sage\":\n          // Theme-aware sage variant: Light Mode = Forest/Sage, Dark Mode = Gold\n          return `radial-gradient(circle, var(--theme-primary) 10%, transparent 20%),\n            radial-gradient(circle at 40% 40%, #F4F1E8 5%, transparent 15%),\n            radial-gradient(circle at 60% 60%, var(--theme-primary) 10%, transparent 20%),\n            radial-gradient(circle at 40% 60%, var(--theme-secondary) 10%, transparent 20%),\n            repeating-conic-gradient(\n              from 236.84deg at 50% 50%,\n              var(--theme-primary) 0%,\n              #F4F1E8 calc(25% / var(--repeating-conic-gradient-times)),\n              var(--theme-primary) calc(50% / var(--repeating-conic-gradient-times)),\n              var(--theme-secondary) calc(75% / var(--repeating-conic-gradient-times)),\n              var(--theme-primary) calc(100% / var(--repeating-conic-gradient-times))\n            )`;\n        default:\n          return `radial-gradient(circle, #dd7bbb 10%, #dd7bbb00 20%),\n            radial-gradient(circle at 40% 40%, #d79f1e 5%, #d79f1e00 15%),\n            radial-gradient(circle at 60% 60%, #5a922c 10%, #5a922c00 20%),\n            radial-gradient(circle at 40% 60%, #4c7894 10%, #4c789400 20%),\n            repeating-conic-gradient(\n              from 236.84deg at 50% 50%,\n              #dd7bbb 0%,\n              #d79f1e calc(25% / var(--repeating-conic-gradient-times)),\n              #5a922c calc(50% / var(--repeating-conic-gradient-times)),\n              #4c7894 calc(75% / var(--repeating-conic-gradient-times)),\n              #dd7bbb calc(100% / var(--repeating-conic-gradient-times))\n            )`;\n      }\n    };\n\n    return (\n      <>\n        <div\n          className={cn(\n            \"pointer-events-none absolute -inset-px hidden rounded-[inherit] border opacity-0 transition-opacity\",\n            glow && \"opacity-100\",\n            variant === \"white\" && \"border-white\",\n            disabled && \"!block\"\n          )}\n        />\n        <div\n          ref={containerRef}\n          style={\n            {\n              \"--blur\": `${blur}px`,\n              \"--spread\": spread,\n              \"--start\": \"0\",\n              \"--active\": \"0\",\n              \"--glowingeffect-border-width\": `${borderWidth}px`,\n              \"--repeating-conic-gradient-times\": \"5\",\n              \"--gradient\": getGradient(),\n            } as React.CSSProperties\n          }\n          className={cn(\n            \"pointer-events-none absolute inset-0 rounded-[inherit] opacity-100 transition-opacity\",\n            glow && \"opacity-100\",\n            blur > 0 && \"blur-[var(--blur)] \",\n            className,\n            disabled && \"!hidden\"\n          )}\n        >\n          <div\n            className={cn(\n              \"glow\",\n              \"rounded-[inherit]\",\n              'after:content-[\"\"] after:rounded-[inherit] after:absolute after:inset-[calc(-1*var(--glowingeffect-border-width))]',\n              \"after:[border:var(--glowingeffect-border-width)_solid_transparent]\",\n              \"after:[background:var(--gradient)] after:[background-attachment:fixed]\",\n              \"after:opacity-[var(--active)] after:transition-opacity after:duration-300\",\n              \"after:[mask-clip:padding-box,border-box]\",\n              \"after:[mask-composite:intersect]\",\n              \"after:[mask-image:linear-gradient(#0000,#0000),conic-gradient(from_calc((var(--start)-var(--spread))*1deg),#00000000_0deg,#fff,#00000000_calc(var(--spread)*2deg))]\"\n            )}\n          />\n        </div>\n      </>\n    );\n  }\n);\n\nGlowingEffect.displayName = \"GlowingEffect\";\n\nexport { GlowingEffect };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAmBA,MAAM,8BAAgB,GAAA,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,UACvB;QAAC,EACC,OAAO,CAAC,EACR,eAAe,GAAG,EAClB,YAAY,CAAC,EACb,SAAS,EAAE,EACX,UAAU,MAAM,EAChB,OAAO,KAAK,EACZ,SAAS,EACT,mBAAmB,CAAC,EACpB,cAAc,CAAC,EACf,WAAW,IAAI,EACI;;IACnB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IACzC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAU;IAEzC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAC3B,CAAC;YACC,IAAI,CAAC,aAAa,OAAO,EAAE;YAE3B,IAAI,kBAAkB,OAAO,EAAE;gBAC7B,qBAAqB,kBAAkB,OAAO;YAChD;YAEA,kBAAkB,OAAO,GAAG;yDAAsB;oBAChD,MAAM,UAAU,aAAa,OAAO;oBACpC,IAAI,CAAC,SAAS;oBAEd,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,QAAQ,qBAAqB;wBACnD;oBAAf,MAAM,SAAS,CAAA,OAAA,cAAA,wBAAA,EAAG,CAAC,cAAJ,kBAAA,OAAQ,aAAa,OAAO,CAAC,CAAC;wBAC9B;oBAAf,MAAM,SAAS,CAAA,OAAA,cAAA,wBAAA,EAAG,CAAC,cAAJ,kBAAA,OAAQ,aAAa,OAAO,CAAC,CAAC;oBAE7C,IAAI,GAAG;wBACL,aAAa,OAAO,GAAG;4BAAE,GAAG;4BAAQ,GAAG;wBAAO;oBAChD;oBAEA,MAAM,SAAS;wBAAC,OAAO,QAAQ;wBAAK,MAAM,SAAS;qBAAI;oBACvD,MAAM,qBAAqB,KAAK,KAAK,CACnC,SAAS,MAAM,CAAC,EAAE,EAClB,SAAS,MAAM,CAAC,EAAE;oBAEpB,MAAM,iBAAiB,MAAM,KAAK,GAAG,CAAC,OAAO,UAAU;oBAEvD,IAAI,qBAAqB,gBAAgB;wBACvC,QAAQ,KAAK,CAAC,WAAW,CAAC,YAAY;wBACtC;oBACF;oBAEA,MAAM,WACJ,SAAS,OAAO,aAChB,SAAS,OAAO,QAAQ,aACxB,SAAS,MAAM,aACf,SAAS,MAAM,SAAS;oBAE1B,QAAQ,KAAK,CAAC,WAAW,CAAC,YAAY,WAAW,MAAM;oBAEvD,IAAI,CAAC,UAAU;oBAEf,MAAM,eACJ,WAAW,QAAQ,KAAK,CAAC,gBAAgB,CAAC,eAAe;oBAC3D,IAAI,cACF,AAAC,MAAM,KAAK,KAAK,CAAC,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,MAAM,CAAC,EAAE,IACtD,KAAK,EAAE,GACT;oBAEF,MAAM,YAAY,AAAC,CAAC,cAAc,eAAe,GAAG,IAAI,MAAO;oBAC/D,MAAM,WAAW,eAAe;oBAEhC,CAAA,GAAA,mLAAA,CAAA,UAAO,AAAD,EAAE,cAAc,UAAU;wBAC9B,UAAU;wBACV,MAAM;4BAAC;4BAAM;4BAAG;4BAAK;yBAAE;wBACvB,QAAQ;qEAAE,CAAC;gCACT,QAAQ,KAAK,CAAC,WAAW,CAAC,WAAW,OAAO;4BAC9C;;oBACF;gBACF;;QACF;gDACA;QAAC;QAAc;QAAW;KAAiB;IAG7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,UAAU;YAEd,MAAM;wDAAe,IAAM;;YAC3B,MAAM;6DAAoB,CAAC,IAAoB,WAAW;;YAE1D,OAAO,gBAAgB,CAAC,UAAU,cAAc;gBAAE,SAAS;YAAK;YAChE,SAAS,IAAI,CAAC,gBAAgB,CAAC,eAAe,mBAAmB;gBAC/D,SAAS;YACX;YAEA;2CAAO;oBACL,IAAI,kBAAkB,OAAO,EAAE;wBAC7B,qBAAqB,kBAAkB,OAAO;oBAChD;oBACA,OAAO,mBAAmB,CAAC,UAAU;oBACrC,SAAS,IAAI,CAAC,mBAAmB,CAAC,eAAe;gBACnD;;QACF;kCAAG;QAAC;QAAY;KAAS;IAEzB,yCAAyC;IACzC,MAAM,cAAc;QAClB,OAAQ;YACN,KAAK;gBACH,OAAQ;YAKV,KAAK;gBACH,uEAAuE;gBACvE,OAAQ;YAYV;gBACE,OAAQ;QAYZ;IACF;IAEA,qBACE;;0BACE,6LAAC;gBACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,uGACA,QAAQ,eACR,YAAY,WAAW,gBACvB,YAAY;;;;;;0BAGhB,6LAAC;gBACC,KAAK;gBACL,OACE;oBACE,UAAU,AAAC,GAAO,OAAL,MAAK;oBAClB,YAAY;oBACZ,WAAW;oBACX,YAAY;oBACZ,gCAAgC,AAAC,GAAc,OAAZ,aAAY;oBAC/C,oCAAoC;oBACpC,cAAc;gBAChB;gBAEF,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,yFACA,QAAQ,eACR,OAAO,KAAK,uBACZ,WACA,YAAY;0BAGd,cAAA,6LAAC;oBACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,QACA,qBACA,sHACA,sEACA,0EACA,6EACA,4CACA,oCACA;;;;;;;;;;;;;AAMZ;;AAGF,cAAc,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/golden-glowing-card-container.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { cn } from \"@/lib/utils\";\nimport { GlowingEffect } from \"./glowing-effect\";\n\ninterface GoldenGlowingCardContainerProps {\n  children: React.ReactNode;\n  className?: string;\n}\n\nexport const GoldenGlowingCardContainer: React.FC<GoldenGlowingCardContainerProps> = React.memo(({\n  children,\n  className,\n}) => {\n  return (\n    <div className={cn(\"relative h-full group\", className)}>\n      {/* Outer container with glowing effect */}\n      <div className=\"relative h-full rounded-[1.25rem] border-[0.75px] border-white/20 p-2 md:rounded-[1.5rem] md:p-3\">\n        <GlowingEffect\n          spread={40}\n          glow={true}\n          disabled={false}\n          proximity={64}\n          inactiveZone={0.01}\n          borderWidth={3}\n          variant=\"sage\"\n        />\n\n        {/* Inner container with margin - Enhanced glassmorphism with 80% transparency */}\n        <div className=\"relative flex h-full flex-col justify-between gap-6 overflow-hidden rounded-xl border-[0.75px] p-6 shadow-xl\" style={{\n          backgroundColor: 'var(--card-bg)',\n          borderColor: 'var(--border-accent)',\n          backdropFilter: 'blur(16px)',\n          WebkitBackdropFilter: 'blur(16px)'\n        }}>\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n});\n\nexport default GoldenGlowingCardContainer;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAWO,MAAM,2CAAwE,6JAAA,CAAA,UAAK,CAAC,IAAI,MAAC;QAAC,EAC/F,QAAQ,EACR,SAAS,EACV;IACC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;kBAE1C,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC,yIAAA,CAAA,gBAAa;oBACZ,QAAQ;oBACR,MAAM;oBACN,UAAU;oBACV,WAAW;oBACX,cAAc;oBACd,aAAa;oBACb,SAAQ;;;;;;8BAIV,6LAAC;oBAAI,WAAU;oBAA+G,OAAO;wBACnI,iBAAiB;wBACjB,aAAa;wBACb,gBAAgB;wBAChB,sBAAsB;oBACxB;8BACG;;;;;;;;;;;;;;;;;AAKX;;uCAEe", "debugId": null}}, {"offset": {"line": 326, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/bento-grid.tsx"], "sourcesContent": ["\"use client\";\n\nimport { ReactNode, useState, useEffect } from \"react\";\nimport { ArrowRightIcon } from \"@radix-ui/react-icons\";\nimport Link from \"next/link\";\nimport { useRouter } from \"next/navigation\";\n\nimport { cn } from \"@/lib/utils\";\nimport { Button } from \"@/components/ui/button\";\n\nconst BentoGrid = ({\n  children,\n  className,\n}: {\n  children: ReactNode;\n  className?: string;\n}) => {\n  return (\n    <div\n      className={cn(\n        \"grid w-full auto-rows-[20rem] grid-cols-3 gap-4\",\n        // 16px gap as specified in design system\n        \"gap-4\", // 16px\n        className,\n      )}\n    >\n      {children}\n    </div>\n  );\n};\n\nconst BentoCard = ({\n  name,\n  className,\n  background,\n  Icon,\n  description,\n  services,\n  href,\n  cta,\n}: {\n  name: string;\n  className: string;\n  background?: ReactNode;\n  Icon: any;\n  description: string;\n  services?: string[];\n  href: string;\n  cta: string;\n}) => {\n  const [isClicked, setIsClicked] = useState(false);\n  const [isMobile, setIsMobile] = useState(false);\n  const router = useRouter();\n\n  useEffect(() => {\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth < 768); // md breakpoint\n    };\n\n    checkMobile();\n    window.addEventListener('resize', checkMobile);\n    return () => window.removeEventListener('resize', checkMobile);\n  }, []);\n\n  const handleClick = (e: React.MouseEvent) => {\n    if (isMobile) {\n      e.preventDefault();\n      if (!isClicked) {\n        // First click: reveal text\n        setIsClicked(true);\n      } else {\n        // Second click: navigate\n        router.push(href);\n      }\n    }\n    // On desktop, let the Link handle navigation normally\n  };\n\n  return (\n    <Link\n      href={href}\n      onClick={handleClick}\n      className={cn(\n        \"group relative col-span-3 flex flex-col justify-between overflow-hidden rounded-xl\",\n        // Theme-aware background and borders\n        \"border shadow-xl transition-all duration-500 ease-out transform-gpu\",\n        // Smooth hover effects for desktop, click effects for mobile\n        \"hover:scale-[1.02] hover:shadow-2xl\",\n        // Mobile click state\n        isMobile && isClicked && \"scale-[1.02] shadow-2xl\",\n        className,\n      )}\n      style={{\n        backgroundColor: 'var(--card-bg)',\n        borderColor: 'var(--border-subtle)',\n        boxShadow: 'var(--shadow-card)'\n      }}\n    >\n      {background && <div>{background}</div>}\n      <div className=\"pointer-events-none z-10 flex transform-gpu flex-col gap-2 p-4 transition-all duration-500\">\n        <Icon className={cn(\n          \"h-10 w-10 origin-left transform-gpu transition-all duration-500 ease-out\",\n          // Desktop hover effects\n          \"group-hover:scale-110\",\n          // Mobile click effects\n          isMobile && isClicked && \"scale-110\"\n        )}\n              style={{ color: 'var(--icon-accent)' }} />\n        <h3 className={cn(\n          \"text-lg font-semibold font-tai-heritage leading-tight transition-all duration-500\",\n          // Desktop hover effects\n          \"group-hover:opacity-0 group-hover:transform group-hover:-translate-y-2\",\n          // Mobile click effects\n          isMobile && isClicked && \"opacity-0 transform -translate-y-2\"\n        )}\n            style={{ color: 'var(--text-primary)' }}>\n          {name}\n        </h3>\n        <p className={cn(\n          \"max-w-lg font-sans text-sm leading-relaxed transition-all duration-500 delay-100\",\n          // Desktop hover effects\n          \"opacity-0 transform translate-y-2 group-hover:opacity-100 group-hover:translate-y-0\",\n          // Mobile click effects\n          isMobile && isClicked && \"opacity-100 translate-y-0\",\n          // Mobile default state\n          isMobile && !isClicked && \"opacity-0 transform translate-y-2\"\n        )}\n           style={{ color: 'var(--text-secondary)' }}>{description}</p>\n      </div>\n\n      <div className={cn(\n        \"pointer-events-none absolute inset-0 transform-gpu transition-all duration-500\",\n        // Desktop hover effects\n        \"opacity-0 group-hover:opacity-100\",\n        // Mobile click effects\n        isMobile && isClicked && \"opacity-100\"\n      )}\n           style={{ backgroundColor: 'var(--accent-hover-overlay)' }} />\n    </Link>\n  );\n};\n\nexport { BentoCard, BentoGrid };\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;AAEA;;;AAPA;;;;;AAUA,MAAM,YAAY;QAAC,EACjB,QAAQ,EACR,SAAS,EAIV;IACC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,mDACA,yCAAyC;QACzC,SACA;kBAGD;;;;;;AAGP;KAnBM;AAqBN,MAAM,YAAY;QAAC,EACjB,IAAI,EACJ,SAAS,EACT,UAAU,EACV,IAAI,EACJ,WAAW,EACX,QAAQ,EACR,IAAI,EACJ,GAAG,EAUJ;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM;mDAAc;oBAClB,YAAY,OAAO,UAAU,GAAG,MAAM,gBAAgB;gBACxD;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;uCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;8BAAG,EAAE;IAEL,MAAM,cAAc,CAAC;QACnB,IAAI,UAAU;YACZ,EAAE,cAAc;YAChB,IAAI,CAAC,WAAW;gBACd,2BAA2B;gBAC3B,aAAa;YACf,OAAO;gBACL,yBAAyB;gBACzB,OAAO,IAAI,CAAC;YACd;QACF;IACA,sDAAsD;IACxD;IAEA,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM;QACN,SAAS;QACT,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,sFACA,qCAAqC;QACrC,uEACA,6DAA6D;QAC7D,uCACA,qBAAqB;QACrB,YAAY,aAAa,2BACzB;QAEF,OAAO;YACL,iBAAiB;YACjB,aAAa;YACb,WAAW;QACb;;YAEC,4BAAc,6LAAC;0BAAK;;;;;;0BACrB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAK,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EAChB,4EACA,wBAAwB;wBACxB,yBACA,uBAAuB;wBACvB,YAAY,aAAa;wBAErB,OAAO;4BAAE,OAAO;wBAAqB;;;;;;kCAC3C,6LAAC;wBAAG,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACd,qFACA,wBAAwB;wBACxB,0EACA,uBAAuB;wBACvB,YAAY,aAAa;wBAEvB,OAAO;4BAAE,OAAO;wBAAsB;kCACvC;;;;;;kCAEH,6LAAC;wBAAE,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACb,oFACA,wBAAwB;wBACxB,uFACA,uBAAuB;wBACvB,YAAY,aAAa,6BACzB,uBAAuB;wBACvB,YAAY,CAAC,aAAa;wBAEzB,OAAO;4BAAE,OAAO;wBAAwB;kCAAI;;;;;;;;;;;;0BAGjD,6LAAC;gBAAI,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACf,kFACA,wBAAwB;gBACxB,qCACA,uBAAuB;gBACvB,YAAY,aAAa;gBAEtB,OAAO;oBAAE,iBAAiB;gBAA8B;;;;;;;;;;;;AAGnE;GA7GM;;QAqBW,qIAAA,CAAA,YAAS;;;MArBpB", "debugId": null}}, {"offset": {"line": 494, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/shiny-button.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { motion } from \"framer-motion\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface ShinyButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  children: React.ReactNode;\n  className?: string;\n  size?: \"sm\" | \"md\" | \"lg\";\n  variant?: \"primary\" | \"secondary\" | \"accent\";\n  shimmerColor?: string;\n  backgroundColor?: string;\n  textColor?: string;\n}\n\nconst animationProps = {\n  initial: { \"--x\": \"100%\", scale: 0.8 },\n  animate: { \"--x\": \"-100%\", scale: 1 },\n  whileTap: { scale: 0.95 },\n  transition: {\n    repeat: Infinity,\n    repeatType: \"loop\" as const,\n    repeatDelay: 1,\n    type: \"spring\" as const,\n    stiffness: 20,\n    damping: 15,\n    mass: 2,\n    scale: {\n      type: \"spring\" as const,\n      stiffness: 200,\n      damping: 5,\n      mass: 0.5,\n    },\n  },\n};\n\nexport const ShinyButton: React.FC<ShinyButtonProps> = ({\n  children,\n  className,\n  size = \"md\",\n  variant = \"primary\",\n  shimmerColor,\n  backgroundColor,\n  textColor,\n  ...props\n}) => {\n  // Size-based styling\n  const sizeClasses = {\n    sm: \"px-3 py-2 text-sm rounded-md\",\n    md: \"px-6 py-2 text-base rounded-lg\",\n    lg: \"px-8 py-3 text-lg rounded-lg\"\n  };\n\n  // Color variants with customization support - THEME-AWARE COLORS\n  const getColors = () => {\n    const baseColors = {\n      primary: {\n        // Light Mode: Vierla Forest, Dark Mode: Vierla Gold\n        bg: backgroundColor || \"var(--theme-primary, #364035)\",\n        shimmer: shimmerColor || \"#F4F1E8\",  /* Warm Cream - Shimmer effect */\n        text: textColor || \"#F4F1E8\"         /* Warm Cream - Text on primary */\n      },\n      secondary: {\n        // Light Mode: Vierla Sage, Dark Mode: Muted Gold/Champagne\n        bg: backgroundColor || \"var(--theme-secondary, #8B9A8C)\",\n        shimmer: shimmerColor || \"#F4F1E8\",  /* Warm Cream - Shimmer effect */\n        text: textColor || \"#F4F1E8\"         /* Warm Cream - Text on secondary */\n      },\n      accent: {\n        // Light Mode: Vierla Forest, Dark Mode: Vierla Gold\n        bg: backgroundColor || \"var(--theme-primary, #364035)\",\n        shimmer: shimmerColor || \"var(--theme-secondary, #8B9A8C)\",\n        text: textColor || \"#F4F1E8\"         /* Warm Cream - Text on accent */\n      }\n    };\n    return baseColors[variant];\n  };\n\n  const colors = getColors();\n\n  const {\n    onClick,\n    onMouseEnter,\n    onMouseLeave,\n    onFocus,\n    onBlur,\n    disabled,\n    type,\n    id,\n    name,\n    value\n  } = props;\n\n  return (\n    <motion.button\n      {...animationProps}\n      onClick={onClick}\n      onMouseEnter={onMouseEnter}\n      onMouseLeave={onMouseLeave}\n      onFocus={onFocus}\n      onBlur={onBlur}\n      disabled={disabled}\n      type={type}\n      id={id}\n      name={name}\n      value={value}\n      className={cn(\n        \"relative font-medium backdrop-blur-xl transition-shadow duration-300 ease-in-out hover:shadow\",\n        \"dark:hover:shadow-[0_0_20px_hsl(var(--primary)/10%)]\",\n        \"flex items-center justify-center\",\n        sizeClasses[size],\n        className\n      )}\n      style={{\n        backgroundColor: colors.bg,\n        \"--primary\": colors.shimmer,\n      } as React.CSSProperties}\n    >\n      <span\n        className=\"relative flex items-center justify-center size-full uppercase tracking-wide dark:font-light\"\n        style={{\n          color: colors.text,\n          maskImage:\n            `linear-gradient(-75deg,${colors.shimmer} calc(var(--x) + 20%),transparent calc(var(--x) + 30%),${colors.shimmer} calc(var(--x) + 100%))`,\n        }}\n      >\n        {children}\n      </span>\n      <span\n        style={{\n          mask: \"linear-gradient(var(--mask-black), var(--mask-black)) content-box,linear-gradient(var(--mask-black), var(--mask-black))\",\n          maskComposite: \"exclude\",\n          background: `linear-gradient(-75deg,${colors.shimmer}10 calc(var(--x)+20%),${colors.shimmer}80 calc(var(--x)+25%),${colors.shimmer}10 calc(var(--x)+100%))`,\n        }}\n        className=\"absolute inset-0 z-10 block rounded-[inherit] p-px\"\n      ></span>\n    </motion.button>\n  );\n};\n\nexport default ShinyButton;\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAiBA,MAAM,iBAAiB;IACrB,SAAS;QAAE,OAAO;QAAQ,OAAO;IAAI;IACrC,SAAS;QAAE,OAAO;QAAS,OAAO;IAAE;IACpC,UAAU;QAAE,OAAO;IAAK;IACxB,YAAY;QACV,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;QACN,OAAO;YACL,MAAM;YACN,WAAW;YACX,SAAS;YACT,MAAM;QACR;IACF;AACF;AAEO,MAAM,cAA0C;QAAC,EACtD,QAAQ,EACR,SAAS,EACT,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,YAAY,EACZ,eAAe,EACf,SAAS,EACT,GAAG,OACJ;IACC,qBAAqB;IACrB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,iEAAiE;IACjE,MAAM,YAAY;QAChB,MAAM,aAAa;YACjB,SAAS;gBACP,oDAAoD;gBACpD,IAAI,mBAAmB;gBACvB,SAAS,gBAAgB;gBAAY,+BAA+B,GACpE,MAAM,aAAa,UAAkB,gCAAgC;YACvE;YACA,WAAW;gBACT,2DAA2D;gBAC3D,IAAI,mBAAmB;gBACvB,SAAS,gBAAgB;gBAAY,+BAA+B,GACpE,MAAM,aAAa,UAAkB,kCAAkC;YACzE;YACA,QAAQ;gBACN,oDAAoD;gBACpD,IAAI,mBAAmB;gBACvB,SAAS,gBAAgB;gBACzB,MAAM,aAAa,UAAkB,+BAA+B;YACtE;QACF;QACA,OAAO,UAAU,CAAC,QAAQ;IAC5B;IAEA,MAAM,SAAS;IAEf,MAAM,EACJ,OAAO,EACP,YAAY,EACZ,YAAY,EACZ,OAAO,EACP,MAAM,EACN,QAAQ,EACR,IAAI,EACJ,EAAE,EACF,IAAI,EACJ,KAAK,EACN,GAAG;IAEJ,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACX,GAAG,cAAc;QAClB,SAAS;QACT,cAAc;QACd,cAAc;QACd,SAAS;QACT,QAAQ;QACR,UAAU;QACV,MAAM;QACN,IAAI;QACJ,MAAM;QACN,OAAO;QACP,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,iGACA,wDACA,oCACA,WAAW,CAAC,KAAK,EACjB;QAEF,OAAO;YACL,iBAAiB,OAAO,EAAE;YAC1B,aAAa,OAAO,OAAO;QAC7B;;0BAEA,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,OAAO,OAAO,IAAI;oBAClB,WACE,AAAC,0BAAiG,OAAxE,OAAO,OAAO,EAAC,2DAAwE,OAAf,OAAO,OAAO,EAAC;gBACrH;0BAEC;;;;;;0BAEH,6LAAC;gBACC,OAAO;oBACL,MAAM;oBACN,eAAe;oBACf,YAAY,AAAC,0BAAgE,OAAvC,OAAO,OAAO,EAAC,0BAA+D,OAAvC,OAAO,OAAO,EAAC,0BAAuC,OAAf,OAAO,OAAO,EAAC;gBACrI;gBACA,WAAU;;;;;;;;;;;;AAIlB;KAtGa;uCAwGE", "debugId": null}}, {"offset": {"line": 629, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/text-shimmer.tsx"], "sourcesContent": ["'use client';\nimport React, { useMemo, type JSX } from 'react';\nimport { motion } from 'framer-motion';\nimport { cn } from '@/lib/utils';\n\ninterface TextShimmerProps {\n  children: string;\n  as?: React.ElementType;\n  className?: string;\n  duration?: number;\n  spread?: number;\n}\n\nexport const TextShimmer = React.memo(function TextShimmer({\n  children,\n  as: Component = 'p',\n  className,\n  duration = 2,\n  spread = 2,\n}: TextShimmerProps) {\n  const MotionComponent = motion.create(Component as keyof JSX.IntrinsicElements);\n\n  const dynamicSpread = useMemo(() => {\n    return children.length * spread;\n  }, [children, spread]);\n\n  return (\n    <MotionComponent\n      className={cn(\n        'relative inline-block bg-[length:250%_100%,auto] bg-clip-text',\n        'text-transparent [--base-color:var(--global-text-shimmer-base)] [--base-gradient-color:var(--global-text-shimmer-highlight)]',\n        '[--bg:linear-gradient(90deg,#0000_calc(50%-var(--spread)),var(--base-gradient-color),#0000_calc(50%+var(--spread)))] [background-repeat:no-repeat,padding-box]',\n        'dark:[--base-color:var(--global-text-shimmer-base)] dark:[--base-gradient-color:var(--global-text-shimmer-highlight)]',\n        className\n      )}\n      initial={{ backgroundPosition: '100% center' }}\n      animate={{ backgroundPosition: '0% center' }}\n      transition={{\n        repeat: Infinity,\n        duration,\n        ease: 'linear',\n      }}\n      style={\n        {\n          '--spread': `${dynamicSpread}px`,\n          backgroundImage: `var(--bg), linear-gradient(var(--base-color), var(--base-color))`,\n        } as React.CSSProperties\n      }\n    >\n      {children}\n    </MotionComponent>\n  );\n});\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;AAHA;;;;AAaO,MAAM,4BAAc,GAAA,6JAAA,CAAA,UAAK,CAAC,IAAI,SAAC,SAAS,YAAY,KAMxC;QANwC,EACzD,QAAQ,EACR,IAAI,YAAY,GAAG,EACnB,SAAS,EACT,WAAW,CAAC,EACZ,SAAS,CAAC,EACO,GANwC;;IAOzD,MAAM,kBAAkB,6LAAA,CAAA,SAAM,CAAC,MAAM,CAAC;IAEtC,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;0DAAE;YAC5B,OAAO,SAAS,MAAM,GAAG;QAC3B;yDAAG;QAAC;QAAU;KAAO;IAErB,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,iEACA,gIACA,kKACA,yHACA;QAEF,SAAS;YAAE,oBAAoB;QAAc;QAC7C,SAAS;YAAE,oBAAoB;QAAY;QAC3C,YAAY;YACV,QAAQ;YACR;YACA,MAAM;QACR;QACA,OACE;YACE,YAAY,AAAC,GAAgB,OAAd,eAAc;YAC7B,iBAAkB;QACpB;kBAGD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 691, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/word-pull-up.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion, Variants } from \"framer-motion\";\nimport { JSX } from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\ninterface WordPullUpProps {\n  words: string;\n  delayMultiple?: number;\n  wrapperFramerProps?: Variants;\n  framerProps?: Variants;\n  className?: string;\n  delay?: number;\n  as?: keyof JSX.IntrinsicElements;\n}\n\nfunction WordPullUp({\n  words,\n  wrapperFramerProps = {\n    hidden: { opacity: 0 },\n    show: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.2,\n      },\n    },\n  },\n  framerProps = {\n    hidden: { y: 20, opacity: 0 },\n    show: { y: 0, opacity: 1 },\n  },\n  className,\n  delay = 0,\n  as = \"h1\",\n}: WordPullUpProps) {\n  const MotionComponent = motion.create(as as keyof JSX.IntrinsicElements);\n\n  return (\n    <MotionComponent\n      variants={wrapperFramerProps}\n      initial=\"hidden\"\n      animate=\"show\"\n      transition={{ delay }}\n      className={cn(\n        \"font-display text-center text-4xl font-bold leading-[5rem] tracking-[-0.02em] drop-shadow-sm text-white\",\n        className,\n      )}\n    >\n      {words.split(\" \").map((word, i) => (\n        <motion.span\n          key={i}\n          variants={framerProps}\n          style={{ display: \"inline-block\", paddingRight: \"8px\" }}\n        >\n          {word === \"\" ? <span>&nbsp;</span> : word}\n        </motion.span>\n      ))}\n    </MotionComponent>\n  );\n}\n\nexport { WordPullUp };\n"], "names": [], "mappings": ";;;;AAEA;AAGA;AALA;;;;AAiBA,SAAS,WAAW,KAkBF;QAlBE,EAClB,KAAK,EACL,qBAAqB;QACnB,QAAQ;YAAE,SAAS;QAAE;QACrB,MAAM;YACJ,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF,CAAC,EACD,cAAc;QACZ,QAAQ;YAAE,GAAG;YAAI,SAAS;QAAE;QAC5B,MAAM;YAAE,GAAG;YAAG,SAAS;QAAE;IAC3B,CAAC,EACD,SAAS,EACT,QAAQ,CAAC,EACT,KAAK,IAAI,EACO,GAlBE;IAmBlB,MAAM,kBAAkB,6LAAA,CAAA,SAAM,CAAC,MAAM,CAAC;IAEtC,qBACE,6LAAC;QACC,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,YAAY;YAAE;QAAM;QACpB,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,2GACA;kBAGD,MAAM,KAAK,CAAC,KAAK,GAAG,CAAC,CAAC,MAAM,kBAC3B,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;gBAEV,UAAU;gBACV,OAAO;oBAAE,SAAS;oBAAgB,cAAc;gBAAM;0BAErD,SAAS,mBAAK,6LAAC;8BAAK;;;;;2BAAgB;eAJhC;;;;;;;;;;AASf;KA3CS", "debugId": null}}, {"offset": {"line": 768, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/morphing-text.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useCallback, useEffect, useRef } from \"react\";\n\nimport { cn } from \"@/lib/utils\";\n\nconst morphTime = 3.38; // Slowed down by 30% more (from 2.6 to 3.38)\nconst cooldownTime = 1.13; // Proportionally increased for smoother transitions\n\nconst useMorphingText = (texts: string[]) => {\n  const textIndexRef = useRef(0);\n  const morphRef = useRef(0);\n  const cooldownRef = useRef(0);\n  const timeRef = useRef(new Date());\n\n  const text1Ref = useRef<HTMLSpanElement>(null);\n  const text2Ref = useRef<HTMLSpanElement>(null);\n\n  const setStyles = useCallback(\n    (fraction: number) => {\n      const [current1, current2] = [text1Ref.current, text2Ref.current];\n      if (!current1 || !current2) return;\n\n      current2.style.filter = `blur(${Math.min(8 / fraction - 8, 100)}px)`;\n      current2.style.opacity = `${Math.pow(fraction, 0.4) * 100}%`;\n\n      const invertedFraction = 1 - fraction;\n      current1.style.filter = `blur(${Math.min(8 / invertedFraction - 8, 100)}px)`;\n      current1.style.opacity = `${Math.pow(invertedFraction, 0.4) * 100}%`;\n\n      current1.textContent = texts[textIndexRef.current % texts.length];\n      current2.textContent = texts[(textIndexRef.current + 1) % texts.length];\n    },\n    [texts],\n  );\n\n  const doMorph = useCallback(() => {\n    morphRef.current -= cooldownRef.current;\n    cooldownRef.current = 0;\n\n    let fraction = morphRef.current / morphTime;\n\n    if (fraction > 1) {\n      cooldownRef.current = cooldownTime;\n      fraction = 1;\n    }\n\n    setStyles(fraction);\n\n    if (fraction === 1) {\n      textIndexRef.current++;\n    }\n  }, [setStyles]);\n\n  const doCooldown = useCallback(() => {\n    morphRef.current = 0;\n    const [current1, current2] = [text1Ref.current, text2Ref.current];\n    if (current1 && current2) {\n      current2.style.filter = \"none\";\n      current2.style.opacity = \"100%\";\n      current1.style.filter = \"none\";\n      current1.style.opacity = \"0%\";\n    }\n  }, []);\n\n  useEffect(() => {\n    let animationFrameId: number;\n\n    const animate = () => {\n      animationFrameId = requestAnimationFrame(animate);\n\n      const newTime = new Date();\n      const dt = (newTime.getTime() - timeRef.current.getTime()) / 1000;\n      timeRef.current = newTime;\n\n      cooldownRef.current -= dt;\n\n      if (cooldownRef.current <= 0) doMorph();\n      else doCooldown();\n    };\n\n    animate();\n    return () => {\n      cancelAnimationFrame(animationFrameId);\n    };\n  }, [doMorph, doCooldown]);\n\n  return { text1Ref, text2Ref };\n};\n\ninterface MorphingTextProps {\n  className?: string;\n  texts: string[];\n}\n\nconst Texts: React.FC<Pick<MorphingTextProps, \"texts\">> = ({ texts }) => {\n  const { text1Ref, text2Ref } = useMorphingText(texts);\n  return (\n    <>\n      <span\n        className=\"absolute inset-x-0 top-0 m-auto inline-block w-full\"\n        ref={text1Ref}\n      />\n      <span\n        className=\"absolute inset-x-0 top-0 m-auto inline-block w-full\"\n        ref={text2Ref}\n      />\n    </>\n  );\n};\n\nconst SvgFilters: React.FC = () => (\n  <svg id=\"filters\" className=\"hidden\" preserveAspectRatio=\"xMidYMid slice\">\n    <defs>\n      <filter id=\"threshold\">\n        <feColorMatrix\n          in=\"SourceGraphic\"\n          type=\"matrix\"\n          values=\"1 0 0 0 0\n                  0 1 0 0 0\n                  0 0 1 0 0\n                  0 0 0 255 -140\"\n        />\n      </filter>\n    </defs>\n  </svg>\n);\n\nconst MorphingText: React.FC<MorphingTextProps> = ({ texts, className }) => (\n  <div\n    className={cn(\n      \"relative mx-auto h-16 w-full max-w-screen-md text-center font-sans font-bold leading-none [filter:url(#threshold)_blur(0.6px)] md:h-24\",\n      className,\n    )}\n  >\n    <Texts texts={texts} />\n    <SvgFilters />\n  </div>\n);\n\nexport { MorphingText };\n"], "names": [], "mappings": ";;;;AAEA;AAEA;;;AAJA;;;AAMA,MAAM,YAAY,MAAM,6CAA6C;AACrE,MAAM,eAAe,MAAM,oDAAoD;AAE/E,MAAM,kBAAkB,CAAC;;IACvB,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC5B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC3B,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,IAAI;IAE3B,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAmB;IACzC,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAmB;IAEzC,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAC1B,CAAC;YACC,MAAM,CAAC,UAAU,SAAS,GAAG;gBAAC,SAAS,OAAO;gBAAE,SAAS,OAAO;aAAC;YACjE,IAAI,CAAC,YAAY,CAAC,UAAU;YAE5B,SAAS,KAAK,CAAC,MAAM,GAAG,AAAC,QAAuC,OAAhC,KAAK,GAAG,CAAC,IAAI,WAAW,GAAG,MAAK;YAChE,SAAS,KAAK,CAAC,OAAO,GAAG,AAAC,GAAgC,OAA9B,KAAK,GAAG,CAAC,UAAU,OAAO,KAAI;YAE1D,MAAM,mBAAmB,IAAI;YAC7B,SAAS,KAAK,CAAC,MAAM,GAAG,AAAC,QAA+C,OAAxC,KAAK,GAAG,CAAC,IAAI,mBAAmB,GAAG,MAAK;YACxE,SAAS,KAAK,CAAC,OAAO,GAAG,AAAC,GAAwC,OAAtC,KAAK,GAAG,CAAC,kBAAkB,OAAO,KAAI;YAElE,SAAS,WAAW,GAAG,KAAK,CAAC,aAAa,OAAO,GAAG,MAAM,MAAM,CAAC;YACjE,SAAS,WAAW,GAAG,KAAK,CAAC,CAAC,aAAa,OAAO,GAAG,CAAC,IAAI,MAAM,MAAM,CAAC;QACzE;iDACA;QAAC;KAAM;IAGT,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;gDAAE;YAC1B,SAAS,OAAO,IAAI,YAAY,OAAO;YACvC,YAAY,OAAO,GAAG;YAEtB,IAAI,WAAW,SAAS,OAAO,GAAG;YAElC,IAAI,WAAW,GAAG;gBAChB,YAAY,OAAO,GAAG;gBACtB,WAAW;YACb;YAEA,UAAU;YAEV,IAAI,aAAa,GAAG;gBAClB,aAAa,OAAO;YACtB;QACF;+CAAG;QAAC;KAAU;IAEd,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE;YAC7B,SAAS,OAAO,GAAG;YACnB,MAAM,CAAC,UAAU,SAAS,GAAG;gBAAC,SAAS,OAAO;gBAAE,SAAS,OAAO;aAAC;YACjE,IAAI,YAAY,UAAU;gBACxB,SAAS,KAAK,CAAC,MAAM,GAAG;gBACxB,SAAS,KAAK,CAAC,OAAO,GAAG;gBACzB,SAAS,KAAK,CAAC,MAAM,GAAG;gBACxB,SAAS,KAAK,CAAC,OAAO,GAAG;YAC3B;QACF;kDAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI;YAEJ,MAAM;qDAAU;oBACd,mBAAmB,sBAAsB;oBAEzC,MAAM,UAAU,IAAI;oBACpB,MAAM,KAAK,CAAC,QAAQ,OAAO,KAAK,QAAQ,OAAO,CAAC,OAAO,EAAE,IAAI;oBAC7D,QAAQ,OAAO,GAAG;oBAElB,YAAY,OAAO,IAAI;oBAEvB,IAAI,YAAY,OAAO,IAAI,GAAG;yBACzB;gBACP;;YAEA;YACA;6CAAO;oBACL,qBAAqB;gBACvB;;QACF;oCAAG;QAAC;QAAS;KAAW;IAExB,OAAO;QAAE;QAAU;IAAS;AAC9B;GA/EM;AAsFN,MAAM,QAAoD;QAAC,EAAE,KAAK,EAAE;;IAClE,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,gBAAgB;IAC/C,qBACE;;0BACE,6LAAC;gBACC,WAAU;gBACV,KAAK;;;;;;0BAEP,6LAAC;gBACC,WAAU;gBACV,KAAK;;;;;;;;AAIb;IAdM;;QAC2B;;;KAD3B;AAgBN,MAAM,aAAuB,kBAC3B,6LAAC;QAAI,IAAG;QAAU,WAAU;QAAS,qBAAoB;kBACvD,cAAA,6LAAC;sBACC,cAAA,6LAAC;gBAAO,IAAG;0BACT,cAAA,6LAAC;oBACC,IAAG;oBACH,MAAK;oBACL,QAAO;;;;;;;;;;;;;;;;;;;;;MAPX;AAiBN,MAAM,eAA4C;QAAC,EAAE,KAAK,EAAE,SAAS,EAAE;yBACrE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,0IACA;;0BAGF,6LAAC;gBAAM,OAAO;;;;;;0BACd,6LAAC;;;;;;;;;;;;MARC", "debugId": null}}]}