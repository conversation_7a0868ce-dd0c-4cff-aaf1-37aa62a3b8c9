import type { Config } from "tailwindcss";
import plugin from "tailwindcss/plugin";

// Helper function to flatten the color palette for CSS variable generation
function flattenColorPalette(colors: any): Record<string, string> {
  const result: Record<string, string> = {};

  function flatten(obj: any, prefix = "") {
    for (const [key, value] of Object.entries(obj)) {
      const newKey = prefix ? `${prefix}-${key}` : key;
      if (typeof value === "object" && value !== null && !Array.isArray(value)) {
        flatten(value, newKey);
      } else {
        result[newKey] = value as string;
      }
    }
  }

  flatten(colors);
  return result;
}

// ========================================
// 🌌 AURORA BACKGROUND CENTRALIZED CONTROL SYSTEM - v1.5.9
// ========================================

const config: Config = {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "./hooks/**/*.{js,ts,jsx,tsx,mdx}",
    "./lib/**/*.{js,ts,jsx,tsx,mdx}",
    "*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      // ========================================
      // 🌌 AURORA BACKGROUND THEME CONFIGURATION
      // ========================================
      aurora: {
        light: {
          // Natural Wellness Aurora: White background with soft, natural beauty tones
          background: "#FFFFFF", // Pure white base
          flow: {
            color1: "rgba(255, 245, 235, 0.4)", // Soft Peach/Cream
            color2: "rgba(240, 248, 255, 0.3)", // Soft Blue/White
            color3: "rgba(245, 255, 250, 0.35)", // Soft Mint/White
            color4: "rgba(255, 250, 240, 0.3)", // Soft Ivory
            color5: "rgba(248, 250, 252, 0.25)", // Soft Gray/White
          }
        },
        dark: {
          // Modern Luxury Aurora: Deep Charcoal, Vierla Gold, Deep Plum/Violet
          background: "#2D2A26", // Deep Charcoal base
          flow: {
            color1: "rgba(45, 42, 38, 0.45)", // Deep Charcoal variation
            color2: "rgba(184, 149, 106, 0.35)", // Vierla Gold
            color3: "rgba(75, 61, 107, 0.30)", // Deep Plum/Violet for luxury
            color4: "rgba(169, 162, 153, 0.25)", // Muted Gold/Champagne
            color5: "rgba(45, 42, 38, 0.40)", // Deep charcoal secondary
          }
        },
        // Feature Section Aurora: Natural Wellness & Modern Luxury themes
        feature: {
          light: {
            // Natural Wellness Feature Aurora: White background with even softer tones
            background: "#FFFFFF", // Pure white base
            flow: {
              color1: "rgba(255, 245, 235, 0.2)", // Very soft Peach/Cream
              color2: "rgba(240, 248, 255, 0.15)", // Very soft Blue/White
              color3: "rgba(245, 255, 250, 0.18)", // Very soft Mint/White
              color4: "rgba(255, 250, 240, 0.15)", // Very soft Ivory
              color5: "rgba(248, 250, 252, 0.12)", // Very soft Gray/White
            }
          },
          dark: {
            // Modern Luxury Feature Aurora: Subtle luxury blend
            background: "#2D2A26", // Deep Charcoal base
            flow: {
              color1: "rgba(45, 42, 38, 0.35)", // Deep Charcoal
              color2: "rgba(184, 149, 106, 0.25)", // Vierla Gold
              color3: "rgba(75, 61, 107, 0.20)", // Deep Plum/Violet
              color4: "rgba(169, 162, 153, 0.18)", // Muted Gold/Champagne
              color5: "rgba(45, 42, 38, 0.30)", // Deep charcoal variation
            }
          }
        }
      },
      fontFamily: {
        // Define 'sans' as the primary body font (Inter) - Updated to new specs
        sans: ["var(--font-inter)", "Inter", "system-ui", "sans-serif"],
        // Define 'serif' as the secondary heading font (Playfair Display)
        serif: ["var(--font-tai-heritage)", "Georgia", "serif"],
        // Define 'playfair' for H1/H2 headlines - New primary headline font (Playfair Display)
        playfair: ["var(--font-playfair)", "Playfair Display", "Georgia", "serif"],
        // Define 'inter' for body text and UI elements
        inter: ["var(--font-inter)", "Inter", "system-ui", "sans-serif"],
        // Define 'tai-heritage' for H3 section titles (also Playfair Display)
        "tai-heritage": ["var(--font-tai-heritage)", "Playfair Display", "Georgia", "serif"],
        // Define 'farsan' for optional accent text
        farsan: ["var(--font-farsan)", "cursive"],
      },
      spacing: {
        // 8-point grid system
        '1': 'var(--space-1)',   // 8px
        '2': 'var(--space-2)',   // 16px
        '3': 'var(--space-3)',   // 24px
        '4': 'var(--space-4)',   // 32px
        '5': 'var(--space-5)',   // 40px
        '6': 'var(--space-6)',   // 48px
        '7': 'var(--space-7)',   // 56px
        '8': 'var(--space-8)',   // 64px
        '9': 'var(--space-9)',   // 72px
        '10': 'var(--space-10)', // 80px
        '12': 'var(--space-12)', // 96px
        '16': 'var(--space-16)', // 128px
        '20': 'var(--space-20)', // 160px
        '24': 'var(--space-24)', // 192px
      },
      colors: {
        // ========================================
        // 🎨 DUAL-THEME COLOR SYSTEM - Natural Wellness & Modern Luxury
        // ========================================

        // ========================================
        // LIGHT MODE: "Natural Wellness" Theme - Green, Cream, Charcoal
        // ========================================
        "natural-wellness": {
          background: "#F4F1E8",        // Warm Cream - Main page backgrounds
          "text-primary": "#2D2A26",    // Deep Charcoal - Headlines and body copy
          "accent-primary": "#364035",  // Vierla Forest - Primary CTAs, active nav
          "accent-secondary": "#8B9A8C", // Vierla Sage - Secondary buttons, sub-headings
          "accent-energetic": "#7C9A85", // Energetic Green - Aurora background only
        },

        // ========================================
        // DARK MODE: "Modern Luxury" Theme - Gold, Charcoal
        // ========================================
        "modern-luxury": {
          background: "#2D2A26",        // Deep Charcoal - Main page backgrounds
          "text-primary": "#F4F1E8",    // Warm Cream - Headlines and body copy
          "accent-primary": "#B8956A",  // Vierla Gold - Primary CTAs, active nav, key accents
          "accent-secondary": "#A9A299", // Muted Gold/Champagne - Secondary buttons, captions
        },

        // ========================================
        // LEGACY BRAND COLORS (Backward Compatibility)
        // ========================================
        "vierla-sage": "#8B9A8C",      // Updated: Vierla-Sage for secondary accents
        "vierla-gold": "#B8956A",      // Vierla-Gold for primary CTAs and highlights
        "vierla-forest": "#364035",    // New: Vierla-Forest for dark accents
        "brand-sage": "#8B9A8C",       // Alias for backward compatibility
        "brand-gold": "#B8956A",       // Alias for backward compatibility
        "brand-beige": "#F0E6D9",      // Highlight & accent background

        // Neutral Colors (Light Mode) - New Specifications
        "neutral-background-light": "#F4F1E8",    // Warm Cream - Primary light background
        "neutral-text-primary-light": "#2D2A26",  // Deep Charcoal - Primary text on light
        "neutral-text-secondary-light": "#8B9A8C", // Sage Green - Secondary text on light
        "neutral-borders-light": "#B7C4B7",       // Muted Sage - Borders and dividers

        // Neutral Colors (Dark Mode) - New Specifications
        "neutral-background-dark": "#2D2A26",     // Deep Charcoal - Primary dark background
        "neutral-text-primary-dark": "#F4F1E8",   // Warm Cream - Primary text on dark
        "neutral-text-secondary-dark": "#8B9A8C", // Sage Green - Secondary text on dark
        "neutral-borders-dark": "#657D6D",        // Deeper Sage - Borders in dark mode

        // Legacy Neutral Colors (for backward compatibility)
        "neutral-charcoal-dark": "#2D2A26",   // Updated to match new Deep Charcoal
        "neutral-charcoal-light": "#333333",  // Surface & card backgrounds
        "neutral-off-white": "#F4F1E8",       // Updated to match new Warm Cream

        // THEME-AWARE BRAND PALETTE - STRICT SEPARATION
        "theme-primary": {
          // Light Mode: Vierla Forest, Dark Mode: Vierla Gold
          DEFAULT: "#364035", // Light mode default
          light: "#364035",   // Vierla Forest for light mode
          dark: "#B8956A",    // Vierla Gold for dark mode
        },
        "theme-secondary": {
          // Light Mode: Vierla Sage, Dark Mode: Muted Gold/Champagne
          DEFAULT: "#8B9A8C", // Light mode default
          light: "#8B9A8C",   // Vierla Sage for light mode
          dark: "#A9A299",    // Muted Gold/Champagne for dark mode
        },

        // LEGACY COLORS (for backward compatibility - will be phased out)
        "sage": {
          DEFAULT: "#8B9A8C", // LIGHT MODE ONLY - will show gold in dark mode via CSS
          50: "#F5F7F6",
          100: "#EAEFEB",
          200: "#D4DFD6",
          300: "#BECFC1",
          400: "#A8BFAC",
          500: "#8B9A8C", // Main Vierla-Sage
          600: "#7A8A7C",
          700: "#697A6B",
          800: "#586A5A",
          900: "#475A49",
        },
        "gold": {
          DEFAULT: "#B8956A", // Muted Gold/Tan - Primary action color
          50: "#FAF7F2",
          100: "#F5EFE5",
          200: "#EBDFCB",
          300: "#E1CFB1",
          400: "#D7BF97",
          500: "#B8956A", // Main gold
          600: "#A6845C",
          700: "#94734E",
          800: "#826240",
          900: "#705132",
        },
        "forest": {
          DEFAULT: "#364035", // New: Vierla-Forest - Dark accent color
          50: "#F4F5F4",
          100: "#E8EAE8",
          200: "#D1D5D1",
          300: "#BAC0BA",
          400: "#A3AAA3",
          500: "#8C948C",
          600: "#757D75",
          700: "#5E665E",
          800: "#475047",
          900: "#364035", // Main Vierla-Forest
          950: "#2A302A",
        },
        "beige": {
          DEFAULT: "#F0E6D9", // Warm Beige
          50: "#FEFCFA",
          100: "#F0E6D9", // Main beige
          200: "#E0D5C7",
          300: "#D0C5B7",
          400: "#C0B5A7",
          500: "#B0A597",
          600: "#A09587",
          700: "#908577",
          800: "#807567",
          900: "#706557",
        },
        "charcoal": {
          DEFAULT: "#2C3137", // Dark Charcoal
          50: "#F5F6F6",
          100: "#E6E8E9",
          200: "#CDD1D3",
          300: "#B4BABD",
          400: "#9BA3A7",
          500: "#828C91",
          600: "#69757B",
          700: "#505E65",
          800: "#37474F",
          900: "#333333", // Light Charcoal
          950: "#2C3137", // Dark Charcoal
        },

        // Semantic State Colors
        "success": {
          DEFAULT: "#7C9A85", // Use brand sage for success
          50: "#F4F7F5",
          100: "#E8EFEA",
          200: "#D1DFD4",
          300: "#BACFBE",
          400: "#A3BFA8",
          500: "#7C9A85",
          600: "#6B8A74",
          700: "#5A7963",
          800: "#496852",
          900: "#3A5441",
        },
        "warning": {
          DEFAULT: "#B8956A", // Use brand gold for warnings
          50: "#FAF7F2",
          100: "#F5EFE5",
          200: "#EBDFCB",
          300: "#E1CFB1",
          400: "#D7BF97",
          500: "#B8956A",
          600: "#A6845C",
          700: "#94734E",
          800: "#826240",
          900: "#705132",
        },
        "error": {
          DEFAULT: "#D97706", // Terracotta for errors
          50: "#FEF7ED",
          100: "#FDEDD3",
          200: "#FBD5A5",
          300: "#F9BC6D",
          400: "#F59E0B",
          500: "#D97706", // Main error
          600: "#C2410C",
          700: "#9A3412",
          800: "#7C2D12",
          900: "#431407",
        },

        // Legacy Compatibility Colors (Maintained for existing components)
        "mantle": {
          "50": "#F5FAF7",   // Light Off-White
          "100": "#F0E6D9",  // Warm Beige
          "200": "#E0D5C7",
          "300": "#D0C5B7",
          "400": "#7C9A85",  // Sage Green
          "500": "#6B8A74",
          "600": "#5A7963",
          "700": "#496852",
          "800": "#3A5441",
          "900": "#333333",  // Light Charcoal
          "950": "#2C3137", // Dark Charcoal
        },

        // Utility Colors
        white: "#ffffff",
        black: "#000000",
        transparent: "transparent",
        // ========================================
        // 🎨 SHADCN UI INTEGRATION - CSS Variable Based
        // ========================================
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        aurora: {
          "0%": { backgroundPosition: "50% 50%, 50% 50%" },
          "25%": { backgroundPosition: "200% 25%, 200% 75%" },
          "50%": { backgroundPosition: "350% 50%, 350% 50%" },
          "75%": { backgroundPosition: "100% 75%, 100% 25%" },
          "100%": { backgroundPosition: "50% 50%, 50% 50%" },
        },
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
        "shimmer-slide": {
          to: {
            transform: "translate(calc(100cqw - 100%), 0)",
          },
        },
        "spin-around": {
          "0%": {
            transform: "translateZ(0) rotate(0)",
          },
          "15%, 35%": {
            transform: "translateZ(0) rotate(90deg)",
          },
          "65%, 85%": {
            transform: "translateZ(0) rotate(270deg)",
          },
          "100%": {
            transform: "translateZ(0) rotate(360deg)",
          },
        },
      },
      animation: {
        aurora: "aurora 33s ease-in-out infinite", // Slowed down by 33% from original 25s (25s * 1.33 = 33s)
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        "shimmer-slide": "shimmer-slide var(--speed) ease-in-out infinite alternate",
        "spin-around": "spin-around calc(var(--speed) * 2) infinite linear",
      },
    },
  },
  plugins: [
    require("tailwindcss-animate"),
    plugin(function ({ addBase, theme }) {
      // Flatten all colors to CSS variables for maximum flexibility
      const allColors = flattenColorPalette(theme("colors"));
      const newVars = Object.fromEntries(
        Object.entries(allColors).map(([key, val]) => [`--${key}`, val])
      );

      // Get aurora configuration from theme
      const auroraConfig = theme("aurora") as any;

      // Generate stripe gradients using background colors
      const generateStripeGradient = (bgColor: string) => {
        return `repeating-linear-gradient(100deg, ${bgColor} 0%, ${bgColor} 7%, transparent 10%, transparent 12%, ${bgColor} 16%)`;
      };

      // Generate flow gradients from color arrays
      const generateFlowGradient = (colors: any) => {
        return `repeating-linear-gradient(100deg, ${colors.color1} 10%, ${colors.color2} 15%, ${colors.color3} 20%, ${colors.color4} 25%, ${colors.color5} 30%)`;
      };

      addBase({
        ":root": {
          // All Tailwind colors as CSS variables
          ...newVars,

          // ========================================
          // 🌌 AURORA BACKGROUND DYNAMIC VARIABLES - Light Mode
          // ========================================
          "--aurora-bg": auroraConfig.light.background,
          "--aurora-stripes": generateStripeGradient(auroraConfig.light.background),
          "--aurora-flow": generateFlowGradient(auroraConfig.light.flow),

          // Feature Section Aurora Variables
          "--aurora-feature-bg": auroraConfig.feature.light.background,
          "--aurora-feature-stripes": generateStripeGradient(auroraConfig.feature.light.background),
          "--aurora-feature-flow": generateFlowGradient(auroraConfig.feature.light.flow),
        },
        ".dark": {
          // ========================================
          // 🌌 AURORA BACKGROUND DYNAMIC VARIABLES - Dark Mode
          // ========================================
          "--aurora-bg": auroraConfig.dark.background,
          "--aurora-stripes": generateStripeGradient(auroraConfig.dark.background),
          "--aurora-flow": generateFlowGradient(auroraConfig.dark.flow),

          // Feature Section Aurora Variables - Dark Mode
          "--aurora-feature-bg": auroraConfig.feature.dark.background,
          "--aurora-feature-stripes": generateStripeGradient(auroraConfig.feature.dark.background),
          "--aurora-feature-flow": generateFlowGradient(auroraConfig.feature.dark.flow),
        },
      });
    }),
  ],
};

export default config;
