import { <PERSON>ada<PERSON> } from 'next'
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AuroraBackgroundLayer } from "@/components/ui/aurora-background";
import { GoldenGlowingCardContainer } from "@/components/ui/golden-glowing-card-container";
import { BentoGrid, BentoCard } from "@/components/ui/bento-grid";
import { ShimmerButton } from "@/components/ui/shimmer-button";
import { StructuredData } from "@/components/seo/structured-data";
import { BreadcrumbSchema, getBreadcrumbsForPage } from "@/components/seo/breadcrumb-schema";
import { LayoutTemplate, FileText, Users, BarChart2, Calendar, CreditCard, Globe, Palette, Zap, Shield } from "lucide-react";
import Link from "next/link";

export const metadata: Metadata = {
  title: 'For Beauty Professionals - Grow Your Business',
  description: 'Join <PERSON> as a beauty professional. Access powerful business tools, connect with clients, and grow your beauty business with our all-in-one platform.',
  openGraph: {
    title: 'For Beauty Professionals - Grow Your Business | Vierla',
    description: 'Join <PERSON> as a beauty professional and access powerful tools to grow your business.',
    url: 'https://vierla.com/providers',
    siteName: 'Vierla',
    locale: 'en_CA',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'For Beauty Professionals - Grow Your Business | Vierla',
    description: 'Join Vierla as a beauty professional and access powerful tools to grow your business.',
  },
};

export default function ProviderApp() {
  return (
    <div className="page-provider relative overflow-hidden">
      <StructuredData page="providers" />
      <BreadcrumbSchema items={getBreadcrumbsForPage('providers')} />
      <AuroraBackgroundLayer variant="feature" />
      
      {/* Hero Section */}
      <section className="relative z-10 w-full px-4 py-20 pt-36">
        <div className="text-center max-w-6xl mx-auto">
          <h1 className="text-4xl md:text-6xl font-black mb-6 leading-none text-[#2D2A26] dark:text-[#F4F1E8] drop-shadow-lg font-sans">
            FOR BEAUTY PROFESSIONALS - EVERYTHING YOU NEED TO RUN YOUR BUSINESS
          </h1>
          <p className="text-xl md:text-2xl text-[var(--master-text-primary-light)] dark:text-[var(--master-text-primary-dark)] mb-8 leading-relaxed max-w-4xl mx-auto drop-shadow-sm font-inter">
            Join our curated network of top-tier beauty professionals. Build your business with powerful tools designed for your success.
          </p>
          
          <Link href="/apply" className="mb-8 inline-block">
            <ShimmerButton
              size="lg"
              background="#B8956A"
              shimmerColor="#F4F1E8"
              className="px-8 py-4 text-lg font-medium text-[var(--master-text-primary-light)]"
            >
              <span className="flex items-center">
                Apply to Join Vierla
                <FileText className="ml-2 w-5 h-5" />
              </span>
            </ShimmerButton>
          </Link>
        </div>
      </section>

      {/* Business Tools Section */}
      <section className="relative z-10 py-12 border-t border-white/20">
        <div className="container mx-auto px-4">
          <BentoGrid className="lg:grid-rows-2 max-w-6xl mx-auto min-h-[800px] mb-16">
            {[
              {
                Icon: Globe,
                name: "Digital Store & Website Builder",
                description: "Create stunning, professional websites and branded online presence with our AI-powered builder. Drag & drop interface, AI content generation, portfolio gallery, and custom domains.",
                href: "/contact",
                cta: "Learn More",
                className: "lg:row-start-1 lg:row-end-2 lg:col-start-1 lg:col-end-3",
              },
              {
                Icon: FileText,
                name: "Smart Invoicing",
                description: "Streamline your billing process with intelligent invoicing features. Automated reminders, multiple currencies, payment tracking, and professional templates.",
                href: "/contact",
                cta: "Learn More",
                className: "lg:row-start-1 lg:row-end-2 lg:col-start-3 lg:col-end-4",
              },
              {
                Icon: Users,
                name: "Integrated CRM",
                description: "Manage customer relationships and grow your business effectively. Customer database, sales pipeline, task automation, and journey tracking.",
                href: "/contact",
                cta: "Learn More",
                className: "lg:row-start-2 lg:row-end-3 lg:col-start-1 lg:col-end-2",
              },
              {
                Icon: BarChart2,
                name: "Business Analytics",
                description: "Track your performance with detailed insights on bookings, revenue, customer behavior, and growth trends. Revenue tracking, customer insights, booking analytics, and growth metrics.",
                href: "/contact",
                cta: "Learn More",
                className: "lg:row-start-2 lg:row-end-3 lg:col-start-2 lg:col-end-4",
              }
            ].map((tool) => (
              <BentoCard key={tool.name} {...tool} />
            ))}
          </BentoGrid>



          <BentoGrid className="lg:grid-rows-1 max-w-6xl mx-auto min-h-[400px]">
            {[
              {
                Icon: Calendar,
                name: "Booking Management",
                description: "Centralized appointment scheduling with calendar integration, automated reminders, and easy rescheduling. Calendar sync, auto reminders, easy rescheduling, and availability control.",
                href: "/contact",
                cta: "Learn More",
                className: "lg:row-start-1 lg:row-end-2 lg:col-start-1 lg:col-end-2",
              },
              {
                Icon: CreditCard,
                name: "Payment Processing",
                description: "Secure payment handling with multiple payment methods and direct deposits to your preferred account. Multiple payment methods, secure transactions, direct deposits, and transaction history.",
                href: "/contact",
                cta: "Learn More",
                className: "lg:row-start-1 lg:row-end-2 lg:col-start-2 lg:col-end-4",
              }
            ].map((feature) => (
              <BentoCard key={feature.name} {...feature} />
            ))}
          </BentoGrid>

          {/* Coming Soon Section */}
          <div className="text-center mt-16">
            <div className="max-w-4xl mx-auto">
              <GoldenGlowingCardContainer>
              <div className="text-center pt-4 pb-2">
                <h3 className="text-3xl md:text-4xl font-black text-light-off-white mb-4 drop-shadow-lg font-tai-heritage">COMING SOON</h3>
                <p className="text-lg md:text-xl text-warm-beige mb-6 max-w-2xl mx-auto drop-shadow-sm font-sans">
                  We're building something special for beauty professionals. Join our waitlist to be the first to know when we launch.
                </p>
                
                <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
                  <div className="flex items-center px-6 py-3 bg-charcoal/50 rounded-xl border border-neutral-off-white/20 cursor-not-allowed opacity-75">
                    <svg className="w-8 h-8 mr-3 text-neutral-off-white" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
                    </svg>
                    <div className="text-left">
                      <div className="text-xs text-brand-beige/70">Coming Soon to</div>
                      <div className="text-lg font-semibold text-neutral-off-white">Apple App Store</div>
                    </div>
                  </div>
                  <div className="flex items-center px-6 py-3 bg-charcoal/50 rounded-xl border border-neutral-off-white/20 cursor-not-allowed opacity-75">
                    <svg className="w-8 h-8 mr-3 text-neutral-off-white" viewBox="0 0 24 24" fill="currentColor">
                      <path d="M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.61 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z"/>
                    </svg>
                    <div className="text-left">
                      <div className="text-xs text-brand-beige/70">Coming Soon to</div>
                      <div className="text-lg font-semibold text-neutral-off-white">Google Play Store</div>
                    </div>
                  </div>
                </div>

                <div className="flex justify-center mt-8">
                  <Link href="/apply">
                    <ShimmerButton
                      size="lg"
                      background="#B8956A"
                      shimmerColor="#E5D4A1"
                      className="px-8 py-4 text-lg font-medium text-[#2D2A26]"
                    >
                      <span className="flex items-center">
                        Apply to Join Vierla
                        <FileText className="ml-2 w-5 h-5" />
                      </span>
                    </ShimmerButton>
                  </Link>
                </div>
              </div>
            </GoldenGlowingCardContainer>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
