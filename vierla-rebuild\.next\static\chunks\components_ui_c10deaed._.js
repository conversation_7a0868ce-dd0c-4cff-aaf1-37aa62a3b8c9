(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/components/ui/aurora-background.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "AuroraBackground": ()=>AuroraBackground,
    "AuroraBackgroundLayer": ()=>AuroraBackgroundLayer
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
"use client";
;
;
;
const AuroraBackground = (param)=>{
    let { className, children, showRadialGradient = true, variant = "hero", ...props } = param;
    // Determine which aurora variables to use based on variant
    const auroraVars = variant === "feature" ? {
        bg: "var(--aurora-feature-bg, var(--aurora-bg))",
        stripes: "var(--aurora-feature-stripes, var(--aurora-stripes))",
        flow: "var(--aurora-feature-flow, var(--aurora-flow))"
    } : {
        bg: "var(--aurora-bg)",
        stripes: "var(--aurora-stripes)",
        flow: "var(--aurora-flow)"
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("relative flex flex-col h-[100vh] items-center justify-center text-slate-950 transition-colors duration-300", className),
        style: {
            backgroundColor: auroraVars.bg
        },
        ...props,
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute inset-0 overflow-hidden",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('filter blur-[10px]\n            after:content-[""] after:absolute after:inset-0\n            after:[background-size:200%,_100%]\n            after:animate-aurora after:mix-blend-difference\n            pointer-events-none\n            absolute -inset-[10px] opacity-75 will-change-transform', showRadialGradient && "[mask-image:radial-gradient(ellipse_at_100%_0%,black_10%,transparent_70%)]"),
                    style: {
                        backgroundImage: "".concat(auroraVars.stripes, ", ").concat(auroraVars.flow),
                        backgroundSize: "400%, 300%",
                        backgroundPosition: "50% 50%, 50% 50%"
                    },
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute inset-0",
                        style: {
                            backgroundImage: "".concat(auroraVars.stripes, ", ").concat(auroraVars.flow),
                            backgroundSize: "300%, 200%" // Enhanced: Larger background for more dynamic movement
                        }
                    }, void 0, false, {
                        fileName: "[project]/components/ui/aurora-background.tsx",
                        lineNumber: 57,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/components/ui/aurora-background.tsx",
                    lineNumber: 41,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/components/ui/aurora-background.tsx",
                lineNumber: 40,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            children
        ]
    }, void 0, true, {
        fileName: "[project]/components/ui/aurora-background.tsx",
        lineNumber: 32,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_c = AuroraBackground;
const AuroraBackgroundLayer = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(_c1 = (param)=>{
    let { className, showRadialGradient = true, variant = "hero" } = param;
    // Determine which aurora variables to use based on variant
    const auroraVars = variant === "feature" ? {
        bg: "var(--aurora-feature-bg, var(--aurora-bg))",
        stripes: "var(--aurora-feature-stripes, var(--aurora-stripes))",
        flow: "var(--aurora-feature-flow, var(--aurora-flow))"
    } : {
        bg: "var(--aurora-bg)",
        stripes: "var(--aurora-stripes)",
        flow: "var(--aurora-flow)"
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "fixed inset-0 -z-10 overflow-hidden",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])('filter blur-[10px]\n            after:content-[""] after:absolute after:inset-0\n            after:[background-size:200%,_100%]\n            after:animate-aurora after:mix-blend-difference\n            pointer-events-none\n            absolute -inset-[10px] opacity-75 will-change-transform', showRadialGradient && "[mask-image:radial-gradient(ellipse_at_100%_0%,black_10%,transparent_70%)]", className),
            style: {
                backgroundImage: "".concat(auroraVars.stripes, ", ").concat(auroraVars.flow),
                backgroundSize: "400%, 300%",
                backgroundPosition: "50% 50%, 50% 50%"
            },
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "absolute inset-0",
                style: {
                    backgroundImage: "".concat(auroraVars.stripes, ", ").concat(auroraVars.flow),
                    backgroundSize: "300%, 200%" // Enhanced: Larger background for more dynamic movement
                }
            }, void 0, false, {
                fileName: "[project]/components/ui/aurora-background.tsx",
                lineNumber: 111,
                columnNumber: 11
            }, ("TURBOPACK compile-time value", void 0))
        }, void 0, false, {
            fileName: "[project]/components/ui/aurora-background.tsx",
            lineNumber: 94,
            columnNumber: 9
        }, ("TURBOPACK compile-time value", void 0))
    }, void 0, false, {
        fileName: "[project]/components/ui/aurora-background.tsx",
        lineNumber: 93,
        columnNumber: 7
    }, ("TURBOPACK compile-time value", void 0));
});
_c2 = AuroraBackgroundLayer;
AuroraBackgroundLayer.displayName = "AuroraBackgroundLayer";
var _c, _c1, _c2;
__turbopack_context__.k.register(_c, "AuroraBackground");
__turbopack_context__.k.register(_c1, "AuroraBackgroundLayer$memo");
__turbopack_context__.k.register(_c2, "AuroraBackgroundLayer");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/components/ui/shiny-button.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "ShinyButton": ()=>ShinyButton,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/lib/utils.ts [app-client] (ecmascript)");
"use client";
;
;
;
const animationProps = {
    initial: {
        "--x": "100%",
        scale: 0.8
    },
    animate: {
        "--x": "-100%",
        scale: 1
    },
    whileTap: {
        scale: 0.95
    },
    transition: {
        repeat: Infinity,
        repeatType: "loop",
        repeatDelay: 1,
        type: "spring",
        stiffness: 20,
        damping: 15,
        mass: 2,
        scale: {
            type: "spring",
            stiffness: 200,
            damping: 5,
            mass: 0.5
        }
    }
};
const ShinyButton = (param)=>{
    let { children, className, size = "md", variant = "primary", shimmerColor, backgroundColor, textColor, ...props } = param;
    // Size-based styling
    const sizeClasses = {
        sm: "px-3 py-2 text-sm rounded-md",
        md: "px-6 py-2 text-base rounded-lg",
        lg: "px-8 py-3 text-lg rounded-lg"
    };
    // Color variants with customization support - Updated to New Specifications
    const getColors = ()=>{
        const baseColors = {
            primary: {
                bg: backgroundColor || "#B8956A",
                /* Vierla-Gold - Primary CTAs */ shimmer: shimmerColor || "#F4F1E8",
                /* Warm Cream - Shimmer effect */ text: textColor || "#2D2A26" /* Deep Charcoal - Text on primary */ 
            },
            secondary: {
                bg: backgroundColor || "#8B9A8C",
                /* Vierla-Sage - Secondary buttons */ shimmer: shimmerColor || "#F4F1E8",
                /* Warm Cream - Shimmer effect */ text: textColor || "#F4F1E8" /* Warm Cream - Text on secondary */ 
            },
            accent: {
                bg: backgroundColor || "#364035",
                /* Vierla-Forest - Accent buttons */ shimmer: shimmerColor || "#B8956A",
                /* Vierla-Gold - Shimmer effect */ text: textColor || "#F4F1E8" /* Warm Cream - Text on accent */ 
            }
        };
        return baseColors[variant];
    };
    const colors = getColors();
    const { onClick, onMouseEnter, onMouseLeave, onFocus, onBlur, disabled, type, id, name, value } = props;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$framer$2d$motion$2f$dist$2f$es$2f$render$2f$components$2f$motion$2f$proxy$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["motion"].button, {
        ...animationProps,
        onClick: onClick,
        onMouseEnter: onMouseEnter,
        onMouseLeave: onMouseLeave,
        onFocus: onFocus,
        onBlur: onBlur,
        disabled: disabled,
        type: type,
        id: id,
        name: name,
        value: value,
        className: (0, __TURBOPACK__imported__module__$5b$project$5d2f$lib$2f$utils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cn"])("relative font-medium backdrop-blur-xl transition-shadow duration-300 ease-in-out hover:shadow", "dark:hover:shadow-[0_0_20px_hsl(var(--primary)/10%)]", "flex items-center justify-center", sizeClasses[size], className),
        style: {
            backgroundColor: colors.bg,
            "--primary": colors.shimmer
        },
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                className: "relative flex items-center justify-center size-full uppercase tracking-wide dark:font-light",
                style: {
                    color: colors.text,
                    maskImage: "linear-gradient(-75deg,".concat(colors.shimmer, " calc(var(--x) + 20%),transparent calc(var(--x) + 30%),").concat(colors.shimmer, " calc(var(--x) + 100%))")
                },
                children: children
            }, void 0, false, {
                fileName: "[project]/components/ui/shiny-button.tsx",
                lineNumber: 118,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                style: {
                    mask: "linear-gradient(var(--mask-black), var(--mask-black)) content-box,linear-gradient(var(--mask-black), var(--mask-black))",
                    maskComposite: "exclude",
                    background: "linear-gradient(-75deg,".concat(colors.shimmer, "10 calc(var(--x)+20%),").concat(colors.shimmer, "80 calc(var(--x)+25%),").concat(colors.shimmer, "10 calc(var(--x)+100%))")
                },
                className: "absolute inset-0 z-10 block rounded-[inherit] p-px"
            }, void 0, false, {
                fileName: "[project]/components/ui/shiny-button.tsx",
                lineNumber: 128,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/components/ui/shiny-button.tsx",
        lineNumber: 94,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_c = ShinyButton;
const __TURBOPACK__default__export__ = ShinyButton;
var _c;
__turbopack_context__.k.register(_c, "ShinyButton");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=components_ui_c10deaed._.js.map