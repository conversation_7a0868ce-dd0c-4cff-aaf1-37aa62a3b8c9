"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { GetStartedButton } from "@/components/ui/get-started-button";
import { Sheet, She<PERSON><PERSON>ontent, She<PERSON><PERSON>rigger, SheetTitle } from "@/components/ui/sheet";
import { Menu } from "lucide-react";
import Image from "next/image";
import { ThemeSwitcher } from "@/components/ui/theme-switcher";

export const Navbar = React.memo(function Navbar() {
  const pathname = usePathname();

  // Simple function to check if a link is active
  const isActive = (href: string) => {
    if (href === '/') {
      return pathname === '/';
    }
    return pathname.startsWith(href);
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-50 flex w-full items-center justify-between px-4 md:px-6 bg-[var(--header-footer-bg)] border-b border-[var(--header-footer-border)]" style={{
      height: 'var(--header-height, 5rem)', /* Mobile: 3.33rem, Desktop: 5rem */
      backdropFilter: 'var(--header-footer-backdrop-blur)',
      WebkitBackdropFilter: 'var(--header-footer-backdrop-blur)'
    }}>
      <Link href="/" className="flex items-center gap-3 group" prefetch={false}>
        <div className="flex items-center justify-center transform group-hover:scale-110 transition-all duration-300" style={{
          width: 'var(--header-logo-size, 3rem)', /* Mobile: 2rem, Desktop: 3rem */
          height: 'var(--header-logo-size, 3rem)', /* Mobile: 2rem, Desktop: 3rem */
        }}>
          <Image
            src="/logo-transparent.png"
            alt="Vierla Logo"
            width={28}
            height={28}
            className="w-5 h-5 md:w-7 md:h-7 drop-shadow-lg object-contain"
            style={{
              filter: 'drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1))'
            }}
          />
        </div>
        <span className="font-bold drop-shadow-lg font-notable" style={{
          fontSize: 'var(--header-nav-font-size, 2rem)', /* Mobile: 0.75rem, Desktop: 2rem */
          color: 'var(--master-header-brand-text)',
          fontFamily: 'Oswald, sans-serif'
        }}>Vierla</span>
      </Link>
      <div className="hidden items-center gap-1 text-sm font-medium md:flex">
        <nav className="flex items-center gap-1 mr-1">
          <Link
            href="/"
            className={`nav-link limelight-nav ${isActive('/') ? 'nav-link-active' : ''}`}
            style={{ color: 'var(--text-primary)' }}
          >
            Home
          </Link>
          <Link
            href="/features"
            className={`nav-link limelight-nav ${isActive('/features') ? 'nav-link-active' : ''}`}
            style={{ color: 'var(--text-primary)' }}
          >
            Features
          </Link>
          <Link
            href="/pricing"
            className={`nav-link limelight-nav ${isActive('/pricing') ? 'nav-link-active' : ''}`}
            style={{ color: 'var(--text-primary)' }}
          >
            Pricing
          </Link>
          <Link
            href="/about"
            className={`nav-link ${isActive('/about') ? 'nav-link-active' : ''}`}
            style={{ color: 'var(--text-primary)' }}
          >
            About
          </Link>
          <Link
            href="/contact"
            className={`nav-link ${isActive('/contact') ? 'nav-link-active' : ''}`}
            style={{ color: 'var(--text-primary)' }}
          >
            Contact
          </Link>
        </nav>

        {/* Get Started Button - Reduced size */}
        <Link href="/contact">
          <GetStartedButton size="sm" className="h-8 px-4 text-sm" />
        </Link>

        {/* Theme Switcher - Moved to the right */}
        <ThemeSwitcher />
      </div>
      {/* Mobile Menu and Theme Switcher */}
      <div className="flex items-center gap-3 md:hidden">
        <ThemeSwitcher />
        <Sheet>
          <SheetTrigger asChild>
            <Button variant="ghost" size="icon" className="bg-transparent hover:bg-brand-sage/10 transition-colors border-0">
              <Menu className="h-6 w-6" style={{ color: 'var(--icon-primary)' }} />
              <span className="sr-only">Toggle navigation menu</span>
            </Button>
          </SheetTrigger>
        <SheetContent
          side="right"
          className="bg-[var(--header-footer-bg)] border-[var(--header-footer-border)] min-w-fit max-w-sm w-auto"
          style={{
            backdropFilter: 'var(--header-footer-backdrop-blur)',
            WebkitBackdropFilter: 'var(--header-footer-backdrop-blur)'
          }}
        >
          <SheetTitle className="sr-only">Navigation Menu</SheetTitle>
          <div className="flex flex-col space-y-6 mt-8 px-2">
            {/* Mobile Logo */}
            <div className="flex items-center gap-3 pb-4 border-b border-[var(--header-footer-border)]">
              <div className="flex items-center justify-center" style={{
                width: '2rem',
                height: '2rem'
              }}>
                <Image
                  src="/logo-transparent.png"
                  alt="Vierla Logo"
                  width={16}
                  height={16}
                  className="w-4 h-4 object-contain"
                />
              </div>
              <span className="text-xl font-bold font-notable" style={{
                color: 'var(--text-primary)',
                fontFamily: 'Oswald, sans-serif'
              }}>Vierla</span>
            </div>

            {/* Navigation Links */}
            <div className="flex flex-col space-y-3">
              <Link
                href="/"
                className={`text-base font-medium transition-colors py-2 px-3 rounded-lg ${
                  isActive('/')
                    ? 'text-theme-primary bg-theme-primary/10'
                    : 'hover:text-theme-primary hover:bg-theme-primary/5'
                }`}
                style={{ color: isActive('/') ? undefined : 'var(--text-primary)' }}
              >
                Home
              </Link>
              <Link
                href="/features"
                className={`text-base font-medium transition-colors py-2 px-3 rounded-lg ${
                  isActive('/features')
                    ? 'text-theme-primary bg-theme-primary/10'
                    : 'hover:text-theme-primary hover:bg-theme-primary/5'
                }`}
                style={{ color: isActive('/features') ? undefined : 'var(--text-primary)' }}
              >
                Features
              </Link>
              <Link
                href="/pricing"
                className={`text-base font-medium transition-colors py-2 px-3 rounded-lg ${
                  isActive('/pricing')
                    ? 'text-theme-primary bg-theme-primary/10'
                    : 'hover:text-theme-primary hover:bg-theme-primary/5'
                }`}
                style={{ color: isActive('/pricing') ? undefined : 'var(--text-primary)' }}
              >
                Pricing
              </Link>
              <Link
                href="/about"
                className={`text-base font-medium transition-colors py-2 px-3 rounded-lg ${
                  isActive('/about')
                    ? 'text-theme-primary bg-theme-primary/10'
                    : 'hover:text-theme-primary hover:bg-theme-primary/5'
                }`}
                style={{ color: isActive('/about') ? undefined : 'var(--text-primary)' }}
              >
                About
              </Link>
              <Link
                href="/contact"
                className={`text-base font-medium transition-colors py-2 px-3 rounded-lg ${
                  isActive('/contact')
                    ? 'text-theme-primary bg-theme-primary/10'
                    : 'hover:text-theme-primary hover:bg-theme-primary/5'
                }`}
                style={{ color: isActive('/contact') ? undefined : 'var(--text-primary)' }}
              >
                Contact
              </Link>
            </div>

            {/* CTA Button - Isolated instance for mobile menu */}
            <div className="pt-4 border-t border-[var(--header-footer-border)]">
              <Link href="/contact" className="block">
                <GetStartedButton className="w-full text-sm py-2 px-4" size="sm" />
              </Link>
            </div>
          </div>
        </SheetContent>
        </Sheet>
      </div>
    </header>
  );
});
