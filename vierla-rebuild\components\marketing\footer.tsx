import React from "react";
import Link from "next/link";
import Image from "next/image";

export const Footer = React.memo(function Footer() {
  return (
    <footer className="bg-[var(--header-footer-bg)] border-[var(--header-footer-border)] border-t py-8 sm:py-12" style={{
      backdropFilter: 'var(--header-footer-backdrop-blur)',
      WebkitBackdropFilter: 'var(--header-footer-backdrop-blur)'
    }}>
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Mobile: Single column, Tablet: 2 columns, Desktop: 4 columns */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8">
          {/* Brand Section - Full width on mobile */}
          <div className="sm:col-span-2 lg:col-span-1">
            <div className="flex items-center gap-3 mb-3 sm:mb-4">
              <div className="flex items-center justify-center w-8 h-8 sm:w-10 sm:h-10">
                <Image
                  src="/logo-transparent.png"
                  alt="Vierla <PERSON>go"
                  width={32}
                  height={32}
                  className="w-6 h-6 sm:w-8 sm:h-8 drop-shadow-lg object-contain"
                  style={{
                    filter: 'drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1))'
                  }}
                />
              </div>
              <h2 className="text-xl sm:text-2xl font-bold drop-shadow-lg font-notable" style={{
                color: 'var(--text-primary)',
                fontFamily: 'Oswald, sans-serif'
              }}>Vierla</h2>
            </div>
            <p className="text-sm sm:text-base drop-shadow-sm font-sans max-w-xs" style={{ color: 'var(--text-secondary)' }}>
              Self-Care, Simplified. Connecting you with top beauty professionals.
            </p>
          </div>

          {/* Product Links */}
          <div>
            <h3 className="font-semibold mb-3 sm:mb-4 drop-shadow-lg font-tai-heritage text-base sm:text-lg" style={{ color: 'var(--text-primary)' }}>Product</h3>
            <ul className="space-y-2 text-sm sm:text-base">
              <li><Link href="/features" className="drop-shadow-sm font-sans transition-colors hover:opacity-80" style={{ color: 'var(--text-secondary)' }}>Features</Link></li>
              <li><Link href="/pricing" className="drop-shadow-sm font-sans transition-colors hover:opacity-80" style={{ color: 'var(--text-secondary)' }}>Pricing</Link></li>
              <li><Link href="/providers" className="drop-shadow-sm font-sans transition-colors hover:opacity-80" style={{ color: 'var(--text-secondary)' }}>For Providers</Link></li>
              <li><Link href="/apply" className="drop-shadow-sm font-sans transition-colors hover:opacity-80" style={{ color: 'var(--text-secondary)' }}>Apply</Link></li>
            </ul>
          </div>

          {/* Company Links */}
          <div>
            <h3 className="font-semibold mb-3 sm:mb-4 drop-shadow-lg font-tai-heritage text-base sm:text-lg" style={{ color: 'var(--text-primary)' }}>Company</h3>
            <ul className="space-y-2 text-sm sm:text-base">
              <li><Link href="/about" className="drop-shadow-sm font-sans transition-colors hover:opacity-80" style={{ color: 'var(--text-secondary)' }}>About Us</Link></li>
              <li><Link href="/contact" className="drop-shadow-sm font-sans transition-colors hover:opacity-80" style={{ color: 'var(--text-secondary)' }}>Contact Us</Link></li>
            </ul>
          </div>

          {/* Legal Links */}
          <div>
            <h3 className="font-semibold mb-3 sm:mb-4 drop-shadow-lg font-tai-heritage text-base sm:text-lg" style={{ color: 'var(--text-primary)' }}>Legal</h3>
            <ul className="space-y-2 text-sm sm:text-base">
              <li><Link href="/privacy" className="drop-shadow-sm font-sans transition-colors hover:opacity-80" style={{ color: 'var(--text-secondary)' }}>Privacy Policy</Link></li>
              <li><Link href="/terms" className="drop-shadow-sm font-sans transition-colors hover:opacity-80" style={{ color: 'var(--text-secondary)' }}>Terms of Service</Link></li>
            </ul>
          </div>
        </div>

        {/* Copyright Section */}
        <div className="mt-8 sm:mt-10 border-t pt-6 sm:pt-8 text-center text-xs sm:text-sm drop-shadow-sm" style={{
          borderColor: 'var(--border-subtle)',
          color: 'var(--text-muted)'
        }}>
          <p className="font-sans">© {new Date().getFullYear()} Vierla, Inc. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
});
