{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/app/sitemap.ts"], "sourcesContent": ["import { MetadataRoute } from 'next'\n\nexport default function sitemap(): MetadataRoute.Sitemap {\n  const baseUrl = 'https://vierla.com'\n  const currentDate = new Date()\n\n  return [\n    {\n      url: baseUrl,\n      lastModified: currentDate,\n      changeFrequency: 'weekly',\n      priority: 1,\n    },\n    {\n      url: `${baseUrl}/features`,\n      lastModified: currentDate,\n      changeFrequency: 'monthly',\n      priority: 0.9,\n    },\n    {\n      url: `${baseUrl}/pricing`,\n      lastModified: currentDate,\n      changeFrequency: 'monthly',\n      priority: 0.9,\n    },\n    {\n      url: `${baseUrl}/about`,\n      lastModified: currentDate,\n      changeFrequency: 'monthly',\n      priority: 0.8,\n    },\n    {\n      url: `${baseUrl}/contact`,\n      lastModified: currentDate,\n      changeFrequency: 'monthly',\n      priority: 0.8,\n    },\n    {\n      url: `${baseUrl}/providers`,\n      lastModified: currentDate,\n      changeFrequency: 'weekly',\n      priority: 0.9,\n    },\n    {\n      url: `${baseUrl}/apply`,\n      lastModified: currentDate,\n      changeFrequency: 'monthly',\n      priority: 0.7,\n    },\n    {\n      url: `${baseUrl}/privacy`,\n      lastModified: currentDate,\n      changeFrequency: 'yearly',\n      priority: 0.3,\n    },\n    {\n      url: `${baseUrl}/terms`,\n      lastModified: currentDate,\n      changeFrequency: 'yearly',\n      priority: 0.3,\n    },\n  ]\n}\n"], "names": [], "mappings": ";;;AAEe,SAAS;IACtB,MAAM,UAAU;IAChB,MAAM,cAAc,IAAI;IAExB,OAAO;QACL;YACE,KAAK;YACL,cAAc;YACd,iBAAiB;YACjB,UAAU;QACZ;QACA;YACE,KAAK,GAAG,QAAQ,SAAS,CAAC;YAC1B,cAAc;YACd,iBAAiB;YACjB,UAAU;QACZ;QACA;YACE,KAAK,GAAG,QAAQ,QAAQ,CAAC;YACzB,cAAc;YACd,iBAAiB;YACjB,UAAU;QACZ;QACA;YACE,KAAK,GAAG,QAAQ,MAAM,CAAC;YACvB,cAAc;YACd,iBAAiB;YACjB,UAAU;QACZ;QACA;YACE,KAAK,GAAG,QAAQ,QAAQ,CAAC;YACzB,cAAc;YACd,iBAAiB;YACjB,UAAU;QACZ;QACA;YACE,KAAK,GAAG,QAAQ,UAAU,CAAC;YAC3B,cAAc;YACd,iBAAiB;YACjB,UAAU;QACZ;QACA;YACE,KAAK,GAAG,QAAQ,MAAM,CAAC;YACvB,cAAc;YACd,iBAAiB;YACjB,UAAU;QACZ;QACA;YACE,KAAK,GAAG,QAAQ,QAAQ,CAAC;YACzB,cAAc;YACd,iBAAiB;YACjB,UAAU;QACZ;QACA;YACE,KAAK,GAAG,QAAQ,MAAM,CAAC;YACvB,cAAc;YACd,iBAAiB;YACjB,UAAU;QACZ;KACD;AACH", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/app/sitemap--route-entry.js"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport * as _sitemapModule from \"./sitemap.ts\"\nimport { resolveRouteData } from 'next/dist/build/webpack/loaders/metadata/resolve-route-data'\n\nconst sitemapModule = { ..._sitemapModule }\nconst handler = sitemapModule.default\nconst generateSitemaps = sitemapModule.generateSitemaps\nconst contentType = \"application/xml\"\nconst cacheControl = \"public, max-age=0, must-revalidate\"\nconst fileType = \"sitemap\"\n\nif (typeof handler !== 'function') {\n    throw new Error('Default export is missing in \"./sitemap.ts\"')\n}\n\nexport async function GET(_, ctx) {\n    const { __metadata_id__: id, ...params } = await ctx.params || {}\n    const hasXmlExtension = id ? id.endsWith('.xml') : false\n    if (id && !hasXmlExtension) {\n        return new NextResponse('Not Found', {\n            status: 404,\n        })\n    }\n\n    if (process.env.NODE_ENV !== 'production' && sitemapModule.generateSitemaps) {\n        const sitemaps = await sitemapModule.generateSitemaps()\n        for (const item of sitemaps) {\n            if (item?.id == null) {\n                throw new Error('id property is required for every item returned from generateSitemaps')\n            }\n        }\n    }\n    \n    const targetId = id && hasXmlExtension ? id.slice(0, -4) : undefined\n    const data = await handler({ id: targetId })\n    const content = resolveRouteData(data, fileType)\n\n    return new NextResponse(content, {\n        headers: {\n            'Content-Type': contentType,\n            'Cache-Control': cacheControl,\n        },\n    })\n}\n\nexport * from \"./sitemap.ts\"\n\n\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,gBAAgB;IAAE,GAAG,8GAAc;AAAC;AAC1C,MAAM,UAAU,cAAc,OAAO;AACrC,MAAM,mBAAmB,cAAc,gBAAgB;AACvD,MAAM,cAAc;AACpB,MAAM,eAAe;AACrB,MAAM,WAAW;AAEjB,IAAI,OAAO,YAAY,YAAY;IAC/B,MAAM,IAAI,MAAM;AACpB;AAEO,eAAe,IAAI,CAAC,EAAE,GAAG;IAC5B,MAAM,EAAE,iBAAiB,EAAE,EAAE,GAAG,QAAQ,GAAG,MAAM,IAAI,MAAM,IAAI,CAAC;IAChE,MAAM,kBAAkB,KAAK,GAAG,QAAQ,CAAC,UAAU;IACnD,IAAI,MAAM,CAAC,iBAAiB;QACxB,OAAO,IAAI,8HAAA,CAAA,eAAY,CAAC,aAAa;YACjC,QAAQ;QACZ;IACJ;IAEA,IAAI,oDAAyB,gBAAgB,cAAc,gBAAgB,EAAE;QACzE,MAAM,WAAW,MAAM,cAAc,gBAAgB;QACrD,KAAK,MAAM,QAAQ,SAAU;YACzB,IAAI,MAAM,MAAM,MAAM;gBAClB,MAAM,IAAI,MAAM;YACpB;QACJ;IACJ;IAEA,MAAM,WAAW,MAAM,kBAAkB,GAAG,KAAK,CAAC,GAAG,CAAC,KAAK;IAC3D,MAAM,OAAO,MAAM,QAAQ;QAAE,IAAI;IAAS;IAC1C,MAAM,UAAU,CAAA,GAAA,mMAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM;IAEvC,OAAO,IAAI,8HAAA,CAAA,eAAY,CAAC,SAAS;QAC7B,SAAS;YACL,gBAAgB;YAChB,iBAAiB;QACrB;IACJ;AACJ", "debugId": null}}]}