{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AuroraBackground = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackground() from the server but AuroraBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx <module evaluation>\",\n    \"AuroraBackground\",\n);\nexport const AuroraBackgroundLayer = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackgroundLayer() from the server but AuroraBackgroundLayer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx <module evaluation>\",\n    \"AuroraBackgroundLayer\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,qEACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,qEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AuroraBackground = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackground() from the server but AuroraBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx\",\n    \"AuroraBackground\",\n);\nexport const AuroraBackgroundLayer = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackgroundLayer() from the server but AuroraBackgroundLayer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx\",\n    \"AuroraBackgroundLayer\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,iDACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,iDACA", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/app/privacy/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\r\nimport { AuroraBackgroundLayer } from \"@/components/ui/aurora-background\";\r\n\r\nexport const metadata: Metadata = {\r\n  title: 'Privacy Policy | Vierla',\r\n  description: '<PERSON>ier<PERSON>\\'s privacy policy outlining how we collect, use, and protect your personal information in compliance with Canadian privacy laws.',\r\n  openGraph: {\r\n    title: 'Privacy Policy | Vierla',\r\n    description: 'Learn about <PERSON><PERSON><PERSON>\\'s commitment to protecting your privacy and personal information.',\r\n    url: 'https://vierla.com/privacy',\r\n    siteName: 'Vierla',\r\n    locale: 'en_CA',\r\n    type: 'website',\r\n  },\r\n}\r\n\r\nexport default function PrivacyPolicy() {\r\n  return (\r\n    <div className=\"page-privacy min-h-screen relative overflow-hidden\">\r\n      <AuroraBackgroundLayer />\r\n\r\n      {/* Main Content */}\r\n      <main className=\"relative z-10 container mx-auto px-4 py-20 pt-36\">\r\n        <div className=\"max-w-4xl mx-auto\">\r\n          <div className=\"p-8 md:p-12\">\r\n            <h1 className=\"text-4xl md:text-6xl font-black text-light-off-white mb-8 drop-shadow-lg font-sans\">\r\n              PRIVACY POLICY\r\n            </h1>\r\n\r\n            <div className=\"prose prose-xl prose-invert max-w-none\">\r\n              <p className=\"text-vierla-text/90 text-xl mb-8\">\r\n                <strong>Effective Date:</strong> January 2025\r\n              </p>\r\n\r\n              <p className=\"text-vierla-text/90 text-lg mb-8\">\r\n                At Vierla (\"we,\" \"our,\" or \"us\"), we are committed to protecting your privacy and ensuring the security of your personal information. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you visit our website www.vierla.com and use our services.\r\n              </p>\r\n\r\n              <h2 className=\"text-3xl md:text-4xl font-bold text-vierla-text mt-10 mb-6 drop-shadow-lg\">Information We Collect</h2>\r\n\r\n              <h3 className=\"text-2xl font-semibold text-vierla-text mt-8 mb-4 drop-shadow-sm\">Personal Information</h3>\r\n              <p className=\"text-vierla-text/90 text-lg mb-6\">\r\n                We may collect personal information that you voluntarily provide to us, including:\r\n              </p>\r\n              <ul className=\"text-vierla-text/90 text-lg mb-8 space-y-3\">\r\n                <li>• Name and contact information (email address, phone number, address)</li>\r\n                <li>• Account credentials and profile information</li>\r\n                <li>• Payment and billing information</li>\r\n                <li>• Service preferences and booking history</li>\r\n                <li>• Communications with us</li>\r\n              </ul>\r\n\r\n              <h3 className=\"text-2xl font-semibold text-vierla-text mt-8 mb-4 drop-shadow-sm\">Technical Information</h3>\r\n              <p className=\"text-vierla-text/90 text-lg mb-6\">\r\n                We automatically collect certain technical information, including:\r\n              </p>\r\n              <ul className=\"text-vierla-text/90 text-lg mb-8 space-y-3\">\r\n                <li>• IP address and device information</li>\r\n                <li>• Browser type and version</li>\r\n                <li>• Usage patterns and preferences</li>\r\n                <li>• Cookies and similar technologies</li>\r\n              </ul>\r\n\r\n              <h2 className=\"text-3xl md:text-4xl font-bold text-vierla-text mt-10 mb-6 drop-shadow-lg\">How We Use Your Information</h2>\r\n              <p className=\"text-vierla-text/90 text-lg mb-6\">We use your information to:</p>\r\n              <ul className=\"text-vierla-text/90 text-lg mb-8 space-y-3\">\r\n                <li>• Provide and improve our beauty services platform</li>\r\n                <li>• Process bookings and payments</li>\r\n                <li>• Communicate with you about services and updates</li>\r\n                <li>• Personalize your experience</li>\r\n                <li>• Ensure platform security and prevent fraud</li>\r\n                <li>• Comply with legal obligations</li>\r\n              </ul>\r\n\r\n              <h2 className=\"text-3xl md:text-4xl font-bold text-vierla-text mt-10 mb-6 drop-shadow-lg\">Information Sharing</h2>\r\n              <p className=\"text-vierla-text/90 text-lg mb-6\">\r\n                We do not sell your personal information. We may share your information with:\r\n              </p>\r\n              <ul className=\"text-vierla-text/90 text-lg mb-8 space-y-3\">\r\n                <li>• Beauty professionals to fulfill service bookings</li>\r\n                <li>• Service providers who assist our operations</li>\r\n                <li>• Legal authorities when required by law</li>\r\n                <li>• Business partners with your consent</li>\r\n              </ul>\r\n\r\n              <h2 className=\"text-3xl md:text-4xl font-bold text-vierla-text mt-10 mb-6 drop-shadow-lg\">Data Security</h2>\r\n              <p className=\"text-vierla-text/90 text-lg mb-8\">\r\n                We implement appropriate technical and organizational measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. However, no internet transmission is completely secure.\r\n              </p>\r\n\r\n              <h2 className=\"text-3xl md:text-4xl font-bold text-vierla-text mt-10 mb-6 drop-shadow-lg\">Your Rights</h2>\r\n              <p className=\"text-vierla-text/90 text-lg mb-6\">You have the right to:</p>\r\n              <ul className=\"text-vierla-text/90 text-lg mb-8 space-y-3\">\r\n                <li>• Access and update your personal information</li>\r\n                <li>• Request deletion of your data</li>\r\n                <li>• Opt-out of marketing communications</li>\r\n                <li>• Request data portability</li>\r\n                <li>• Lodge complaints with supervisory authorities</li>\r\n              </ul>\r\n\r\n              <h2 className=\"text-3xl md:text-4xl font-bold text-vierla-text mt-10 mb-6 drop-shadow-lg\">Cookies</h2>\r\n              <p className=\"text-vierla-text/90 text-lg mb-8\">\r\n                We use cookies and similar technologies to enhance your browsing experience, analyze website traffic, and personalize content. You can control cookie preferences through your browser settings.\r\n              </p>\r\n\r\n              <h2 className=\"text-3xl md:text-4xl font-bold text-vierla-text mt-10 mb-6 drop-shadow-lg\">Contact Us</h2>\r\n              <p className=\"text-vierla-text/90 text-lg mb-8\">\r\n                If you have questions about this Privacy Policy or our data practices, please contact us at:\r\n                <br />\r\n                Email: <EMAIL>\r\n              </p>\r\n\r\n              <p className=\"text-vierla-text/90 text-base mt-10\">\r\n                This Privacy Policy may be updated periodically. We will notify you of significant changes through our website or email.\r\n              </p>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </main>\r\n    </div>\r\n  )\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,WAAW;QACT,OAAO;QACP,aAAa;QACb,KAAK;QACL,UAAU;QACV,QAAQ;QACR,MAAM;IACR;AACF;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,yIAAA,CAAA,wBAAqB;;;;;0BAGtB,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqF;;;;;;0CAInG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;;0DACX,8OAAC;0DAAO;;;;;;4CAAwB;;;;;;;kDAGlC,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAIhD,8OAAC;wCAAG,WAAU;kDAA4E;;;;;;kDAE1F,8OAAC;wCAAG,WAAU;kDAAmE;;;;;;kDACjF,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAGhD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;kDAGN,8OAAC;wCAAG,WAAU;kDAAmE;;;;;;kDACjF,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAGhD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;kDAGN,8OAAC;wCAAG,WAAU;kDAA4E;;;;;;kDAC1F,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAChD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;kDAGN,8OAAC;wCAAG,WAAU;kDAA4E;;;;;;kDAC1F,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAGhD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;kDAGN,8OAAC;wCAAG,WAAU;kDAA4E;;;;;;kDAC1F,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAIhD,8OAAC;wCAAG,WAAU;kDAA4E;;;;;;kDAC1F,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAChD,8OAAC;wCAAG,WAAU;;0DACZ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;0DACJ,8OAAC;0DAAG;;;;;;;;;;;;kDAGN,8OAAC;wCAAG,WAAU;kDAA4E;;;;;;kDAC1F,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAIhD,8OAAC;wCAAG,WAAU;kDAA4E;;;;;;kDAC1F,8OAAC;wCAAE,WAAU;;4CAAmC;0DAE9C,8OAAC;;;;;4CAAK;;;;;;;kDAIR,8OAAC;wCAAE,WAAU;kDAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjE", "debugId": null}}]}