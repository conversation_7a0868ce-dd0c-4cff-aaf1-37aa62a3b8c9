"use client";

import React, { useState, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { ChevronDown, Check } from 'lucide-react';

export interface DropdownOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface DropdownSelectProps {
  options: DropdownOption[];
  value?: string;
  placeholder?: string;
  onChange?: (value: string) => void;
  className?: string;
  disabled?: boolean;
  error?: string;
  label?: string;
  required?: boolean;
}

export function DropdownSelect({
  options,
  value,
  placeholder = "Select an option",
  onChange,
  className,
  disabled = false,
  error,
  label,
  required = false,
}: DropdownSelectProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedValue, setSelectedValue] = useState(value || '');
  const dropdownRef = useRef<HTMLDivElement>(null);

  const selectedOption = options.find(option => option.value === selectedValue);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  useEffect(() => {
    if (value !== undefined) {
      setSelectedValue(value);
    }
  }, [value]);

  const handleSelect = (optionValue: string) => {
    setSelectedValue(optionValue);
    setIsOpen(false);
    onChange?.(optionValue);
  };

  const handleKeyDown = (event: React.KeyboardEvent) => {
    if (disabled) return;

    switch (event.key) {
      case 'Enter':
      case ' ':
        event.preventDefault();
        setIsOpen(!isOpen);
        break;
      case 'Escape':
        setIsOpen(false);
        break;
      case 'ArrowDown':
        event.preventDefault();
        if (!isOpen) {
          setIsOpen(true);
        } else {
          // Focus next option
          const currentIndex = options.findIndex(opt => opt.value === selectedValue);
          const nextIndex = Math.min(currentIndex + 1, options.length - 1);
          if (options[nextIndex] && !options[nextIndex].disabled) {
            handleSelect(options[nextIndex].value);
          }
        }
        break;
      case 'ArrowUp':
        event.preventDefault();
        if (isOpen) {
          const currentIndex = options.findIndex(opt => opt.value === selectedValue);
          const prevIndex = Math.max(currentIndex - 1, 0);
          if (options[prevIndex] && !options[prevIndex].disabled) {
            handleSelect(options[prevIndex].value);
          }
        }
        break;
    }
  };

  return (
    <div className={cn("relative", className)}>
      {label && (
        <label className="block text-sm font-medium mb-2 font-sans text-[#2D2A26] dark:text-[#F0E6D9]">
          {label}
          {required && <span className="text-error ml-1">*</span>}
        </label>
      )}
      
      <div ref={dropdownRef} className="relative">
        <button
          type="button"
          onClick={() => !disabled && setIsOpen(!isOpen)}
          onKeyDown={handleKeyDown}
          disabled={disabled}
          className={cn(
            "relative w-full rounded-lg px-4 py-3 text-left font-sans transition-all duration-200",
            "bg-light-charcoal border border-sage/30 text-light-off-white",
            "focus:outline-none focus:ring-2 focus:ring-muted-gold/30 focus:border-muted-gold",
            "hover:border-sage/50",
            disabled && "opacity-50 cursor-not-allowed",
            error && "border-error focus:ring-error/30",
            isOpen && "ring-2 ring-muted-gold/30 border-muted-gold"
          )}
          aria-haspopup="listbox"
          aria-expanded={isOpen}
          aria-labelledby={label ? `${label}-label` : undefined}
        >
          <span className="block truncate">
            {selectedOption ? selectedOption.label : placeholder}
          </span>
          <span className="absolute inset-y-0 right-0 flex items-center pr-4 pointer-events-none">
            <ChevronDown
              className={cn(
                "w-4 h-4 text-mantle-300 transition-transform duration-200",
                isOpen && "rotate-180"
              )}
            />
          </span>
        </button>

        {isOpen && (
          <div className="absolute z-50 w-full mt-1 bg-neutral-charcoal-dark/80 backdrop-blur-lg supports-[backdrop-filter]:bg-neutral-charcoal-dark/70 border border-brand-sage/20 rounded-lg shadow-2xl max-h-60 overflow-auto">
            <ul role="listbox" className="py-1">
              {options.map((option) => (
                <li
                  key={option.value}
                  role="option"
                  aria-selected={selectedValue === option.value}
                  className={cn(
                    "relative cursor-pointer select-none py-2 pl-10 pr-4 text-[#F4F1E8] dark:text-light-off-white font-sans",
                    "hover:bg-light-charcoal focus:bg-light-charcoal",
                    option.disabled && "opacity-50 cursor-not-allowed",
                    selectedValue === option.value && "bg-light-charcoal"
                  )}
                  onClick={() => !option.disabled && handleSelect(option.value)}
                >
                  <span className="block truncate">{option.label}</span>
                  {selectedValue === option.value && (
                    <span className="absolute inset-y-0 left-0 flex items-center pl-3">
                      <Check className="w-4 h-4 text-terracotta" />
                    </span>
                  )}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {error && (
        <p className="mt-1 text-sm text-error font-sans">{error}</p>
      )}
    </div>
  );
}
