"use client"

import * as React from "react"
import { useTheme } from "next-themes"
import { Moon, Sun } from "lucide-react"
import { cn } from "@/lib/utils"

interface ThemeSwitcherProps {
  className?: string
}

export function ThemeSwitcher({ className }: ThemeSwitcherProps) {
  const { resolvedTheme, setTheme } = useTheme()
  const [mounted, setMounted] = React.useState(false)

  // useEffect only runs on the client, so now we can safely show the UI
  React.useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return (
      <div className="w-16 h-8 p-1 rounded-full bg-gray-200 animate-pulse" />
    )
  }

  const isDark = resolvedTheme === "dark"

  const toggleTheme = () => {
    setTheme(isDark ? "light" : "dark")
  }

  return (
    <div
      className={cn(
        "flex w-16 h-8 p-1 rounded-full cursor-pointer transition-all duration-300",
        isDark
          ? "bg-[var(--header-footer-bg)] border border-[var(--header-footer-border)]"
          : "bg-[var(--header-footer-bg)] border border-[var(--header-footer-border)]",
        className
      )}
      onClick={toggleTheme}
      role="button"
      tabIndex={0}
      title={isDark ? "Switch to light mode" : "Switch to dark mode"}
      style={{
        backdropFilter: 'var(--header-footer-backdrop-blur)',
        WebkitBackdropFilter: 'var(--header-footer-backdrop-blur)'
      }}
    >
      <div className="flex justify-between items-center w-full">
        <div
          className={cn(
            "flex justify-center items-center w-6 h-6 rounded-full transition-transform duration-300",
            isDark
              ? "transform translate-x-0 bg-[var(--theme-primary)]"
              : "transform translate-x-8 bg-[var(--theme-primary)]"
          )}
        >
          {isDark ? (
            <Moon
              className="w-4 h-4 text-[#F4F1E8]"
              strokeWidth={1.5}
            />
          ) : (
            <Sun
              className="w-4 h-4 text-[#F4F1E8]"
              strokeWidth={1.5}
            />
          )}
        </div>
        <div
          className={cn(
            "flex justify-center items-center w-6 h-6 rounded-full transition-transform duration-300",
            isDark
              ? "bg-transparent"
              : "transform -translate-x-8"
          )}
        >
          {isDark ? (
            <Sun
              className="w-4 h-4"
              style={{ color: 'var(--icon-muted)' }}
              strokeWidth={1.5}
            />
          ) : (
            <Moon
              className="w-4 h-4"
              style={{ color: 'var(--icon-muted)' }}
              strokeWidth={1.5}
            />
          )}
        </div>
      </div>
    </div>
  )
}
