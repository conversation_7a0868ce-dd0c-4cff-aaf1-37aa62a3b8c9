import Script from 'next/script'

interface BreadcrumbItem {
  name: string
  url: string
}

interface BreadcrumbSchemaProps {
  items: BreadcrumbItem[]
}

export function BreadcrumbSchema({ items }: BreadcrumbSchemaProps) {
  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url
    }))
  }

  return (
    <Script
      id="breadcrumb-schema"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(breadcrumbSchema)
      }}
    />
  )
}

// Helper function to generate breadcrumbs for different pages
export function getBreadcrumbsForPage(page: string): BreadcrumbItem[] {
  const baseBreadcrumb = { name: "Home", url: "https://vierla.com" }
  
  switch (page) {
    case 'features':
      return [
        baseBreadcrumb,
        { name: "Features", url: "https://vierla.com/features" }
      ]
    case 'pricing':
      return [
        baseBreadcrumb,
        { name: "Pricing", url: "https://vierla.com/pricing" }
      ]
    case 'about':
      return [
        baseBreadcrumb,
        { name: "About", url: "https://vierla.com/about" }
      ]
    case 'contact':
      return [
        baseBreadcrumb,
        { name: "Contact", url: "https://vierla.com/contact" }
      ]
    case 'providers':
      return [
        baseBreadcrumb,
        { name: "For Providers", url: "https://vierla.com/providers" }
      ]
    case 'apply':
      return [
        baseBreadcrumb,
        { name: "For Providers", url: "https://vierla.com/providers" },
        { name: "Apply", url: "https://vierla.com/apply" }
      ]
    case 'privacy':
      return [
        baseBreadcrumb,
        { name: "Privacy Policy", url: "https://vierla.com/privacy" }
      ]
    case 'terms':
      return [
        baseBreadcrumb,
        { name: "Terms of Service", url: "https://vierla.com/terms" }
      ]
    default:
      return [baseBreadcrumb]
  }
}
