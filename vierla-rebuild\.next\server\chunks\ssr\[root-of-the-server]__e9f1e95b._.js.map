{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AuroraBackground = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackground() from the server but AuroraBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx <module evaluation>\",\n    \"AuroraBackground\",\n);\nexport const AuroraBackgroundLayer = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackgroundLayer() from the server but AuroraBackgroundLayer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx <module evaluation>\",\n    \"AuroraBackgroundLayer\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,qEACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,qEACA", "debugId": null}}, {"offset": {"line": 34, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AuroraBackground = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackground() from the server but AuroraBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx\",\n    \"AuroraBackground\",\n);\nexport const AuroraBackgroundLayer = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackgroundLayer() from the server but AuroraBackgroundLayer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx\",\n    \"AuroraBackgroundLayer\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,iDACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,iDACA", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/shiny-button.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ShinyButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call ShinyButton() from the server but ShinyButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/shiny-button.tsx <module evaluation>\",\n    \"ShinyButton\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ui/shiny-button.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/shiny-button.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,gEACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/shiny-button.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ShinyButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call ShinyButton() from the server but ShinyButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/shiny-button.tsx\",\n    \"ShinyButton\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ui/shiny-button.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/shiny-button.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,4CACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 152, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/app/pricing/page.tsx"], "sourcesContent": ["import { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { AuroraBackgroundLayer } from \"@/components/ui/aurora-background\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { GoldenGlowingCardContainer } from \"@/components/ui/golden-glowing-card-container\";\nimport ShinyButton from \"@/components/ui/shiny-button\";\nimport { Check, Star } from \"lucide-react\";\nimport Link from \"next/link\";\n\nexport default function Pricing() {\n  const plans = [\n    {\n      name: \"Individual\",\n      description: \"Perfect for solo beauty professionals\",\n      price: \"Free\",\n      features: [\n        \"1 Service Listing\",\n        \"Basic Website Builder\",\n        \"5 Bookings/month\",\n        \"Email Support\",\n        \"Basic Templates\",\n        \"Payment Processing\"\n      ],\n      popular: false,\n      cta: \"Get Started Free\"\n    },\n    {\n      name: \"Medium Business\",\n      description: \"Most popular for growing beauty businesses\",\n      price: \"$29/month\",\n      features: [\n        \"Unlimited Service Listings\",\n        \"Full Website Builder\",\n        \"Unlimited Bookings\",\n        \"Integrated CRM\",\n        \"Advanced Analytics\",\n        \"Priority Support\",\n        \"Custom Domain\",\n        \"Premium Templates\",\n        \"Team Management (up to 5)\"\n      ],\n      popular: true,\n      cta: \"Start Pro Trial\"\n    },\n    {\n      name: \"Large Business\",\n      description: \"For established beauty enterprises\",\n      price: \"$79/month\",\n      features: [\n        \"Everything in Medium Business\",\n        \"Unlimited Team Members\",\n        \"White-label Options\",\n        \"API Access\",\n        \"Dedicated Account Manager\",\n        \"Custom Integrations\",\n        \"Advanced Automation\",\n        \"Multi-location Support\"\n      ],\n      popular: false,\n      cta: \"Contact Sales\"\n    }\n  ];\n\n  return (\n    <div className=\"page-pricing min-h-screen relative overflow-hidden\">\n      <AuroraBackgroundLayer />\n\n      {/* Hero Section */}\n      <section className=\"relative z-10 w-full px-4 py-20 pt-32\">\n        <div className=\"text-center max-w-6xl mx-auto\">\n          <h1 className=\"text-4xl md:text-6xl font-black mb-6 leading-none text-[var(--master-text-primary-dark)] drop-shadow-lg font-jost\">\n            SIMPLE, TRANSPARENT PRICING\n          </h1>\n          <p className=\"text-xl md:text-2xl text-[var(--master-text-secondary-dark)] mb-8 leading-relaxed max-w-4xl mx-auto drop-shadow-sm font-inter\">\n            Choose the plan that fits your business needs. Start free and scale as you grow.\n          </p>\n        </div>\n      </section>\n\n      {/* Pricing Cards */}\n      <section className=\"relative z-10 py-20 border-t border-white/20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"grid gap-6 lg:grid-cols-3 max-w-6xl mx-auto\">\n            {plans.map((plan, index) => (\n              <div key={index} className=\"relative h-full\">\n                {/* Card container with hover group */}\n                <div className=\"relative h-full group/card\">\n                  {/* Golden glow effect only on card hover, excluding button area */}\n                  <div className=\"absolute -inset-0.5 rounded-lg opacity-0 group-hover/card:opacity-100 transition-opacity duration-500 blur-sm\" style={{ background: 'linear-gradient(to right, var(--golden-glow-color, #B8956A)/30%, var(--golden-glow-color, #B8956A)/60%, var(--golden-glow-color, #B8956A)/30%)' }} />\n\n                  <div className={`relative h-full bg-light-charcoal backdrop-blur-md shadow-xl rounded-2xl overflow-hidden ${plan.popular ? 'border-2 border-[var(--master-brand-accent)]' : 'border border-sage/30'}`}>\n                    {/* Top accent bar for popular plan */}\n                    {plan.popular && (\n                      <div className=\"absolute top-0 left-0 right-0 h-1 bg-[var(--master-brand-accent)]\" />\n                    )}\n                    <div className=\"relative h-full flex flex-col p-6\">\n                      {plan.popular && (\n                        <div className=\"absolute -top-3 left-1/2 transform -translate-x-1/2\">\n                          <Badge className=\"bg-[var(--master-brand-accent)] text-[var(--master-text-primary-light)] font-inter\">Most Popular</Badge>\n                        </div>\n                      )}\n                      <div className=\"text-center pb-8\">\n                        <h3 className=\"text-2xl font-bold text-light-off-white drop-shadow-lg mb-2 font-tai-heritage\">{plan.name}</h3>\n                        <p className=\"text-warm-beige drop-shadow-sm mb-4 font-sans\">{plan.description}</p>\n                        <div className=\"mt-4\">\n                          <span className=\"text-4xl font-black text-muted-gold drop-shadow-lg font-tai-heritage\">{plan.price}</span>\n                        </div>\n                      </div>\n                      <div className=\"flex-grow flex flex-col\">\n                        <ul className=\"space-y-3 mb-8 flex-grow\">\n                          {plan.features.map((feature, idx) => (\n                            <li key={idx} className=\"flex items-center text-[var(--master-text-secondary-dark)] drop-shadow-sm font-inter\">\n                              <Check className=\"h-4 w-4 text-[var(--master-icon-success)] mr-3 flex-shrink-0 drop-shadow-sm\" />\n                              <span className=\"text-sm\">{feature}</span>\n                            </li>\n                          ))}\n                        </ul>\n                        {/* Button area with separate hover group */}\n                        <div className=\"mt-auto flex justify-center group/button\">\n                          <Link href={plan.name === \"Large Business\" ? \"/contact\" : \"/apply\"} className=\"w-full\">\n                            <ShinyButton\n                              size=\"sm\"\n                              variant=\"primary\"\n                              backgroundColor=\"#B8956A\"\n                              shimmerColor=\"#F4F1E8\"\n                              textColor=\"#2D2A26\"\n                              className=\"w-full max-w-[180px] mx-auto drop-shadow-sm text-sm whitespace-nowrap font-medium\"\n                            >\n                              {plan.cta}\n                            </ShinyButton>\n                          </Link>\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"relative z-10 py-20 border-t border-white/20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"mx-auto flex max-w-[58rem] flex-col items-center space-y-4 text-center\">\n            <h2 className=\"font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl text-light-off-white drop-shadow-lg font-tai-heritage\">\n              Ready to get started?\n            </h2>\n            <p className=\"max-w-[42rem] leading-normal text-warm-beige sm:text-xl sm:leading-8 drop-shadow-sm font-sans\">\n              Join thousands of entrepreneurs who trust Vierla to power their business.\n            </p>\n            <div className=\"space-x-4\">\n              <ShinyButton asChild>\n                <a href=\"/apply\">Start Free Trial</a>\n              </ShinyButton>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAEA;AACA;AACA;;;;;;;AAEe,SAAS;IACtB,MAAM,QAAQ;QACZ;YACE,MAAM;YACN,aAAa;YACb,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,SAAS;YACT,KAAK;QACP;QACA;YACE,MAAM;YACN,aAAa;YACb,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,SAAS;YACT,KAAK;QACP;QACA;YACE,MAAM;YACN,aAAa;YACb,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,SAAS;YACT,KAAK;QACP;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,yIAAA,CAAA,wBAAqB;;;;;0BAGtB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAoH;;;;;;sCAGlI,8OAAC;4BAAE,WAAU;sCAAgI;;;;;;;;;;;;;;;;;0BAOjJ,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;gCAAgB,WAAU;0CAEzB,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;4CAAgH,OAAO;gDAAE,YAAY;4CAAiJ;;;;;;sDAErS,8OAAC;4CAAI,WAAW,CAAC,yFAAyF,EAAE,KAAK,OAAO,GAAG,iDAAiD,yBAAyB;;gDAElM,KAAK,OAAO,kBACX,8OAAC;oDAAI,WAAU;;;;;;8DAEjB,8OAAC;oDAAI,WAAU;;wDACZ,KAAK,OAAO,kBACX,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC,0HAAA,CAAA,QAAK;gEAAC,WAAU;0EAAqF;;;;;;;;;;;sEAG1G,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAAiF,KAAK,IAAI;;;;;;8EACxG,8OAAC;oEAAE,WAAU;8EAAiD,KAAK,WAAW;;;;;;8EAC9E,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAK,WAAU;kFAAwE,KAAK,KAAK;;;;;;;;;;;;;;;;;sEAGtG,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,oBAC3B,8OAAC;4EAAa,WAAU;;8FACtB,8OAAC,oMAAA,CAAA,QAAK;oFAAC,WAAU;;;;;;8FACjB,8OAAC;oFAAK,WAAU;8FAAW;;;;;;;2EAFpB;;;;;;;;;;8EAOb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;wEAAC,MAAM,KAAK,IAAI,KAAK,mBAAmB,aAAa;wEAAU,WAAU;kFAC5E,cAAA,8OAAC,oIAAA,CAAA,UAAW;4EACV,MAAK;4EACL,SAAQ;4EACR,iBAAgB;4EAChB,cAAa;4EACb,WAAU;4EACV,WAAU;sFAET,KAAK,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BA5Cf;;;;;;;;;;;;;;;;;;;;0BA2DlB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAoH;;;;;;0CAGlI,8OAAC;gCAAE,WAAU;0CAAgG;;;;;;0CAG7G,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,oIAAA,CAAA,UAAW;oCAAC,OAAO;8CAClB,cAAA,8OAAC;wCAAE,MAAK;kDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjC", "debugId": null}}]}