import { ReactNode } from "react";
import { ArrowRightIcon } from "@radix-ui/react-icons";
import Link from "next/link";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";

const BentoGrid = ({
  children,
  className,
}: {
  children: ReactNode;
  className?: string;
}) => {
  return (
    <div
      className={cn(
        "grid w-full auto-rows-[20rem] grid-cols-3 gap-4",
        // 16px gap as specified in design system
        "gap-4", // 16px
        className,
      )}
    >
      {children}
    </div>
  );
};

const BentoCard = ({
  name,
  className,
  background,
  Icon,
  description,
  services,
  href,
  cta,
}: {
  name: string;
  className: string;
  background?: ReactNode;
  Icon: any;
  description: string;
  services?: string[];
  href: string;
  cta: string;
}) => (
  <Link href={href} className={cn(
      "group relative col-span-3 flex flex-col justify-between overflow-hidden rounded-xl",
      // Theme-aware background and borders
      "border shadow-xl transition-all duration-500 ease-out transform-gpu",
      // Smooth hover effects for desktop
      "hover:scale-[1.02] hover:shadow-2xl",
      // Mobile touch behavior - show content on first tap, navigate on second
      "mobile-bento-card",
      className,
    )}
    style={{
      backgroundColor: 'var(--card-bg)',
      borderColor: 'var(--border-subtle)',
      boxShadow: 'var(--shadow-card)'
    }}
  >
    {background && <div>{background}</div>}
    <div className="pointer-events-none z-10 flex transform-gpu flex-col gap-2 p-4 transition-all duration-500">
      <Icon className="h-10 w-10 origin-left transform-gpu transition-all duration-500 ease-out group-hover:scale-110 mobile-bento-icon"
            style={{ color: 'var(--icon-accent)' }} />
      <h3 className="text-lg font-semibold font-tai-heritage leading-tight transition-all duration-500 group-hover:opacity-0 group-hover:transform group-hover:-translate-y-2 mobile-bento-title"
          style={{ color: 'var(--text-primary)' }}>
        {name}
      </h3>
      <p className="max-w-lg font-sans text-sm leading-relaxed opacity-0 transform translate-y-2 group-hover:opacity-100 group-hover:translate-y-0 transition-all duration-500 delay-100 mobile-bento-description"
         style={{ color: 'var(--text-secondary)' }}>{description}</p>
    </div>

    <div className="pointer-events-none absolute inset-0 transform-gpu transition-all duration-500 opacity-0 group-hover:opacity-100 mobile-bento-overlay"
         style={{ backgroundColor: 'var(--accent-hover-overlay)' }} />
  </Link>
);

export { BentoCard, BentoGrid };
