{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx"], "sourcesContent": ["\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport React, { memo, type ReactNode } from \"react\";\n\ninterface AuroraBackgroundProps extends React.HTMLProps<HTMLDivElement> {\n  children: ReactNode;\n  showRadialGradient?: boolean;\n  variant?: \"hero\" | \"feature\"; // New: Support for different aurora types\n}\n\n// Full page wrapper version\nexport const AuroraBackground = ({\n  className,\n  children,\n  showRadialGradient = true,\n  variant = \"hero\",\n  ...props\n}: AuroraBackgroundProps) => {\n  // Determine which aurora variables to use based on variant\n  const auroraVars = variant === \"feature\" ? {\n    bg: \"var(--aurora-feature-bg, var(--aurora-bg))\",\n    stripes: \"var(--aurora-feature-stripes, var(--aurora-stripes))\",\n    flow: \"var(--aurora-feature-flow, var(--aurora-flow))\"\n  } : {\n    bg: \"var(--aurora-bg)\",\n    stripes: \"var(--aurora-stripes)\",\n    flow: \"var(--aurora-flow)\"\n  };\n\n  return (\n    <main\n      className={cn(\n        \"relative flex flex-col h-[100vh] items-center justify-center text-slate-950 transition-colors duration-300\",\n        className\n      )}\n      style={{ backgroundColor: auroraVars.bg }}\n      {...props}\n    >\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div\n          className={cn(\n            `filter blur-[10px]\n            after:content-[\"\"] after:absolute after:inset-0\n            after:[background-size:200%,_100%]\n            after:animate-aurora after:mix-blend-difference\n            pointer-events-none\n            absolute -inset-[10px] opacity-75 will-change-transform`,\n            showRadialGradient && `[mask-image:radial-gradient(ellipse_at_100%_0%,black_10%,transparent_70%)]`\n          )}\n          style={{\n            backgroundImage: `${auroraVars.stripes}, ${auroraVars.flow}`,\n            backgroundSize: \"400%, 300%\", // Enhanced: Larger background for more dynamic movement\n            backgroundPosition: \"50% 50%, 50% 50%\"\n          }}\n        >\n          <div\n            className=\"absolute inset-0\"\n            style={{\n              backgroundImage: `${auroraVars.stripes}, ${auroraVars.flow}`,\n              backgroundSize: \"300%, 200%\" // Enhanced: Larger background for more dynamic movement\n            }}\n          />\n        </div>\n      </div>\n      {children}\n    </main>\n  );\n};\n\n// Background-only version for existing layouts\nexport const AuroraBackgroundLayer = memo(\n  ({\n    className,\n    showRadialGradient = true,\n    variant = \"hero\",\n  }: {\n    className?: string;\n    showRadialGradient?: boolean;\n    variant?: \"hero\" | \"feature\";\n  }) => {\n    // Determine which aurora variables to use based on variant\n    const auroraVars = variant === \"feature\" ? {\n      bg: \"var(--aurora-feature-bg, var(--aurora-bg))\",\n      stripes: \"var(--aurora-feature-stripes, var(--aurora-stripes))\",\n      flow: \"var(--aurora-feature-flow, var(--aurora-flow))\"\n    } : {\n      bg: \"var(--aurora-bg)\",\n      stripes: \"var(--aurora-stripes)\",\n      flow: \"var(--aurora-flow)\"\n    };\n    return (\n      <div className=\"fixed inset-0 -z-10 overflow-hidden\">\n        <div\n          className={cn(\n            `filter blur-[10px]\n            after:content-[\"\"] after:absolute after:inset-0\n            after:[background-size:200%,_100%]\n            after:animate-aurora after:mix-blend-difference\n            pointer-events-none\n            absolute -inset-[10px] opacity-75 will-change-transform`,\n            showRadialGradient && `[mask-image:radial-gradient(ellipse_at_100%_0%,black_10%,transparent_70%)]`,\n            className\n          )}\n          style={{\n            backgroundImage: `${auroraVars.stripes}, ${auroraVars.flow}`,\n            backgroundSize: \"400%, 300%\", // Enhanced: Larger background for more dynamic movement\n            backgroundPosition: \"50% 50%, 50% 50%\"\n          }}\n        >\n          <div\n            className=\"absolute inset-0\"\n            style={{\n              backgroundImage: `${auroraVars.stripes}, ${auroraVars.flow}`,\n              backgroundSize: \"300%, 200%\" // Enhanced: Larger background for more dynamic movement\n            }}\n          />\n        </div>\n      </div>\n    );\n  }\n);\n\nAuroraBackgroundLayer.displayName = \"AuroraBackgroundLayer\";"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAYO,MAAM,mBAAmB;QAAC,EAC/B,SAAS,EACT,QAAQ,EACR,qBAAqB,IAAI,EACzB,UAAU,MAAM,EAChB,GAAG,OACmB;IACtB,2DAA2D;IAC3D,MAAM,aAAa,YAAY,YAAY;QACzC,IAAI;QACJ,SAAS;QACT,MAAM;IACR,IAAI;QACF,IAAI;QACJ,SAAS;QACT,MAAM;IACR;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,8GACA;QAEF,OAAO;YAAE,iBAAiB,WAAW,EAAE;QAAC;QACvC,GAAG,KAAK;;0BAET,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACT,sSAMD,sBAAuB;oBAEzB,OAAO;wBACL,iBAAiB,AAAC,GAAyB,OAAvB,WAAW,OAAO,EAAC,MAAoB,OAAhB,WAAW,IAAI;wBAC1D,gBAAgB;wBAChB,oBAAoB;oBACtB;8BAEA,cAAA,6LAAC;wBACC,WAAU;wBACV,OAAO;4BACL,iBAAiB,AAAC,GAAyB,OAAvB,WAAW,OAAO,EAAC,MAAoB,OAAhB,WAAW,IAAI;4BAC1D,gBAAgB,aAAa,wDAAwD;wBACvF;;;;;;;;;;;;;;;;YAIL;;;;;;;AAGP;KAxDa;AA2DN,MAAM,sCAAwB,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,QACtC;QAAC,EACC,SAAS,EACT,qBAAqB,IAAI,EACzB,UAAU,MAAM,EAKjB;IACC,2DAA2D;IAC3D,MAAM,aAAa,YAAY,YAAY;QACzC,IAAI;QACJ,SAAS;QACT,MAAM;IACR,IAAI;QACF,IAAI;QACJ,SAAS;QACT,MAAM;IACR;IACA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YACC,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACT,sSAMD,sBAAuB,8EACvB;YAEF,OAAO;gBACL,iBAAiB,AAAC,GAAyB,OAAvB,WAAW,OAAO,EAAC,MAAoB,OAAhB,WAAW,IAAI;gBAC1D,gBAAgB;gBAChB,oBAAoB;YACtB;sBAEA,cAAA,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,iBAAiB,AAAC,GAAyB,OAAvB,WAAW,OAAO,EAAC,MAAoB,OAAhB,WAAW,IAAI;oBAC1D,gBAAgB,aAAa,wDAAwD;gBACvF;;;;;;;;;;;;;;;;AAKV;;AAGF,sBAAsB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/shiny-button.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { motion } from \"framer-motion\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface ShinyButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  children: React.ReactNode;\n  className?: string;\n  size?: \"sm\" | \"md\" | \"lg\";\n  variant?: \"primary\" | \"secondary\" | \"accent\";\n  shimmerColor?: string;\n  backgroundColor?: string;\n  textColor?: string;\n}\n\nconst animationProps = {\n  initial: { \"--x\": \"100%\", scale: 0.8 },\n  animate: { \"--x\": \"-100%\", scale: 1 },\n  whileTap: { scale: 0.95 },\n  transition: {\n    repeat: Infinity,\n    repeatType: \"loop\" as const,\n    repeatDelay: 1,\n    type: \"spring\" as const,\n    stiffness: 20,\n    damping: 15,\n    mass: 2,\n    scale: {\n      type: \"spring\" as const,\n      stiffness: 200,\n      damping: 5,\n      mass: 0.5,\n    },\n  },\n};\n\nexport const ShinyButton: React.FC<ShinyButtonProps> = ({\n  children,\n  className,\n  size = \"md\",\n  variant = \"primary\",\n  shimmerColor,\n  backgroundColor,\n  textColor,\n  ...props\n}) => {\n  // Size-based styling\n  const sizeClasses = {\n    sm: \"px-3 py-2 text-sm rounded-md\",\n    md: \"px-6 py-2 text-base rounded-lg\",\n    lg: \"px-8 py-3 text-lg rounded-lg\"\n  };\n\n  // Color variants with customization support - Updated to New Specifications\n  const getColors = () => {\n    const baseColors = {\n      primary: {\n        bg: backgroundColor || \"#B8956A\",    /* Vierla-Gold - Primary CTAs */\n        shimmer: shimmerColor || \"#F4F1E8\",  /* Warm Cream - Shimmer effect */\n        text: textColor || \"#2D2A26\"         /* Deep Charcoal - Text on primary */\n      },\n      secondary: {\n        bg: backgroundColor || \"#8B9A8C\",    /* Vierla-Sage - Secondary buttons */\n        shimmer: shimmerColor || \"#F4F1E8\",  /* Warm Cream - Shimmer effect */\n        text: textColor || \"#F4F1E8\"         /* Warm Cream - Text on secondary */\n      },\n      accent: {\n        bg: backgroundColor || \"#364035\",    /* Vierla-Forest - Accent buttons */\n        shimmer: shimmerColor || \"#B8956A\",  /* Vierla-Gold - Shimmer effect */\n        text: textColor || \"#F4F1E8\"         /* Warm Cream - Text on accent */\n      }\n    };\n    return baseColors[variant];\n  };\n\n  const colors = getColors();\n\n  const {\n    onClick,\n    onMouseEnter,\n    onMouseLeave,\n    onFocus,\n    onBlur,\n    disabled,\n    type,\n    id,\n    name,\n    value\n  } = props;\n\n  return (\n    <motion.button\n      {...animationProps}\n      onClick={onClick}\n      onMouseEnter={onMouseEnter}\n      onMouseLeave={onMouseLeave}\n      onFocus={onFocus}\n      onBlur={onBlur}\n      disabled={disabled}\n      type={type}\n      id={id}\n      name={name}\n      value={value}\n      className={cn(\n        \"relative font-medium backdrop-blur-xl transition-shadow duration-300 ease-in-out hover:shadow\",\n        \"dark:hover:shadow-[0_0_20px_hsl(var(--primary)/10%)]\",\n        \"flex items-center justify-center\",\n        sizeClasses[size],\n        className\n      )}\n      style={{\n        backgroundColor: colors.bg,\n        \"--primary\": colors.shimmer,\n      } as React.CSSProperties}\n    >\n      <span\n        className=\"relative flex items-center justify-center size-full uppercase tracking-wide dark:font-light\"\n        style={{\n          color: colors.text,\n          maskImage:\n            `linear-gradient(-75deg,${colors.shimmer} calc(var(--x) + 20%),transparent calc(var(--x) + 30%),${colors.shimmer} calc(var(--x) + 100%))`,\n        }}\n      >\n        {children}\n      </span>\n      <span\n        style={{\n          mask: \"linear-gradient(var(--mask-black), var(--mask-black)) content-box,linear-gradient(var(--mask-black), var(--mask-black))\",\n          maskComposite: \"exclude\",\n          background: `linear-gradient(-75deg,${colors.shimmer}10 calc(var(--x)+20%),${colors.shimmer}80 calc(var(--x)+25%),${colors.shimmer}10 calc(var(--x)+100%))`,\n        }}\n        className=\"absolute inset-0 z-10 block rounded-[inherit] p-px\"\n      ></span>\n    </motion.button>\n  );\n};\n\nexport default ShinyButton;\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAiBA,MAAM,iBAAiB;IACrB,SAAS;QAAE,OAAO;QAAQ,OAAO;IAAI;IACrC,SAAS;QAAE,OAAO;QAAS,OAAO;IAAE;IACpC,UAAU;QAAE,OAAO;IAAK;IACxB,YAAY;QACV,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;QACN,OAAO;YACL,MAAM;YACN,WAAW;YACX,SAAS;YACT,MAAM;QACR;IACF;AACF;AAEO,MAAM,cAA0C;QAAC,EACtD,QAAQ,EACR,SAAS,EACT,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,YAAY,EACZ,eAAe,EACf,SAAS,EACT,GAAG,OACJ;IACC,qBAAqB;IACrB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,4EAA4E;IAC5E,MAAM,YAAY;QAChB,MAAM,aAAa;YACjB,SAAS;gBACP,IAAI,mBAAmB;gBAAc,8BAA8B,GACnE,SAAS,gBAAgB;gBAAY,+BAA+B,GACpE,MAAM,aAAa,UAAkB,mCAAmC;YAC1E;YACA,WAAW;gBACT,IAAI,mBAAmB;gBAAc,mCAAmC,GACxE,SAAS,gBAAgB;gBAAY,+BAA+B,GACpE,MAAM,aAAa,UAAkB,kCAAkC;YACzE;YACA,QAAQ;gBACN,IAAI,mBAAmB;gBAAc,kCAAkC,GACvE,SAAS,gBAAgB;gBAAY,gCAAgC,GACrE,MAAM,aAAa,UAAkB,+BAA+B;YACtE;QACF;QACA,OAAO,UAAU,CAAC,QAAQ;IAC5B;IAEA,MAAM,SAAS;IAEf,MAAM,EACJ,OAAO,EACP,YAAY,EACZ,YAAY,EACZ,OAAO,EACP,MAAM,EACN,QAAQ,EACR,IAAI,EACJ,EAAE,EACF,IAAI,EACJ,KAAK,EACN,GAAG;IAEJ,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;QACX,GAAG,cAAc;QAClB,SAAS;QACT,cAAc;QACd,cAAc;QACd,SAAS;QACT,QAAQ;QACR,UAAU;QACV,MAAM;QACN,IAAI;QACJ,MAAM;QACN,OAAO;QACP,WAAW,CAAA,GAAA,+GAAA,CAAA,KAAE,AAAD,EACV,iGACA,wDACA,oCACA,WAAW,CAAC,KAAK,EACjB;QAEF,OAAO;YACL,iBAAiB,OAAO,EAAE;YAC1B,aAAa,OAAO,OAAO;QAC7B;;0BAEA,6LAAC;gBACC,WAAU;gBACV,OAAO;oBACL,OAAO,OAAO,IAAI;oBAClB,WACE,AAAC,0BAAiG,OAAxE,OAAO,OAAO,EAAC,2DAAwE,OAAf,OAAO,OAAO,EAAC;gBACrH;0BAEC;;;;;;0BAEH,6LAAC;gBACC,OAAO;oBACL,MAAM;oBACN,eAAe;oBACf,YAAY,AAAC,0BAAgE,OAAvC,OAAO,OAAO,EAAC,0BAA+D,OAAvC,OAAO,OAAO,EAAC,0BAAuC,OAAf,OAAO,OAAO,EAAC;gBACrI;gBACA,WAAU;;;;;;;;;;;;AAIlB;KAnGa;uCAqGE", "debugId": null}}]}