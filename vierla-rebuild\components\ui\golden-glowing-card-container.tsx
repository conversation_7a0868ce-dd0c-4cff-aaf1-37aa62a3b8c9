"use client";

import React from "react";
import { cn } from "@/lib/utils";
import { GlowingEffect } from "./glowing-effect";

interface GoldenGlowingCardContainerProps {
  children: React.ReactNode;
  className?: string;
  interactive?: boolean;
}

export const GoldenGlowingCardContainer: React.FC<GoldenGlowingCardContainerProps> = React.memo(({
  children,
  className,
  interactive = true,
}) => {
  return (
    <div className={cn("relative h-full group", className)}>
      {/* Outer container with glowing effect */}
      <div className="relative h-full rounded-[1.25rem] border-[0.75px] border-white/20 p-2 md:rounded-[1.5rem] md:p-3">
        <GlowingEffect
          spread={40}
          glow={interactive}
          disabled={!interactive}
          proximity={interactive ? 64 : 0}
          inactiveZone={interactive ? 0.01 : 1}
          borderWidth={3}
          variant="sage"
        />

        {/* Inner container with margin - Consistent glassmorphism */}
        <div className="relative flex h-full flex-col justify-between gap-6 overflow-hidden rounded-xl p-6 glassmorphism-card">
          {children}
        </div>
      </div>
    </div>
  );
});

export default GoldenGlowingCardContainer;
