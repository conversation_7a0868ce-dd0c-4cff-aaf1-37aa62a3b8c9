"use client";

import React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

export interface ShinyButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  className?: string;
  size?: "sm" | "md" | "lg";
  variant?: "primary" | "secondary" | "accent";
  shimmerColor?: string;
  backgroundColor?: string;
  textColor?: string;
}

const animationProps = {
  initial: { "--x": "100%", scale: 0.8 },
  animate: { "--x": "-100%", scale: 1 },
  whileTap: { scale: 0.95 },
  transition: {
    repeat: Infinity,
    repeatType: "loop" as const,
    repeatDelay: 1,
    type: "spring" as const,
    stiffness: 20,
    damping: 15,
    mass: 2,
    scale: {
      type: "spring" as const,
      stiffness: 200,
      damping: 5,
      mass: 0.5,
    },
  },
};

export const ShinyButton: React.FC<ShinyButtonProps> = ({
  children,
  className,
  size = "md",
  variant = "primary",
  shimmerColor,
  backgroundColor,
  textColor,
  ...props
}) => {
  // Size-based styling
  const sizeClasses = {
    sm: "px-3 py-2 text-sm rounded-md",
    md: "px-6 py-2 text-base rounded-lg",
    lg: "px-8 py-3 text-lg rounded-lg"
  };

  // Color variants with customization support - THEME-AWARE COLORS
  const getColors = () => {
    const baseColors = {
      primary: {
        // Light Mode: Vierla Forest, Dark Mode: Vierla Gold
        bg: backgroundColor || "var(--theme-primary, #364035)",
        shimmer: shimmerColor || "#F4F1E8",  /* Warm Cream - Shimmer effect */
        text: textColor || "#F4F1E8"         /* Warm Cream - Text on primary */
      },
      secondary: {
        // Light Mode: Vierla Sage, Dark Mode: Muted Gold/Champagne
        bg: backgroundColor || "var(--theme-secondary, #8B9A8C)",
        shimmer: shimmerColor || "#F4F1E8",  /* Warm Cream - Shimmer effect */
        text: textColor || "#F4F1E8"         /* Warm Cream - Text on secondary */
      },
      accent: {
        // Light Mode: Vierla Forest, Dark Mode: Vierla Gold
        bg: backgroundColor || "var(--theme-primary, #364035)",
        shimmer: shimmerColor || "var(--theme-secondary, #8B9A8C)",
        text: textColor || "#F4F1E8"         /* Warm Cream - Text on accent */
      }
    };
    return baseColors[variant];
  };

  const colors = getColors();

  const {
    onClick,
    onMouseEnter,
    onMouseLeave,
    onFocus,
    onBlur,
    disabled,
    type,
    id,
    name,
    value
  } = props;

  return (
    <motion.button
      {...animationProps}
      onClick={onClick}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      onFocus={onFocus}
      onBlur={onBlur}
      disabled={disabled}
      type={type}
      id={id}
      name={name}
      value={value}
      className={cn(
        "relative font-medium backdrop-blur-xl transition-shadow duration-300 ease-in-out hover:shadow",
        "dark:hover:shadow-[0_0_20px_hsl(var(--primary)/10%)]",
        "flex items-center justify-center",
        sizeClasses[size],
        className
      )}
      style={{
        backgroundColor: colors.bg,
        "--primary": colors.shimmer,
      } as React.CSSProperties}
    >
      <span
        className="relative flex items-center justify-center size-full uppercase tracking-wide dark:font-light"
        style={{
          ...(textColor && { color: colors.text }),
          maskImage:
            `linear-gradient(-75deg,${colors.shimmer} calc(var(--x) + 20%),transparent calc(var(--x) + 30%),${colors.shimmer} calc(var(--x) + 100%))`,
        }}
      >
        {children}
      </span>
      <span
        style={{
          mask: "linear-gradient(var(--mask-black), var(--mask-black)) content-box,linear-gradient(var(--mask-black), var(--mask-black))",
          maskComposite: "exclude",
          background: `linear-gradient(-75deg,${colors.shimmer}10 calc(var(--x)+20%),${colors.shimmer}80 calc(var(--x)+25%),${colors.shimmer}10 calc(var(--x)+100%))`,
        }}
        className="absolute inset-0 z-10 block rounded-[inherit] p-px"
      ></span>
    </motion.button>
  );
};

export default ShinyButton;
