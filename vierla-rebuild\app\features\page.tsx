import { Metadata } from 'next'
import { AuroraBackgroundLayer } from "@/components/ui/aurora-background";
import { BentoCard, BentoGrid } from "@/components/ui/bento-grid";
import { GetStartedButton } from "@/components/ui/get-started-button";
import { GoldenGlowingCardContainer } from "@/components/ui/golden-glowing-card-container";
import { StructuredData } from "@/components/seo/structured-data";
import { BreadcrumbSchema, getBreadcrumbsForPage } from "@/components/seo/breadcrumb-schema";
import { LayoutTemplate, FileText, Users, BarChart2, Palette, Layers, Brain, HeartHandshake } from "lucide-react";

export const metadata: Metadata = {
  title: 'Features - Powerful Business Tools',
  description: 'Discover Vierla\'s comprehensive business features: AI website builder, smart invoicing, CRM system, analytics dashboard, and brand management tools for beauty professionals.',
  openGraph: {
    title: 'Features - Powerful Business Tools | Vierla',
    description: 'Everything you need to build, manage, and grow your beauty business in one integrated platform.',
    url: 'https://vierla.com/features',
    siteName: 'Vierla',
    locale: 'en_CA',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Features - Powerful Business Tools | Vierla',
    description: 'Everything you need to build, manage, and grow your beauty business in one integrated platform.',
  },
};

export default function Features() {
  return (
    <div className="page-features relative overflow-hidden">
      <StructuredData page="features" />
      <BreadcrumbSchema items={getBreadcrumbsForPage('features')} />
      <AuroraBackgroundLayer />

      {/* Hero Section */}
      <section className="relative z-10 w-full px-4 py-20 pt-36">
        <div className="text-center max-w-6xl mx-auto">
          <h1 className="text-4xl md:text-6xl font-black mb-6 leading-none text-[#2D2A26] dark:text-[#F4F1E8] drop-shadow-lg font-sans">
            POWERFUL FEATURES FOR YOUR BUSINESS
          </h1>
          <p className="text-xl md:text-2xl text-[#2D2A26] dark:text-[#A9A299] mb-8 leading-relaxed max-w-4xl mx-auto drop-shadow-sm font-inter">
            Everything you need to build, manage, and grow your business in one integrated platform.
          </p>
        </div>
      </section>

      {/* Features Bento Grid */}
      <section className="relative z-10 py-20 border-t border-white/20">
        <div className="container mx-auto px-4">
          <BentoGrid className="lg:grid-rows-3 max-w-6xl mx-auto min-h-[800px]">
            {[
              {
                Icon: LayoutTemplate,
                name: "Website Builder",
                description: "Create stunning, professional websites with our intuitive drag-and-drop builder. Choose from industry-specific templates, customize your brand colors and fonts, integrate booking systems, and launch your online presence without any coding knowledge. Perfect for beauty professionals who want to showcase their work and attract new clients.",
                href: "/contact",
                cta: "Contact Us",
                background: <div className="absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full blur-3xl" />,
                className: "lg:row-start-1 lg:row-end-4 lg:col-start-2 lg:col-end-3",
              },
              {
                Icon: FileText,
                name: "Smart Invoicing",
                description: "Generate professional invoices instantly and get paid faster with automated payment reminders and multiple payment options. Track payment status, manage recurring billing for regular clients, apply discounts and taxes automatically, and maintain detailed financial records. Streamline your billing process and improve cash flow with intelligent payment tracking.",
                href: "/contact",
                cta: "Contact Us",
                background: <div className="absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full blur-3xl" />,
                className: "lg:col-start-1 lg:col-end-2 lg:row-start-1 lg:row-end-3",
              },
              {
                Icon: Users,
                name: "CRM System",
                description: "Manage customer relationships with a comprehensive CRM that tracks client preferences, service history, and communication logs. Set up automated follow-ups, manage leads through your sales pipeline, segment clients for targeted marketing campaigns, and build lasting relationships that drive repeat business and referrals.",
                href: "/contact",
                cta: "Contact Us",
                background: <div className="absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full blur-3xl" />,
                className: "lg:col-start-1 lg:col-end-2 lg:row-start-3 lg:row-end-4",
              },
              {
                Icon: BarChart2,
                name: "Analytics Dashboard",
                description: "Get comprehensive insights into your business performance with real-time analytics and detailed reporting. Track revenue trends, monitor client acquisition costs, analyze service popularity, measure marketing campaign effectiveness, and identify growth opportunities. Make data-driven decisions to optimize your business operations and maximize profitability.",
                href: "/contact",
                cta: "Contact Us",
                background: <div className="absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full blur-3xl" />,
                className: "lg:col-start-3 lg:col-end-3 lg:row-start-1 lg:row-end-2",
              },
              {
                Icon: Palette,
                name: "Brand Management",
                description: "Maintain consistent branding across all your business touchpoints with centralized brand management tools. Upload your logo, define brand colors and fonts, create branded templates for invoices and marketing materials, and ensure professional consistency across your website, social media, and client communications. Build a memorable brand that stands out in the beauty industry.",
                href: "/contact",
                cta: "Contact Us",
                background: <div className="absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full blur-3xl" />,
                className: "lg:col-start-3 lg:col-end-3 lg:row-start-2 lg:row-end-4",
              },
            ].map((feature) => (
              <BentoCard key={feature.name} {...feature} />
            ))}
          </BentoGrid>
        </div>
      </section>

      {/* The Vierla Advantage Section */}
      <section className="relative z-10 py-16 border-t border-white/20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-4xl md:text-5xl font-black text-[#2D2A26] dark:text-[#F4F1E8] mb-6 drop-shadow-lg font-jost">
              The Vierla Advantage
            </h2>
            <p className="text-xl text-[#2D2A26] dark:text-[#A9A299] max-w-3xl mx-auto drop-shadow-sm font-inter">
              Discover what sets Vierla apart as the ultimate platform for beauty professionals and business owners.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {/* All-in-One Platform */}
            <GoldenGlowingCardContainer>
              <div className="text-center p-6">
                <div className="w-16 h-16 mx-auto mb-6 rounded-full flex items-center justify-center bg-gradient-to-br from-theme-primary/20 to-theme-primary/10 border-2 border-theme-primary/30">
                  <Layers className="w-8 h-8" style={{ color: 'var(--theme-primary)' }} />
                </div>
                <h3 className="text-2xl font-bold text-[#2D2A26] dark:text-[#F4F1E8] mb-4 drop-shadow-lg font-tai-heritage">All-in-One Platform</h3>
                <p className="text-sm sm:text-base text-[#2D2A26] dark:text-[#A9A299] leading-relaxed drop-shadow-sm font-inter">
                  Consolidate your tools for booking, marketing, and payments into one seamless platform. No more juggling multiple subscriptions or learning different systems. Everything you need to run your beauty business is right here.
                </p>
              </div>
            </GoldenGlowingCardContainer>

            {/* AI-Powered Insights */}
            <GoldenGlowingCardContainer>
              <div className="text-center p-6">
                <div className="w-16 h-16 mx-auto mb-6 rounded-full flex items-center justify-center bg-gradient-to-br from-theme-primary/20 to-theme-primary/10 border-2 border-theme-primary/30">
                  <Brain className="w-8 h-8" style={{ color: 'var(--theme-primary)' }} />
                </div>
                <h3 className="text-2xl font-bold text-[#2D2A26] dark:text-[#F4F1E8] mb-4 drop-shadow-lg font-tai-heritage">AI-Powered Insights</h3>
                <p className="text-sm sm:text-base text-[#2D2A26] dark:text-[#A9A299] leading-relaxed drop-shadow-sm font-inter">
                  Leverage data to grow your business and understand your clients better. Our AI analyzes booking patterns, client preferences, and market trends to provide actionable insights that drive revenue growth and client satisfaction.
                </p>
              </div>
            </GoldenGlowingCardContainer>

            {/* Dedicated Growth Partner */}
            <GoldenGlowingCardContainer>
              <div className="text-center p-6">
                <div className="w-16 h-16 mx-auto mb-6 rounded-full flex items-center justify-center bg-gradient-to-br from-theme-primary/20 to-theme-primary/10 border-2 border-theme-primary/30">
                  <HeartHandshake className="w-8 h-8" style={{ color: 'var(--theme-primary)' }} />
                </div>
                <h3 className="text-2xl font-bold text-[#2D2A26] dark:text-[#F4F1E8] mb-4 drop-shadow-lg font-tai-heritage">Dedicated Growth Partner</h3>
                <p className="text-sm sm:text-base text-[#2D2A26] dark:text-[#A9A299] leading-relaxed drop-shadow-sm font-inter">
                  Access hands-on support focused on your success. Our dedicated team provides personalized guidance, marketing strategies, and business coaching to help you achieve your goals and build a thriving beauty business.
                </p>
              </div>
            </GoldenGlowingCardContainer>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative z-10 py-12 border-t border-white/20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <GoldenGlowingCardContainer interactive={false}>
              <div className="text-center pt-8 pb-4 px-6">
                <h2 className="font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl text-[#2D2A26] dark:text-[#F4F1E8] drop-shadow-lg font-jost mb-6">
                  Ready to transform your business?
                </h2>
                <p className="max-w-[42rem] leading-normal text-[#2D2A26] dark:text-[#A9A299] sm:text-xl sm:leading-8 drop-shadow-sm font-inter mb-8 mx-auto">
                  Join beauty entrepreneurs and business owners who trust Vierla to power their business operations.
                </p>
                <div className="flex justify-center">
                  <a href="/apply">
                    <GetStartedButton />
                  </a>
                </div>
              </div>
            </GoldenGlowingCardContainer>
          </div>
        </div>
      </section>
    </div>
  );
}
