import { Metadata } from 'next'
import { AuroraBackgroundLayer } from "@/components/ui/aurora-background";
import { MultiStepForm } from "@/components/marketing/multistep-form";
import { StructuredData } from "@/components/seo/structured-data";
import { BreadcrumbSchema, getBreadcrumbsForPage } from "@/components/seo/breadcrumb-schema";

export const metadata: Metadata = {
  title: 'Apply to Jo<PERSON> - Beauty Professional Application',
  description: 'Apply to become a verified beauty professional on Vierla. Join our network of trusted hair stylists, makeup artists, nail technicians, and skincare experts.',
  openGraph: {
    title: 'Apply to <PERSON><PERSON> - Beauty Professional Application | Vierla',
    description: 'Apply to become a verified beauty professional on <PERSON>ierla and start growing your business.',
    url: 'https://vierla.com/apply',
    siteName: 'Vierla',
    locale: 'en_CA',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Apply to Jo<PERSON> - Beauty Professional Application | Vierla',
    description: 'Apply to become a verified beauty professional on Vierla and start growing your business.',
  },
};

export default function Apply() {
  return (
    <div className="page-apply relative overflow-hidden">
      <StructuredData page="apply" />
      <BreadcrumbSchema items={getBreadcrumbsForPage('apply')} />
      <AuroraBackgroundLayer />

      {/* Header */}
      <section className="relative z-10 w-full px-4 py-20 pt-32">
        <div className="text-center max-w-4xl mx-auto mb-12">
          <h1 className="text-4xl md:text-6xl font-black mb-6 leading-none text-light-off-white drop-shadow-lg font-sans">
            Join Vierla's Professional Network
          </h1>
          <p className="text-xl md:text-2xl text-warm-beige mb-8 leading-relaxed max-w-3xl mx-auto drop-shadow-sm font-sans">
            Apply to become a verified beauty professional on our platform. Connect with clients and grow your business with our comprehensive suite of tools.
          </p>
        </div>

        <MultiStepForm />
      </section>
    </div>
  );
}
