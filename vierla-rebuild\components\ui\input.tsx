import * as React from "react"

import { cn } from "@/lib/utils"

const Input = React.forwardRef<HTMLInputElement, React.ComponentProps<"input">>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          // Updated to use new Vierla color specifications
          "flex h-10 w-full rounded-md border transition-all duration-200",
          "bg-[var(--master-input-bg-light)] dark:bg-[var(--master-input-bg-dark)]",
          "border-[var(--master-input-border)] text-[var(--master-text-primary-light)] dark:text-[var(--master-text-primary-dark)]",
          "px-3 py-2 text-base font-[var(--font-family-body)]",
          "placeholder:text-[var(--master-text-secondary-light)] dark:placeholder:text-[var(--master-text-secondary-dark)]",
          "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-[var(--master-input-border-focus)] focus-visible:border-[var(--master-input-border-focus)]",
          "disabled:cursor-not-allowed disabled:opacity-50",
          "file:border-0 file:bg-transparent file:text-sm file:font-medium",
          "md:text-sm",
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Input.displayName = "Input"

export { Input }
