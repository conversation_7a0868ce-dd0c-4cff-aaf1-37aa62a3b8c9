import Script from 'next/script'

interface StructuredDataProps {
  page?: 'home' | 'features' | 'pricing' | 'about' | 'contact' | 'providers' | 'apply'
}

export function StructuredData({ page = 'home' }: StructuredDataProps) {
  // Organization Schema - Core business information
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "Vierla",
    "alternateName": "Vierla Inc.",
    "url": "https://vierla.com",
    "logo": "https://vierla.com/logo-transparent.png",
    "description": "The ultimate marketplace connecting you with top beauty professionals, and the all-in-one tool for providers to manage and grow their business.",
    "foundingDate": "2025",
    "foundingLocation": {
      "@type": "Place",
      "address": {
        "@type": "PostalAddress",
        "addressLocality": "Toronto",
        "addressRegion": "Ontario",
        "addressCountry": "Canada"
      }
    },
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "******-XXX-XXXX",
      "contactType": "customer service",
      "email": "<EMAIL>",
      "availableLanguage": ["English", "French"]
    },
    "areaServed": [
      {
        "@type": "City",
        "name": "Toronto",
        "addressRegion": "Ontario",
        "addressCountry": "Canada"
      },
      {
        "@type": "City",
        "name": "Ottawa",
        "addressRegion": "Ontario", 
        "addressCountry": "Canada"
      },
      {
        "@type": "City",
        "name": "Vancouver",
        "addressRegion": "British Columbia",
        "addressCountry": "Canada"
      },
      {
        "@type": "City",
        "name": "Calgary",
        "addressRegion": "Alberta",
        "addressCountry": "Canada"
      }
    ],
    "serviceType": [
      "Hair Styling",
      "Makeup Services", 
      "Nail Services",
      "Skincare Services",
      "Eyebrow Services",
      "Eyelash Services",
      "Henna Services",
      "Barbering Services",
      "Braiding Services",
      "Loc Services"
    ]
  }

  // Website Schema - Search functionality
  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "Vierla",
    "url": "https://vierla.com",
    "description": "Self-Care, Simplified. Connect with top beauty professionals for premium beauty services.",
    "publisher": {
      "@type": "Organization",
      "name": "Vierla"
    },
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://vierla.com/search?q={search_term_string}",
      "query-input": "required name=search_term_string"
    }
  }

  // Service Schema - Beauty marketplace service
  const serviceSchema = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "Beauty Services Marketplace",
    "description": "Connect with verified beauty professionals for hair, makeup, nails, skincare, and more. Mobile and studio options available.",
    "provider": {
      "@type": "Organization",
      "name": "Vierla"
    },
    "serviceType": "Beauty Services Marketplace",
    "areaServed": [
      {
        "@type": "City",
        "name": "Toronto",
        "addressRegion": "Ontario",
        "addressCountry": "Canada"
      },
      {
        "@type": "City", 
        "name": "Ottawa",
        "addressRegion": "Ontario",
        "addressCountry": "Canada"
      },
      {
        "@type": "City",
        "name": "Vancouver", 
        "addressRegion": "British Columbia",
        "addressCountry": "Canada"
      },
      {
        "@type": "City",
        "name": "Calgary",
        "addressRegion": "Alberta", 
        "addressCountry": "Canada"
      }
    ],
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Beauty Services",
      "itemListElement": [
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Hair Styling Services",
            "description": "Professional hair cuts, styling, coloring, and treatments by master stylists"
          }
        },
        {
          "@type": "Offer", 
          "itemOffered": {
            "@type": "Service",
            "name": "Makeup Services",
            "description": "Professional makeup application for events, bridal, and everyday looks"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service", 
            "name": "Nail Services",
            "description": "Manicures, pedicures, and custom nail art by certified technicians"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Skincare Services", 
            "description": "Professional facials, treatments, and skincare consultations"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Barbering Services",
            "description": "Precision cuts, expert fades, and luxury hot towel shaves"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Braiding Services",
            "description": "Intricate cornrows, designer box braids, and protective styling"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Loc Services", 
            "description": "Expert loc maintenance, retwisting, and styling"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Eyebrow Services",
            "description": "Expert shaping, threading, tinting, and microblading"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Eyelash Services",
            "description": "Lash extensions, lifts, and tinting for beautiful lashes"
          }
        },
        {
          "@type": "Offer",
          "itemOffered": {
            "@type": "Service",
            "name": "Henna Services",
            "description": "Traditional henna artistry and custom body art patterns"
          }
        }
      ]
    }
  }

  // Page-specific schemas
  let pageSpecificSchemas: any[] = []

  if (page === 'features') {
    pageSpecificSchemas.push({
      "@context": "https://schema.org",
      "@type": "SoftwareApplication",
      "name": "Vierla Business Platform",
      "description": "All-in-one business management platform for beauty professionals",
      "applicationCategory": "BusinessApplication",
      "operatingSystem": "Web Browser",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "CAD",
        "description": "Free tier available with premium features"
      },
      "featureList": [
        "AI Website Builder",
        "Smart Invoicing", 
        "CRM System",
        "Analytics Dashboard",
        "Brand Management",
        "Client Booking System",
        "Payment Processing",
        "Professional Portfolio"
      ]
    })
  }

  if (page === 'pricing') {
    pageSpecificSchemas.push({
      "@context": "https://schema.org",
      "@type": "Product",
      "name": "Vierla Business Plans",
      "description": "Flexible pricing plans for beauty professionals and businesses",
      "offers": [
        {
          "@type": "Offer",
          "name": "Starter Plan",
          "price": "0",
          "priceCurrency": "CAD",
          "description": "Perfect for individual professionals starting their journey"
        },
        {
          "@type": "Offer", 
          "name": "Professional Plan",
          "price": "29",
          "priceCurrency": "CAD",
          "description": "Ideal for established professionals looking to grow"
        },
        {
          "@type": "Offer",
          "name": "Enterprise Plan", 
          "price": "99",
          "priceCurrency": "CAD",
          "description": "Comprehensive solution for large businesses and teams"
        }
      ]
    })
  }

  return (
    <>
      <Script
        id="organization-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationSchema)
        }}
      />
      <Script
        id="website-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteSchema)
        }}
      />
      <Script
        id="service-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(serviceSchema)
        }}
      />
      {pageSpecificSchemas.map((schema, index) => (
        <Script
          key={`page-schema-${index}`}
          id={`page-schema-${index}`}
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(schema)
          }}
        />
      ))}
    </>
  )
}
