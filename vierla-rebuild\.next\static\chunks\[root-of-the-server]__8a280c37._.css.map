{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/jost_cd42f72b.module.css"], "sourcesContent": ["/* cyrillic */\n@font-face {\n  font-family: 'Jost';\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/jost/v19/92zatBhPNqw73oDd4jQmfxIC7w.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Jost';\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/jost/v19/92zatBhPNqw73ord4jQmfxIC7w.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Jost';\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/jost/v19/92zatBhPNqw73oTd4jQmfxI.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n/* cyrillic */\n@font-face {\n  font-family: 'Jost';\n  font-style: normal;\n  font-weight: 500;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/jost/v19/92zatBhPNqw73oDd4jQmfxIC7w.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Jost';\n  font-style: normal;\n  font-weight: 500;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/jost/v19/92zatBhPNqw73ord4jQmfxIC7w.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Jost';\n  font-style: normal;\n  font-weight: 500;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/jost/v19/92zatBhPNqw73oTd4jQmfxI.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n/* cyrillic */\n@font-face {\n  font-family: 'Jost';\n  font-style: normal;\n  font-weight: 600;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/jost/v19/92zatBhPNqw73oDd4jQmfxIC7w.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Jost';\n  font-style: normal;\n  font-weight: 600;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/jost/v19/92zatBhPNqw73ord4jQmfxIC7w.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Jost';\n  font-style: normal;\n  font-weight: 600;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/jost/v19/92zatBhPNqw73oTd4jQmfxI.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n/* cyrillic */\n@font-face {\n  font-family: 'Jost';\n  font-style: normal;\n  font-weight: 700;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/jost/v19/92zatBhPNqw73oDd4jQmfxIC7w.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Jost';\n  font-style: normal;\n  font-weight: 700;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/jost/v19/92zatBhPNqw73ord4jQmfxIC7w.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Jost';\n  font-style: normal;\n  font-weight: 700;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/jost/v19/92zatBhPNqw73oTd4jQmfxI.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n@font-face {\n    font-family: 'Jost Fallback';\n    src: local(\"Arial\");\n    ascent-override: 111.45%;\ndescent-override: 39.06%;\nline-gap-override: 0.00%;\nsize-adjust: 96.01%;\n\n}\n.className {\n    font-family: 'Jost', 'Jost Fallback';\n    font-style: normal;\n\n}\n.variable {\n    --font-jost: 'Jost', 'Jost Fallback';\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AAQA;;;;;;;;;AASA;;;;;AAKA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_3c883839.module.css"], "sourcesContent": ["/* cyrillic-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;\n}\n/* cyrillic */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa0ZL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* greek-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2ZL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+1F00-1FFF;\n}\n/* greek */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1pL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;\n}\n/* vietnamese */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2pL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa25L7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n/* cyrillic-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 500;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;\n}\n/* cyrillic */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 500;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa0ZL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* greek-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 500;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2ZL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+1F00-1FFF;\n}\n/* greek */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 500;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1pL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;\n}\n/* vietnamese */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 500;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2pL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 500;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa25L7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 500;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n/* cyrillic-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 600;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;\n}\n/* cyrillic */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 600;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa0ZL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* greek-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 600;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2ZL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+1F00-1FFF;\n}\n/* greek */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 600;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1pL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;\n}\n/* vietnamese */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 600;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2pL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 600;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa25L7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 600;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n/* cyrillic-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 700;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2JL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;\n}\n/* cyrillic */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 700;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa0ZL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* greek-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 700;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2ZL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+1F00-1FFF;\n}\n/* greek */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 700;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1pL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;\n}\n/* vietnamese */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 700;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa2pL7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 700;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa25L7W0Q5n-wU.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Inter';\n  font-style: normal;\n  font-weight: 700;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/inter/v19/UcC73FwrK3iLTeHuS_nVMrMxCp50SjIa1ZL7W0Q5nw.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n@font-face {\n    font-family: 'Inter Fallback';\n    src: local(\"Arial\");\n    ascent-override: 90.44%;\ndescent-override: 22.52%;\nline-gap-override: 0.00%;\nsize-adjust: 107.12%;\n\n}\n.className {\n    font-family: 'Inter', 'Inter Fallback';\n    font-style: normal;\n\n}\n.variable {\n    --font-inter: 'Inter', 'Inter Fallback';\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AAQA;;;;;;;;;AASA;;;;;AAKA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 399, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/playfair_display_70aae4f1.module.css"], "sourcesContent": ["/* cyrillic */\n@font-face {\n  font-family: 'Playfair Display';\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/playfairdisplay/v39/nuFiD-vYSZviVYUb_rj3ij__anPXDTjYgEM86xRbPQ.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* vietnamese */\n@font-face {\n  font-family: 'Playfair Display';\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/playfairdisplay/v39/nuFiD-vYSZviVYUb_rj3ij__anPXDTPYgEM86xRbPQ.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Playfair Display';\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/playfairdisplay/v39/nuFiD-vYSZviVYUb_rj3ij__anPXDTLYgEM86xRbPQ.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Playfair Display';\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/playfairdisplay/v39/nuFiD-vYSZviVYUb_rj3ij__anPXDTzYgEM86xQ.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n/* cyrillic */\n@font-face {\n  font-family: 'Playfair Display';\n  font-style: normal;\n  font-weight: 500;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/playfairdisplay/v39/nuFiD-vYSZviVYUb_rj3ij__anPXDTjYgEM86xRbPQ.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* vietnamese */\n@font-face {\n  font-family: 'Playfair Display';\n  font-style: normal;\n  font-weight: 500;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/playfairdisplay/v39/nuFiD-vYSZviVYUb_rj3ij__anPXDTPYgEM86xRbPQ.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Playfair Display';\n  font-style: normal;\n  font-weight: 500;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/playfairdisplay/v39/nuFiD-vYSZviVYUb_rj3ij__anPXDTLYgEM86xRbPQ.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Playfair Display';\n  font-style: normal;\n  font-weight: 500;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/playfairdisplay/v39/nuFiD-vYSZviVYUb_rj3ij__anPXDTzYgEM86xQ.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n/* cyrillic */\n@font-face {\n  font-family: 'Playfair Display';\n  font-style: normal;\n  font-weight: 600;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/playfairdisplay/v39/nuFiD-vYSZviVYUb_rj3ij__anPXDTjYgEM86xRbPQ.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* vietnamese */\n@font-face {\n  font-family: 'Playfair Display';\n  font-style: normal;\n  font-weight: 600;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/playfairdisplay/v39/nuFiD-vYSZviVYUb_rj3ij__anPXDTPYgEM86xRbPQ.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Playfair Display';\n  font-style: normal;\n  font-weight: 600;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/playfairdisplay/v39/nuFiD-vYSZviVYUb_rj3ij__anPXDTLYgEM86xRbPQ.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Playfair Display';\n  font-style: normal;\n  font-weight: 600;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/playfairdisplay/v39/nuFiD-vYSZviVYUb_rj3ij__anPXDTzYgEM86xQ.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n/* cyrillic */\n@font-face {\n  font-family: 'Playfair Display';\n  font-style: normal;\n  font-weight: 700;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/playfairdisplay/v39/nuFiD-vYSZviVYUb_rj3ij__anPXDTjYgEM86xRbPQ.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;\n}\n/* vietnamese */\n@font-face {\n  font-family: 'Playfair Display';\n  font-style: normal;\n  font-weight: 700;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/playfairdisplay/v39/nuFiD-vYSZviVYUb_rj3ij__anPXDTPYgEM86xRbPQ.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Playfair Display';\n  font-style: normal;\n  font-weight: 700;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/playfairdisplay/v39/nuFiD-vYSZviVYUb_rj3ij__anPXDTLYgEM86xRbPQ.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Playfair Display';\n  font-style: normal;\n  font-weight: 700;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/playfairdisplay/v39/nuFiD-vYSZviVYUb_rj3ij__anPXDTzYgEM86xQ.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n@font-face {\n    font-family: 'Playfair Display Fallback';\n    src: local(\"Times New Roman\");\n    ascent-override: 97.25%;\ndescent-override: 22.56%;\nline-gap-override: 0.00%;\nsize-adjust: 111.26%;\n\n}\n.className {\n    font-family: 'Playfair Display', 'Playfair Display Fallback';\n    font-style: normal;\n\n}\n.variable {\n    --font-tai-heritage: 'Playfair Display', 'Playfair Display Fallback';\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AAQA;;;;;;;;;AASA;;;;;AAKA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 562, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/dancing_script_13037ae7.module.css"], "sourcesContent": ["/* vietnamese */\n@font-face {\n  font-family: 'Dancing Script';\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/dancingscript/v28/If2RXTr6YS-zF4S-kcSWSVi_szLviuEHiC4Wl-8.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Dancing Script';\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/dancingscript/v28/If2RXTr6YS-zF4S-kcSWSVi_szLuiuEHiC4Wl-8.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Dancing Script';\n  font-style: normal;\n  font-weight: 400;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/dancingscript/v28/If2RXTr6YS-zF4S-kcSWSVi_szLgiuEHiC4W.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n/* vietnamese */\n@font-face {\n  font-family: 'Dancing Script';\n  font-style: normal;\n  font-weight: 500;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/dancingscript/v28/If2RXTr6YS-zF4S-kcSWSVi_szLviuEHiC4Wl-8.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Dancing Script';\n  font-style: normal;\n  font-weight: 500;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/dancingscript/v28/If2RXTr6YS-zF4S-kcSWSVi_szLuiuEHiC4Wl-8.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Dancing Script';\n  font-style: normal;\n  font-weight: 500;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/dancingscript/v28/If2RXTr6YS-zF4S-kcSWSVi_szLgiuEHiC4W.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n/* vietnamese */\n@font-face {\n  font-family: 'Dancing Script';\n  font-style: normal;\n  font-weight: 600;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/dancingscript/v28/If2RXTr6YS-zF4S-kcSWSVi_szLviuEHiC4Wl-8.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Dancing Script';\n  font-style: normal;\n  font-weight: 600;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/dancingscript/v28/If2RXTr6YS-zF4S-kcSWSVi_szLuiuEHiC4Wl-8.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Dancing Script';\n  font-style: normal;\n  font-weight: 600;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/dancingscript/v28/If2RXTr6YS-zF4S-kcSWSVi_szLgiuEHiC4W.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n/* vietnamese */\n@font-face {\n  font-family: 'Dancing Script';\n  font-style: normal;\n  font-weight: 700;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/dancingscript/v28/If2RXTr6YS-zF4S-kcSWSVi_szLviuEHiC4Wl-8.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;\n}\n/* latin-ext */\n@font-face {\n  font-family: 'Dancing Script';\n  font-style: normal;\n  font-weight: 700;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/dancingscript/v28/If2RXTr6YS-zF4S-kcSWSVi_szLuiuEHiC4Wl-8.woff2%22,%22preload%22:false,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;\n}\n/* latin */\n@font-face {\n  font-family: 'Dancing Script';\n  font-style: normal;\n  font-weight: 700;\n  font-display: swap;\n  src: url(@vercel/turbopack-next/internal/font/google/font?{%22url%22:%22https://fonts.gstatic.com/s/dancingscript/v28/If2RXTr6YS-zF4S-kcSWSVi_szLgiuEHiC4W.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}) format('woff2');\n  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;\n}\n@font-face {\n    font-family: 'Dancing Script Fallback';\n    src: local(\"Arial\");\n    ascent-override: 112.99%;\ndescent-override: 34.39%;\nline-gap-override: 0.00%;\nsize-adjust: 81.43%;\n\n}\n.className {\n    font-family: 'Dancing Script', 'Dancing Script Fallback';\n    font-style: normal;\n\n}\n.variable {\n    --font-farsan: 'Dancing Script', 'Dancing Script Fallback';\n}\n"], "names": [], "mappings": "AACA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AASA;;;;;;;;;AAQA;;;;;;;;;AASA;;;;;AAKA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 689, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/app/globals.css"], "sourcesContent": ["*, ::before, ::after {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}\n\n::backdrop {\n  --tw-border-spacing-x: 0;\n  --tw-border-spacing-y: 0;\n  --tw-translate-x: 0;\n  --tw-translate-y: 0;\n  --tw-rotate: 0;\n  --tw-skew-x: 0;\n  --tw-skew-y: 0;\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  --tw-pan-x:  ;\n  --tw-pan-y:  ;\n  --tw-pinch-zoom:  ;\n  --tw-scroll-snap-strictness: proximity;\n  --tw-gradient-from-position:  ;\n  --tw-gradient-via-position:  ;\n  --tw-gradient-to-position:  ;\n  --tw-ordinal:  ;\n  --tw-slashed-zero:  ;\n  --tw-numeric-figure:  ;\n  --tw-numeric-spacing:  ;\n  --tw-numeric-fraction:  ;\n  --tw-ring-inset:  ;\n  --tw-ring-offset-width: 0px;\n  --tw-ring-offset-color: #fff;\n  --tw-ring-color: rgb(59 130 246 / 0.5);\n  --tw-ring-offset-shadow: 0 0 #0000;\n  --tw-ring-shadow: 0 0 #0000;\n  --tw-shadow: 0 0 #0000;\n  --tw-shadow-colored: 0 0 #0000;\n  --tw-blur:  ;\n  --tw-brightness:  ;\n  --tw-contrast:  ;\n  --tw-grayscale:  ;\n  --tw-hue-rotate:  ;\n  --tw-invert:  ;\n  --tw-saturate:  ;\n  --tw-sepia:  ;\n  --tw-drop-shadow:  ;\n  --tw-backdrop-blur:  ;\n  --tw-backdrop-brightness:  ;\n  --tw-backdrop-contrast:  ;\n  --tw-backdrop-grayscale:  ;\n  --tw-backdrop-hue-rotate:  ;\n  --tw-backdrop-invert:  ;\n  --tw-backdrop-opacity:  ;\n  --tw-backdrop-saturate:  ;\n  --tw-backdrop-sepia:  ;\n  --tw-contain-size:  ;\n  --tw-contain-layout:  ;\n  --tw-contain-paint:  ;\n  --tw-contain-style:  ;\n}/*\n! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com\n*//*\n1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)\n2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)\n*/\n\n*,\n::before,\n::after {\n  box-sizing: border-box; /* 1 */\n  border-width: 0; /* 2 */\n  border-style: solid; /* 2 */\n  border-color: #e5e7eb; /* 2 */\n}\n\n::before,\n::after {\n  --tw-content: '';\n}\n\n/*\n1. Use a consistent sensible line-height in all browsers.\n2. Prevent adjustments of font size after orientation changes in iOS.\n3. Use a more readable tab size.\n4. Use the user's configured `sans` font-family by default.\n5. Use the user's configured `sans` font-feature-settings by default.\n6. Use the user's configured `sans` font-variation-settings by default.\n7. Disable tap highlights on iOS\n*/\n\nhtml,\n:host {\n  line-height: 1.5; /* 1 */\n  -webkit-text-size-adjust: 100%; /* 2 */\n  -moz-tab-size: 4; /* 3 */\n  tab-size: 4; /* 3 */\n  font-family: var(--font-inter), Inter, system-ui, sans-serif; /* 4 */\n  font-feature-settings: normal; /* 5 */\n  font-variation-settings: normal; /* 6 */\n  -webkit-tap-highlight-color: transparent; /* 7 */\n}\n\n/*\n1. Remove the margin in all browsers.\n2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.\n*/\n\nbody {\n  margin: 0; /* 1 */\n  line-height: inherit; /* 2 */\n}\n\n/*\n1. Add the correct height in Firefox.\n2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)\n3. Ensure horizontal rules are visible by default.\n*/\n\nhr {\n  height: 0; /* 1 */\n  color: inherit; /* 2 */\n  border-top-width: 1px; /* 3 */\n}\n\n/*\nAdd the correct text decoration in Chrome, Edge, and Safari.\n*/\n\nabbr:where([title]) {\n  text-decoration: underline dotted;\n}\n\n/*\nRemove the default font size and weight for headings.\n*/\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n  font-size: inherit;\n  font-weight: inherit;\n}\n\n/*\nReset links to optimize for opt-in styling instead of opt-out.\n*/\n\na {\n  color: inherit;\n  text-decoration: inherit;\n}\n\n/*\nAdd the correct font weight in Edge and Safari.\n*/\n\nb,\nstrong {\n  font-weight: bolder;\n}\n\n/*\n1. Use the user's configured `mono` font-family by default.\n2. Use the user's configured `mono` font-feature-settings by default.\n3. Use the user's configured `mono` font-variation-settings by default.\n4. Correct the odd `em` font sizing in all browsers.\n*/\n\ncode,\nkbd,\nsamp,\npre {\n  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace; /* 1 */\n  font-feature-settings: normal; /* 2 */\n  font-variation-settings: normal; /* 3 */\n  font-size: 1em; /* 4 */\n}\n\n/*\nAdd the correct font size in all browsers.\n*/\n\nsmall {\n  font-size: 80%;\n}\n\n/*\nPrevent `sub` and `sup` elements from affecting the line height in all browsers.\n*/\n\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\nsup {\n  top: -0.5em;\n}\n\n/*\n1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)\n2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)\n3. Remove gaps between table borders by default.\n*/\n\ntable {\n  text-indent: 0; /* 1 */\n  border-color: inherit; /* 2 */\n  border-collapse: collapse; /* 3 */\n}\n\n/*\n1. Change the font styles in all browsers.\n2. Remove the margin in Firefox and Safari.\n3. Remove default padding in all browsers.\n*/\n\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: inherit; /* 1 */\n  font-feature-settings: inherit; /* 1 */\n  font-variation-settings: inherit; /* 1 */\n  font-size: 100%; /* 1 */\n  font-weight: inherit; /* 1 */\n  line-height: inherit; /* 1 */\n  letter-spacing: inherit; /* 1 */\n  color: inherit; /* 1 */\n  margin: 0; /* 2 */\n  padding: 0; /* 3 */\n}\n\n/*\nRemove the inheritance of text transform in Edge and Firefox.\n*/\n\nbutton,\nselect {\n  text-transform: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Remove default button styles.\n*/\n\nbutton,\ninput:where([type='button']),\ninput:where([type='reset']),\ninput:where([type='submit']) {\n  -webkit-appearance: button; /* 1 */\n  background-color: transparent; /* 2 */\n  background-image: none; /* 2 */\n}\n\n/*\nUse the modern Firefox focus style for all focusable elements.\n*/\n\n:-moz-focusring {\n  outline: auto;\n}\n\n/*\nRemove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)\n*/\n\n:-moz-ui-invalid {\n  box-shadow: none;\n}\n\n/*\nAdd the correct vertical alignment in Chrome and Firefox.\n*/\n\nprogress {\n  vertical-align: baseline;\n}\n\n/*\nCorrect the cursor style of increment and decrement buttons in Safari.\n*/\n\n::-webkit-inner-spin-button,\n::-webkit-outer-spin-button {\n  height: auto;\n}\n\n/*\n1. Correct the odd appearance in Chrome and Safari.\n2. Correct the outline style in Safari.\n*/\n\n[type='search'] {\n  -webkit-appearance: textfield; /* 1 */\n  outline-offset: -2px; /* 2 */\n}\n\n/*\nRemove the inner padding in Chrome and Safari on macOS.\n*/\n\n::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n/*\n1. Correct the inability to style clickable types in iOS and Safari.\n2. Change font properties to `inherit` in Safari.\n*/\n\n::-webkit-file-upload-button {\n  -webkit-appearance: button; /* 1 */\n  font: inherit; /* 2 */\n}\n\n/*\nAdd the correct display in Chrome and Safari.\n*/\n\nsummary {\n  display: list-item;\n}\n\n/*\nRemoves the default spacing and border for appropriate elements.\n*/\n\nblockquote,\ndl,\ndd,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\nhr,\nfigure,\np,\npre {\n  margin: 0;\n}\n\nfieldset {\n  margin: 0;\n  padding: 0;\n}\n\nlegend {\n  padding: 0;\n}\n\nol,\nul,\nmenu {\n  list-style: none;\n  margin: 0;\n  padding: 0;\n}\n\n/*\nReset default styling for dialogs.\n*/\ndialog {\n  padding: 0;\n}\n\n/*\nPrevent resizing textareas horizontally by default.\n*/\n\ntextarea {\n  resize: vertical;\n}\n\n/*\n1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)\n2. Set the default placeholder color to the user's configured gray 400 color.\n*/\n\ninput::placeholder,\ntextarea::placeholder {\n  opacity: 1; /* 1 */\n  color: #9ca3af; /* 2 */\n}\n\n/*\nSet the default cursor for buttons.\n*/\n\nbutton,\n[role=\"button\"] {\n  cursor: pointer;\n}\n\n/*\nMake sure disabled buttons don't get the pointer cursor.\n*/\n:disabled {\n  cursor: default;\n}\n\n/*\n1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)\n2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)\n   This can trigger a poorly considered lint error in some tools but is included by design.\n*/\n\nimg,\nsvg,\nvideo,\ncanvas,\naudio,\niframe,\nembed,\nobject {\n  display: block; /* 1 */\n  vertical-align: middle; /* 2 */\n}\n\n/*\nConstrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)\n*/\n\nimg,\nvideo {\n  max-width: 100%;\n  height: auto;\n}\n\n/* Make elements with the HTML hidden attribute stay hidden by default */\n[hidden]:where(:not([hidden=\"until-found\"])) {\n  display: none;\n}\n\n:root {\n  --inherit: inherit;\n  --current: currentColor;\n  --transparent: transparent;\n  --black: #000000;\n  --white: #ffffff;\n  --slate-50: #f8fafc;\n  --slate-100: #f1f5f9;\n  --slate-200: #e2e8f0;\n  --slate-300: #cbd5e1;\n  --slate-400: #94a3b8;\n  --slate-500: #64748b;\n  --slate-600: #475569;\n  --slate-700: #334155;\n  --slate-800: #1e293b;\n  --slate-900: #0f172a;\n  --slate-950: #020617;\n  --gray-50: #f9fafb;\n  --gray-100: #f3f4f6;\n  --gray-200: #e5e7eb;\n  --gray-300: #d1d5db;\n  --gray-400: #9ca3af;\n  --gray-500: #6b7280;\n  --gray-600: #4b5563;\n  --gray-700: #374151;\n  --gray-800: #1f2937;\n  --gray-900: #111827;\n  --gray-950: #030712;\n  --zinc-50: #fafafa;\n  --zinc-100: #f4f4f5;\n  --zinc-200: #e4e4e7;\n  --zinc-300: #d4d4d8;\n  --zinc-400: #a1a1aa;\n  --zinc-500: #71717a;\n  --zinc-600: #52525b;\n  --zinc-700: #3f3f46;\n  --zinc-800: #27272a;\n  --zinc-900: #18181b;\n  --zinc-950: #09090b;\n  --neutral-50: #fafafa;\n  --neutral-100: #f5f5f5;\n  --neutral-200: #e5e5e5;\n  --neutral-300: #d4d4d4;\n  --neutral-400: #a3a3a3;\n  --neutral-500: #737373;\n  --neutral-600: #525252;\n  --neutral-700: #404040;\n  --neutral-800: #262626;\n  --neutral-900: #171717;\n  --neutral-950: #0a0a0a;\n  --stone-50: #fafaf9;\n  --stone-100: #f5f5f4;\n  --stone-200: #e7e5e4;\n  --stone-300: #d6d3d1;\n  --stone-400: #a8a29e;\n  --stone-500: #78716c;\n  --stone-600: #57534e;\n  --stone-700: #44403c;\n  --stone-800: #292524;\n  --stone-900: #1c1917;\n  --stone-950: #0c0a09;\n  --red-50: #fef2f2;\n  --red-100: #fee2e2;\n  --red-200: #fecaca;\n  --red-300: #fca5a5;\n  --red-400: #f87171;\n  --red-500: #ef4444;\n  --red-600: #dc2626;\n  --red-700: #b91c1c;\n  --red-800: #991b1b;\n  --red-900: #7f1d1d;\n  --red-950: #450a0a;\n  --orange-50: #fff7ed;\n  --orange-100: #ffedd5;\n  --orange-200: #fed7aa;\n  --orange-300: #fdba74;\n  --orange-400: #fb923c;\n  --orange-500: #f97316;\n  --orange-600: #ea580c;\n  --orange-700: #c2410c;\n  --orange-800: #9a3412;\n  --orange-900: #7c2d12;\n  --orange-950: #431407;\n  --amber-50: #fffbeb;\n  --amber-100: #fef3c7;\n  --amber-200: #fde68a;\n  --amber-300: #fcd34d;\n  --amber-400: #fbbf24;\n  --amber-500: #f59e0b;\n  --amber-600: #d97706;\n  --amber-700: #b45309;\n  --amber-800: #92400e;\n  --amber-900: #78350f;\n  --amber-950: #451a03;\n  --yellow-50: #fefce8;\n  --yellow-100: #fef9c3;\n  --yellow-200: #fef08a;\n  --yellow-300: #fde047;\n  --yellow-400: #facc15;\n  --yellow-500: #eab308;\n  --yellow-600: #ca8a04;\n  --yellow-700: #a16207;\n  --yellow-800: #854d0e;\n  --yellow-900: #713f12;\n  --yellow-950: #422006;\n  --lime-50: #f7fee7;\n  --lime-100: #ecfccb;\n  --lime-200: #d9f99d;\n  --lime-300: #bef264;\n  --lime-400: #a3e635;\n  --lime-500: #84cc16;\n  --lime-600: #65a30d;\n  --lime-700: #4d7c0f;\n  --lime-800: #3f6212;\n  --lime-900: #365314;\n  --lime-950: #1a2e05;\n  --green-50: #f0fdf4;\n  --green-100: #dcfce7;\n  --green-200: #bbf7d0;\n  --green-300: #86efac;\n  --green-400: #4ade80;\n  --green-500: #22c55e;\n  --green-600: #16a34a;\n  --green-700: #15803d;\n  --green-800: #166534;\n  --green-900: #14532d;\n  --green-950: #052e16;\n  --emerald-50: #ecfdf5;\n  --emerald-100: #d1fae5;\n  --emerald-200: #a7f3d0;\n  --emerald-300: #6ee7b7;\n  --emerald-400: #34d399;\n  --emerald-500: #10b981;\n  --emerald-600: #059669;\n  --emerald-700: #047857;\n  --emerald-800: #065f46;\n  --emerald-900: #064e3b;\n  --emerald-950: #022c22;\n  --teal-50: #f0fdfa;\n  --teal-100: #ccfbf1;\n  --teal-200: #99f6e4;\n  --teal-300: #5eead4;\n  --teal-400: #2dd4bf;\n  --teal-500: #14b8a6;\n  --teal-600: #0d9488;\n  --teal-700: #0f766e;\n  --teal-800: #115e59;\n  --teal-900: #134e4a;\n  --teal-950: #042f2e;\n  --cyan-50: #ecfeff;\n  --cyan-100: #cffafe;\n  --cyan-200: #a5f3fc;\n  --cyan-300: #67e8f9;\n  --cyan-400: #22d3ee;\n  --cyan-500: #06b6d4;\n  --cyan-600: #0891b2;\n  --cyan-700: #0e7490;\n  --cyan-800: #155e75;\n  --cyan-900: #164e63;\n  --cyan-950: #083344;\n  --sky-50: #f0f9ff;\n  --sky-100: #e0f2fe;\n  --sky-200: #bae6fd;\n  --sky-300: #7dd3fc;\n  --sky-400: #38bdf8;\n  --sky-500: #0ea5e9;\n  --sky-600: #0284c7;\n  --sky-700: #0369a1;\n  --sky-800: #075985;\n  --sky-900: #0c4a6e;\n  --sky-950: #082f49;\n  --blue-50: #eff6ff;\n  --blue-100: #dbeafe;\n  --blue-200: #bfdbfe;\n  --blue-300: #93c5fd;\n  --blue-400: #60a5fa;\n  --blue-500: #3b82f6;\n  --blue-600: #2563eb;\n  --blue-700: #1d4ed8;\n  --blue-800: #1e40af;\n  --blue-900: #1e3a8a;\n  --blue-950: #172554;\n  --indigo-50: #eef2ff;\n  --indigo-100: #e0e7ff;\n  --indigo-200: #c7d2fe;\n  --indigo-300: #a5b4fc;\n  --indigo-400: #818cf8;\n  --indigo-500: #6366f1;\n  --indigo-600: #4f46e5;\n  --indigo-700: #4338ca;\n  --indigo-800: #3730a3;\n  --indigo-900: #312e81;\n  --indigo-950: #1e1b4b;\n  --violet-50: #f5f3ff;\n  --violet-100: #ede9fe;\n  --violet-200: #ddd6fe;\n  --violet-300: #c4b5fd;\n  --violet-400: #a78bfa;\n  --violet-500: #8b5cf6;\n  --violet-600: #7c3aed;\n  --violet-700: #6d28d9;\n  --violet-800: #5b21b6;\n  --violet-900: #4c1d95;\n  --violet-950: #2e1065;\n  --purple-50: #faf5ff;\n  --purple-100: #f3e8ff;\n  --purple-200: #e9d5ff;\n  --purple-300: #d8b4fe;\n  --purple-400: #c084fc;\n  --purple-500: #a855f7;\n  --purple-600: #9333ea;\n  --purple-700: #7e22ce;\n  --purple-800: #6b21a8;\n  --purple-900: #581c87;\n  --purple-950: #3b0764;\n  --fuchsia-50: #fdf4ff;\n  --fuchsia-100: #fae8ff;\n  --fuchsia-200: #f5d0fe;\n  --fuchsia-300: #f0abfc;\n  --fuchsia-400: #e879f9;\n  --fuchsia-500: #d946ef;\n  --fuchsia-600: #c026d3;\n  --fuchsia-700: #a21caf;\n  --fuchsia-800: #86198f;\n  --fuchsia-900: #701a75;\n  --fuchsia-950: #4a044e;\n  --pink-50: #fdf2f8;\n  --pink-100: #fce7f3;\n  --pink-200: #fbcfe8;\n  --pink-300: #f9a8d4;\n  --pink-400: #f472b6;\n  --pink-500: #ec4899;\n  --pink-600: #db2777;\n  --pink-700: #be185d;\n  --pink-800: #9d174d;\n  --pink-900: #831843;\n  --pink-950: #500724;\n  --rose-50: #fff1f2;\n  --rose-100: #ffe4e6;\n  --rose-200: #fecdd3;\n  --rose-300: #fda4af;\n  --rose-400: #fb7185;\n  --rose-500: #f43f5e;\n  --rose-600: #e11d48;\n  --rose-700: #be123c;\n  --rose-800: #9f1239;\n  --rose-900: #881337;\n  --rose-950: #4c0519;\n  --natural-wellness-background: #F4F1E8;\n  --natural-wellness-text-primary: #2D2A26;\n  --natural-wellness-accent-primary: #364035;\n  --natural-wellness-accent-secondary: #8B9A8C;\n  --natural-wellness-accent-energetic: #7C9A85;\n  --modern-luxury-background: #2D2A26;\n  --modern-luxury-text-primary: #F4F1E8;\n  --modern-luxury-accent-primary: #B8956A;\n  --modern-luxury-accent-secondary: #A9A299;\n  --vierla-sage: #8B9A8C;\n  --vierla-gold: #B8956A;\n  --vierla-forest: #364035;\n  --brand-sage: #8B9A8C;\n  --brand-gold: #B8956A;\n  --brand-beige: #F0E6D9;\n  --neutral-background-light: #F4F1E8;\n  --neutral-text-primary-light: #2D2A26;\n  --neutral-text-secondary-light: #8B9A8C;\n  --neutral-borders-light: #B7C4B7;\n  --neutral-background-dark: #2D2A26;\n  --neutral-text-primary-dark: #F4F1E8;\n  --neutral-text-secondary-dark: #8B9A8C;\n  --neutral-borders-dark: #657D6D;\n  --neutral-charcoal-dark: #2D2A26;\n  --neutral-charcoal-light: #333333;\n  --neutral-off-white: #F4F1E8;\n  --theme-primary-DEFAULT: #364035;\n  --theme-primary-light: #364035;\n  --theme-primary-dark: #B8956A;\n  --theme-secondary-DEFAULT: #8B9A8C;\n  --theme-secondary-light: #8B9A8C;\n  --theme-secondary-dark: #A9A299;\n  --sage-50: #F5F7F6;\n  --sage-100: #EAEFEB;\n  --sage-200: #D4DFD6;\n  --sage-300: #BECFC1;\n  --sage-400: #A8BFAC;\n  --sage-500: #8B9A8C;\n  --sage-600: #7A8A7C;\n  --sage-700: #697A6B;\n  --sage-800: #586A5A;\n  --sage-900: #475A49;\n  --sage-DEFAULT: #8B9A8C;\n  --gold-50: #FAF7F2;\n  --gold-100: #F5EFE5;\n  --gold-200: #EBDFCB;\n  --gold-300: #E1CFB1;\n  --gold-400: #D7BF97;\n  --gold-500: #B8956A;\n  --gold-600: #A6845C;\n  --gold-700: #94734E;\n  --gold-800: #826240;\n  --gold-900: #705132;\n  --gold-DEFAULT: #B8956A;\n  --forest-50: #F4F5F4;\n  --forest-100: #E8EAE8;\n  --forest-200: #D1D5D1;\n  --forest-300: #BAC0BA;\n  --forest-400: #A3AAA3;\n  --forest-500: #8C948C;\n  --forest-600: #757D75;\n  --forest-700: #5E665E;\n  --forest-800: #475047;\n  --forest-900: #364035;\n  --forest-950: #2A302A;\n  --forest-DEFAULT: #364035;\n  --beige-50: #FEFCFA;\n  --beige-100: #F0E6D9;\n  --beige-200: #E0D5C7;\n  --beige-300: #D0C5B7;\n  --beige-400: #C0B5A7;\n  --beige-500: #B0A597;\n  --beige-600: #A09587;\n  --beige-700: #908577;\n  --beige-800: #807567;\n  --beige-900: #706557;\n  --beige-DEFAULT: #F0E6D9;\n  --charcoal-50: #F5F6F6;\n  --charcoal-100: #E6E8E9;\n  --charcoal-200: #CDD1D3;\n  --charcoal-300: #B4BABD;\n  --charcoal-400: #9BA3A7;\n  --charcoal-500: #828C91;\n  --charcoal-600: #69757B;\n  --charcoal-700: #505E65;\n  --charcoal-800: #37474F;\n  --charcoal-900: #333333;\n  --charcoal-950: #2C3137;\n  --charcoal-DEFAULT: #2C3137;\n  --success-50: #F4F7F5;\n  --success-100: #E8EFEA;\n  --success-200: #D1DFD4;\n  --success-300: #BACFBE;\n  --success-400: #A3BFA8;\n  --success-500: #7C9A85;\n  --success-600: #6B8A74;\n  --success-700: #5A7963;\n  --success-800: #496852;\n  --success-900: #3A5441;\n  --success-DEFAULT: #7C9A85;\n  --warning-50: #FAF7F2;\n  --warning-100: #F5EFE5;\n  --warning-200: #EBDFCB;\n  --warning-300: #E1CFB1;\n  --warning-400: #D7BF97;\n  --warning-500: #B8956A;\n  --warning-600: #A6845C;\n  --warning-700: #94734E;\n  --warning-800: #826240;\n  --warning-900: #705132;\n  --warning-DEFAULT: #B8956A;\n  --error-50: #FEF7ED;\n  --error-100: #FDEDD3;\n  --error-200: #FBD5A5;\n  --error-300: #F9BC6D;\n  --error-400: #F59E0B;\n  --error-500: #D97706;\n  --error-600: #C2410C;\n  --error-700: #9A3412;\n  --error-800: #7C2D12;\n  --error-900: #431407;\n  --error-DEFAULT: #D97706;\n  --mantle-50: #F5FAF7;\n  --mantle-100: #F0E6D9;\n  --mantle-200: #E0D5C7;\n  --mantle-300: #D0C5B7;\n  --mantle-400: #7C9A85;\n  --mantle-500: #6B8A74;\n  --mantle-600: #5A7963;\n  --mantle-700: #496852;\n  --mantle-800: #3A5441;\n  --mantle-900: #333333;\n  --mantle-950: #2C3137;\n  --border: hsl(var(--border));\n  --input: hsl(var(--input));\n  --ring: hsl(var(--ring));\n  --background: hsl(var(--background));\n  --foreground: hsl(var(--foreground));\n  --primary-DEFAULT: hsl(var(--primary));\n  --primary-foreground: hsl(var(--primary-foreground));\n  --secondary-DEFAULT: hsl(var(--secondary));\n  --secondary-foreground: hsl(var(--secondary-foreground));\n  --destructive-DEFAULT: hsl(var(--destructive));\n  --destructive-foreground: hsl(var(--destructive-foreground));\n  --muted-DEFAULT: hsl(var(--muted));\n  --muted-foreground: hsl(var(--muted-foreground));\n  --accent-DEFAULT: hsl(var(--accent));\n  --accent-foreground: hsl(var(--accent-foreground));\n  --popover-DEFAULT: hsl(var(--popover));\n  --popover-foreground: hsl(var(--popover-foreground));\n  --card-DEFAULT: hsl(var(--card));\n  --card-foreground: hsl(var(--card-foreground));\n  --aurora-bg: #F4F1E8;\n  --aurora-stripes: repeating-linear-gradient(100deg, #F4F1E8 0%, #F4F1E8 7%, transparent 10%, transparent 12%, #F4F1E8 16%);\n  --aurora-flow: repeating-linear-gradient(100deg, #F4F1E8 10%, #364035 15%, #8B9A8C 20%, #F0E6D9 25%, #7C9A85 30%);\n  --aurora-feature-bg: #F4F1E8;\n  --aurora-feature-stripes: repeating-linear-gradient(100deg, #F4F1E8 0%, #F4F1E8 7%, transparent 10%, transparent 12%, #F4F1E8 16%);\n  --aurora-feature-flow: repeating-linear-gradient(100deg, rgba(244, 241, 232, 0.25) 10%, rgba(54, 64, 53, 0.20) 15%, rgba(139, 154, 140, 0.18) 20%, rgba(240, 230, 217, 0.22) 25%, rgba(124, 154, 133, 0.15) 30%);\n}\n\n.dark {\n  --aurora-bg: #2D2A26;\n  --aurora-stripes: repeating-linear-gradient(100deg, #2D2A26 0%, #2D2A26 7%, transparent 10%, transparent 12%, #2D2A26 16%);\n  --aurora-flow: repeating-linear-gradient(100deg, rgba(45, 42, 38, 0.45) 10%, rgba(184, 149, 106, 0.35) 15%, rgba(75, 61, 107, 0.30) 20%, rgba(169, 162, 153, 0.25) 25%, rgba(45, 42, 38, 0.40) 30%);\n  --aurora-feature-bg: #2D2A26;\n  --aurora-feature-stripes: repeating-linear-gradient(100deg, #2D2A26 0%, #2D2A26 7%, transparent 10%, transparent 12%, #2D2A26 16%);\n  --aurora-feature-flow: repeating-linear-gradient(100deg, rgba(45, 42, 38, 0.35) 10%, rgba(184, 149, 106, 0.25) 15%, rgba(75, 61, 107, 0.20) 20%, rgba(169, 162, 153, 0.18) 25%, rgba(45, 42, 38, 0.30) 30%);\n}\n  :root {\n    /*\n    ========================================\n    | 🎨 MASTER CONTROL PANEL - New Visual Identity\n    | Single source of truth for theme variables that map to Tailwind colors\n    ========================================\n    */\n\n    /* iOS Safe Area Insets */\n    --safe-area-inset-top: env(safe-area-inset-top);\n    --safe-area-inset-right: env(safe-area-inset-right);\n    --safe-area-inset-bottom: env(safe-area-inset-bottom);\n    --safe-area-inset-left: env(safe-area-inset-left);\n\n    /* Master Brand Colors - STRICT DUAL-THEME SEPARATION */\n    --master-brand-primary-light: #364035;  /* Light Mode: Vierla Forest - Primary brand color */\n    --master-brand-primary-dark: #B8956A;   /* Dark Mode: Vierla Gold - Primary brand color */\n    --master-brand-secondary: #2D2A26;      /* Deep Charcoal - Main background */\n    --master-brand-accent-light: #8B9A8C;   /* Light Mode: Vierla Sage - Accents */\n    --master-brand-accent-dark: #B8956A;    /* Dark Mode: Vierla Gold - Accents */\n    --master-brand-forest: #364035;         /* Light Mode Only: Vierla Forest */\n    --master-brand-beige: #F0E6D9;          /* Warm Beige - Neutral accent */\n\n    /* Master Neutral Colors - Light Mode */\n    --master-bg-light: #F4F1E8;             /* Warm Cream - Primary light background */   /* Deep Charcoal - Primary text on light */ /* Vierla-Sage - Secondary text on light */\n    --master-borders-light: #B7C4B7;        /* Muted Sage - Borders and dividers */\n\n    /* Master Neutral Colors - Dark Mode (Modern Luxury Palette) */\n    --master-bg-dark: #2D2A26;              /* Deep Charcoal - Primary dark background */    /* Warm Cream - Primary text on dark */  /* Muted Gold/Grey - Secondary text on dark */\n    --master-borders-dark: #B8956A;         /* Vierla Gold - Borders in dark mode */\n    --master-accent-primary-dark: #B8956A;  /* Vierla Gold - Primary accent for dark mode */\n    --master-accent-secondary-dark: #A9A299; /* Muted Gold/Grey - Secondary accent for dark mode */\n\n    /* Master Action Colors - STRICT THEME SEPARATION */\n    --master-action-primary-light: #364035; /* Light Mode: Vierla Forest - Primary buttons */\n    --master-action-primary-dark: #B8956A;  /* Dark Mode: Vierla Gold - Primary buttons */\n    --master-action-hover-light: #2A302A;   /* Light Mode: Darker Forest - Hover states */\n    --master-action-hover-dark: #A6845C;    /* Dark Mode: Darker Gold - Hover states */\n    --master-action-focus-light: #364035;   /* Light Mode: Vierla Forest - Focus states */\n    --master-action-focus-dark: #B8956A;    /* Dark Mode: Vierla Gold - Focus states */\n    --master-action-secondary-light: #8B9A8C; /* Light Mode: Vierla Sage - Secondary buttons */\n    --master-action-secondary-dark: #A9A299; /* Dark Mode: Muted Gold/Champagne - Secondary buttons */\n    --master-action-tertiary-light: #8B9A8C; /* Light Mode: Vierla Sage - Tertiary buttons */\n    --master-action-tertiary-dark: #A9A299; /* Dark Mode: Muted Gold/Champagne - Tertiary buttons */\n\n    /* Master Semantic Colors - THEME-AWARE */\n    --master-success-light: #8B9A8C;        /* Light Mode: Vierla Sage - Success states */\n    --master-success-dark: #B8956A;         /* Dark Mode: Vierla Gold - Success states */\n    --master-warning-light: #364035;        /* Light Mode: Vierla Forest - Warning states */\n    --master-warning-dark: #B8956A;         /* Dark Mode: Vierla Gold - Warning states */\n    --master-error: #D97706;                /* Terracotta - Error states (universal) */\n\n    /* Master Text Colors - STRICT THEME SEPARATION */\n    --master-text-primary-light: #2D2A26;   /* Light Mode: Deep Charcoal - Main text */\n    --master-text-secondary-light: #8B9A8C; /* Light Mode: Vierla Sage - Subtitles */\n    --master-text-primary-dark: #F4F1E8;    /* Dark Mode: Warm Cream - Main text */\n    --master-text-secondary-dark: #A9A299;  /* Dark Mode: Muted Gold/Champagne - Subtitles */\n    --master-text-muted: #F0E6D9;           /* Warm Beige - Muted text (universal) */\n\n    /* Legacy Text Colors (for backward compatibility) */\n    --master-text-primary: #2D2A26;         /* Deep Charcoal - Main text on light */\n    --master-text-secondary: #8B9A8C;       /* Vierla-Sage - Subtitles */\n    --master-text-on-dark: #F4F1E8;         /* Warm Cream - Text on dark backgrounds */\n\n    /* Master Form & Input Colors - THEME-AWARE */\n    --master-input-bg-light: #F4F1E8;       /* Light Mode: Warm Cream - Input backgrounds */\n    --master-input-bg-dark: #333333;        /* Dark Mode: Light Charcoal - Input backgrounds */\n    --master-input-border-light: #8B9A8C;   /* Light Mode: Vierla Sage - Input borders */\n    --master-input-border-dark: #A9A299;    /* Dark Mode: Muted Gold/Champagne - Input borders */\n    --master-input-border-focus-light: #364035; /* Light Mode: Vierla Forest - Input borders on focus */\n    --master-input-border-focus-dark: #B8956A;  /* Dark Mode: Vierla Gold - Input borders on focus */\n    --master-input-text: inherit;           /* Inherit text color from parent */\n    --master-label-color-light: #8B9A8C;    /* Light Mode: Vierla Sage - Form labels */\n    --master-label-color-dark: #A9A299;     /* Dark Mode: Muted Gold/Champagne - Form labels */\n\n    /* Master Icon Colors - STRICT THEME SEPARATION */\n    --master-icon-primary-light: #364035;   /* Light Mode: Vierla Forest - Primary icons */\n    --master-icon-primary-dark: #B8956A;    /* Dark Mode: Vierla Gold - Primary icons */\n    --master-icon-secondary-light: #8B9A8C; /* Light Mode: Vierla Sage - Secondary icons */\n    --master-icon-secondary-dark: #A9A299;  /* Dark Mode: Muted Gold/Champagne - Secondary icons */\n    --master-icon-muted-light: #8B9A8C;     /* Light Mode: Vierla Sage - Muted icons */\n    --master-icon-muted-dark: #A9A299;      /* Dark Mode: Muted Gold/Champagne - Muted icons */\n    --master-icon-success-light: #8B9A8C;   /* Light Mode: Vierla Sage - Success icons */\n    --master-icon-success-dark: #B8956A;    /* Dark Mode: Vierla Gold - Success icons */\n\n    /* Master Card & Container Colors - STRICT THEME SEPARATION */\n    --master-card-bg-light: #F4F1E8;        /* Light Mode: Warm Cream - Card backgrounds */\n    --master-card-bg-dark: #333333;         /* Dark Mode: Light Charcoal - Card backgrounds */\n    --master-card-border-light: #8B9A8C;    /* Light Mode: Vierla Sage - Card borders */\n    --master-card-border-dark: #A9A299;     /* Dark Mode: Muted Gold/Champagne - Card borders */\n    --master-card-border-hover-light: #364035; /* Light Mode: Vierla Forest - Card borders on hover */\n    --master-card-border-hover-dark: #B8956A; /* Dark Mode: Vierla Gold - Card borders on hover */\n    --master-card-glow-light: #8B9A8C;      /* Light Mode: Vierla Sage - Card glow effects */\n    --master-card-glow-dark: #B8956A;       /* Dark Mode: Vierla Gold - Card glow effects */\n    --master-card-accent-light: #364035;    /* Light Mode: Vierla Forest - Card accent elements */\n    --master-card-accent-dark: #B8956A;     /* Dark Mode: Vierla Gold - Card accent elements */\n\n    /* Typography Scale (New Visual Identity) - Original Desktop Sizes */\n    --font-size-h1-original: 3.052rem;    /* 48.83px - Notable (Oswald) Bold */\n    --font-size-h2-original: 2.441rem;    /* 39.06px - Tai Heritage Pro (Playfair) Bold */\n    --font-size-h3-original: 1.953rem;    /* 31.25px - Tai Heritage Pro (Playfair) Regular */\n    --font-size-h4-original: 1.563rem;    /* 25px - Jost (Inter) Bold */\n    --font-size-h5-original: 1.25rem;     /* 20px - Jost (Inter) Bold */\n    --font-size-body-original: 1rem;      /* 16px - Jost (Inter) Regular */\n    --font-size-small-original: 0.8rem;   /* 12.8px - Jost (Inter) Medium */\n\n    /* Typography Scale - 33.33% Reduced for Non-Mobile Devices */\n    --font-size-h1: 2.035rem;    /* 32.56px - Reduced by 33.33% */\n    --font-size-h2: 1.627rem;    /* 26.04px - Reduced by 33.33% */\n    --font-size-h3: 1.302rem;    /* 20.83px - Reduced by 33.33% */\n    --font-size-h4: 1.042rem;    /* 16.67px - Reduced by 33.33% */\n    --font-size-h5: 0.833rem;    /* 13.33px - Reduced by 33.33% */\n    --font-size-body: 0.667rem;  /* 10.67px - Reduced by 33.33% */\n    --font-size-small: 0.533rem; /* 8.53px - Reduced by 33.33% */\n\n    /* Mobile Typography Scale (Responsive) - Keep existing mobile sizes */\n    --font-size-h1-mobile: 2.25rem;  /* 36px - Scaled down for mobile */\n    --font-size-h2-mobile: 1.875rem; /* 30px - Scaled down for mobile */\n    --font-size-h3-mobile: 1.5rem;   /* 24px - Scaled down for mobile */\n    --font-size-h4-mobile: 1.25rem;  /* 20px - Scaled down for mobile */\n    --font-size-h5-mobile: 1.125rem; /* 18px - Scaled down for mobile */\n    --font-size-body-mobile: 0.875rem; /* 14px - Slightly smaller for mobile */\n    --font-size-small-mobile: 0.75rem; /* 12px - Smaller for mobile */\n\n    /* Font Family Controls - Updated to New Typography Specifications */\n    --font-family-h1: var(--font-jost);           /* Jost for H1 headlines - Clean, geometric */\n    --font-family-h2: var(--font-jost);           /* Jost for H2 headlines - Consistent hierarchy */\n    --font-family-h3: var(--font-tai-heritage);   /* Tai Heritage Pro (Playfair) for H3 - Elegant serif */\n    --font-family-body: var(--font-inter);        /* Inter for UI/body text - Optimized readability */\n    --font-family-ui: var(--font-inter);          /* Inter for UI elements - Consistent interface */\n    --font-family-accent: var(--font-farsan);     /* Farsan (Dancing Script) for accent text */\n\n    /* 8-Point Grid System (Vierla Design System) - Original Sizes */\n    --space-1-original: 0.5rem;   /* 8px */\n    --space-2-original: 1rem;     /* 16px */\n    --space-3-original: 1.5rem;   /* 24px */\n    --space-4-original: 2rem;     /* 32px */\n    --space-5-original: 2.5rem;   /* 40px */\n    --space-6-original: 3rem;     /* 48px */\n    --space-7-original: 3.5rem;   /* 56px */\n    --space-8-original: 4rem;     /* 64px */\n    --space-9-original: 4.5rem;   /* 72px */\n    --space-10-original: 5rem;    /* 80px */\n    --space-12-original: 6rem;    /* 96px */\n    --space-16-original: 8rem;    /* 128px */\n    --space-20-original: 10rem;   /* 160px */\n    --space-24-original: 12rem;   /* 192px */\n\n    /* 8-Point Grid System - 33.33% Reduced for Non-Mobile Devices */\n    --space-1: 0.333rem;   /* 5.33px - Reduced by 33.33% */\n    --space-2: 0.667rem;   /* 10.67px - Reduced by 33.33% */\n    --space-3: 1rem;       /* 16px - Reduced by 33.33% */\n    --space-4: 1.333rem;   /* 21.33px - Reduced by 33.33% */\n    --space-5: 1.667rem;   /* 26.67px - Reduced by 33.33% */\n    --space-6: 2rem;       /* 32px - Reduced by 33.33% */\n    --space-7: 2.333rem;   /* 37.33px - Reduced by 33.33% */\n    --space-8: 2.667rem;   /* 42.67px - Reduced by 33.33% */\n    --space-9: 3rem;       /* 48px - Reduced by 33.33% */\n    --space-10: 3.333rem;  /* 53.33px - Reduced by 33.33% */\n    --space-12: 4rem;      /* 64px - Reduced by 33.33% */\n    --space-16: 5.333rem;  /* 85.33px - Reduced by 33.33% */\n    --space-20: 6.667rem;  /* 106.67px - Reduced by 33.33% */\n    --space-24: 8rem;      /* 128px - Reduced by 33.33% */\n\n    /* Master Button Colors - Natural Wellness Palette for Light Mode */\n    --master-button-primary-bg: #364035;           /* Vierla Forest - Primary button background for light mode */\n    --master-button-primary-text: #F4F1E8;         /* Warm Cream - Text on primary buttons */\n    --master-button-primary-hover: #2A302A;        /* Darker Forest - Primary button hover */\n    --master-button-secondary-bg: #8B9A8C;         /* Vierla-Sage - Secondary button background */\n    --master-button-secondary-text: #F4F1E8;       /* Warm Cream - Secondary button text */\n    --master-button-secondary-hover: #7A8A7C;      /* Sage 600 - Secondary button hover */\n    --master-button-tertiary-bg: transparent;      /* Transparent tertiary buttons */\n    --master-button-tertiary-text: #8B9A8C;        /* Vierla-Sage - Tertiary button text */\n    --master-button-tertiary-hover: underline;     /* Underline on hover for tertiary */\n\n    /* Master Card & Container Colors */\n    --master-card-background: #333333; /* #333333 - Card backgrounds */\n    --master-card-border: rgb(139 154 140 / 0.3);         /* Sage border with opacity */\n    --master-card-glow: #B8956A;                   /* #B8956A - Card glow effects */\n\n    /* Master Header/Navbar Colors - Proper Glassmorphism (Light Mode) */\n    --master-header-background: #f6f7f6;                           /* Light mode: opaque very light sage */\n    --master-header-backdrop-blur: 50%;                            /* 50% backdrop blur for proper glassmorphism */\n    --master-header-border: rgba(139, 154, 140, 0.2);              /* Light mode: medium sage border */\n    --master-header-logo-bg: linear-gradient(135deg, #364035, #8B9A8C); /* Light mode: Forest to Sage gradient */\n    --master-header-logo-border: rgba(54, 64, 53, 0.3);            /* Light mode: Vierla Forest border */\n    --master-header-logo-icon: #F4F1E8;                            /* Light mode: Warm Cream icon for contrast */\n    --master-header-brand-text: #181b19;                           /* Light mode: very dark charcoal text */\n    --master-header-nav-text: #F4F1E8;    /* #F5FAF7 - Nav text */\n    --master-header-nav-text-muted: #B8956A;     /* #B8956A - Muted nav text */\n    --master-header-nav-active-bg: rgba(124, 154, 133, 0.2);        /* Glassmorphic sage active background */\n    --master-header-nav-active-glow: transparent;                   /* No active glow */\n    --master-header-nav-hover-bg: rgba(184, 149, 106, 0.15);        /* Glassmorphic gold hover background */\n\n    /*\n    ========================================\n    | 📄 PAGE-SPECIFIC COMPONENT CONTROLS\n    ========================================\n    */\n\n    /* ----- 🏠 HOME PAGE - THEME-AWARE CONTROLS ----- */\n    --home-hero-title-light: #2D2A26;              /* Light Mode: Deep Charcoal for main headline */\n    --home-hero-title-dark: #F4F1E8;               /* Dark Mode: Warm Cream for main headline */\n    --home-hero-subtitle-light: #8B9A8C;           /* Light Mode: Vierla Sage for subtitle */\n    --home-hero-subtitle-dark: #A9A299;            /* Dark Mode: Muted Gold/Champagne for subtitle */\n    --home-hero-cta-bg-light: #364035;             /* Light Mode: Vierla Forest for CTA */\n    --home-hero-cta-bg-dark: #B8956A;              /* Dark Mode: Vierla Gold for CTA */\n    --home-hero-cta-text-light: #F4F1E8;           /* Light Mode: Warm Cream text on CTA */\n    --home-hero-cta-text-dark: #2D2A26;            /* Dark Mode: Deep Charcoal text on CTA */\n    --home-card-background: #333333;               /* Light Charcoal for cards */\n    --home-card-border-light: rgba(139, 154, 140, 0.3); /* Light Mode: Vierla Sage border */\n    --home-card-border-dark: rgba(169, 162, 153, 0.3);  /* Dark Mode: Muted Gold/Champagne border */\n    --home-card-glow-light: #8B9A8C;               /* Light Mode: Vierla Sage glow effect */\n    --home-card-glow-dark: #B8956A;                /* Dark Mode: Vierla Gold glow effect */\n\n    /* Home Page Shiny Button Controls - THEME-AWARE */\n    --home-shiny-button-bg-light: #364035;         /* Light Mode: Vierla Forest background */\n    --home-shiny-button-bg-dark: #B8956A;          /* Dark Mode: Vierla Gold background */\n    --home-shiny-button-text-light: #F4F1E8;       /* Light Mode: Warm Cream text */\n    --home-shiny-button-text-dark: #2D2A26;        /* Dark Mode: Deep Charcoal text */\n    --home-shiny-button-shimmer-light: #8B9A8C;    /* Light Mode: Vierla Sage shimmer */\n    --home-shiny-button-shimmer-dark: #F4F1E8;     /* Dark Mode: Warm Cream shimmer */\n\n    /* Home Page Text Effects */\n    --home-text-shimmer-base: var(--master-text-on-dark);\n    --home-text-shimmer-highlight: var(--master-brand-accent);\n    --home-word-pullup-color: var(--mantle-100);\n    --home-marquee-text: var(--mantle-100);\n    --home-marquee-bg: transparent;\n\n    /* Home Page Golden Cards */\n    --home-golden-card-bg: var(--master-card-background);\n    --home-golden-card-border: var(--master-card-border);\n    --home-golden-card-glow: var(--master-card-glow);\n\n    /* ----- 🎯 FEATURES PAGE ----- */\n    --features-hero-title: #F5FAF7;                /* Light Off-White for title */\n    --features-hero-subtitle: #F0E6D9;             /* Warm Beige for subtitle */\n    --features-bento-card-bg: #333333;             /* Light Charcoal for cards */\n    --features-bento-card-border: rgba(124, 154, 133, 0.3); /* Subtle sage border */\n    --features-bento-card-title: #F5FAF7;          /* Light Off-White for card titles */\n    --features-bento-card-text: #F0E6D9;           /* Warm Beige for card text */\n    --features-bento-icon-color: #7C9A85;          /* Sage for icons */\n    --features-bento-grid-gap: 1.5rem;\n    --features-cta-button-bg: #B8956A;             /* Gold for CTA buttons */\n    --features-cta-button-text: #2C3137;           /* Dark text on CTA */\n    --features-cta-button-hover: #A6845C;          /* Darker gold on hover */\n\n    /* Features Page Component Controls */\n    --features-shiny-button-bg: var(--master-button-primary-bg);\n    --features-shiny-button-text: var(--master-button-primary-text);\n    --features-shiny-button-shimmer: var(--master-brand-accent);\n    --features-golden-card-bg: var(--master-card-background);\n    --features-golden-card-border: var(--master-card-border);\n    --features-golden-card-glow: var(--master-card-glow);\n\n    /* ----- 💰 PRICING PAGE ----- */\n    --pricing-hero-title: #F5FAF7;                 /* Light Off-White for title */\n    --pricing-hero-subtitle: #F0E6D9;              /* Warm Beige for subtitle */\n    --pricing-card-background: #333333;            /* Light Charcoal for cards */\n    --pricing-card-border: rgba(124, 154, 133, 0.3); /* Subtle sage border */\n    --pricing-card-glow: #B8956A;                  /* Gold glow effect */\n    --pricing-card-title: #F5FAF7;                 /* Light Off-White for card titles */\n    --pricing-card-price: #B8956A;                 /* Gold for pricing */\n    --pricing-card-text: #F0E6D9;                  /* Warm Beige for card text */\n    --pricing-popular-bg: rgba(184, 149, 106, 0.1); /* Subtle gold background */\n    --pricing-popular-border: #B8956A;             /* Gold border for popular plan */\n    --pricing-shiny-button-bg: #B8956A;            /* Gold button background */\n    --pricing-shiny-button-text: #2C3137;          /* Dark text on button */\n    --pricing-shiny-button-shimmer: #F5FAF7;       /* Light shimmer */\n    --pricing-badge-bg: #7C9A85;                   /* Sage badge background */\n    --pricing-badge-text: #F5FAF7;                 /* Light text on badge */\n\n    /* ----- 👥 ABOUT PAGE ----- */\n    --about-hero-title: #F5FAF7;                   /* Light Off-White for title */\n    --about-hero-subtitle: #F0E6D9;                /* Warm Beige for subtitle */\n    --about-card-background: #333333;              /* Light Charcoal for cards */\n    --about-card-border: rgba(124, 154, 133, 0.3); /* Subtle sage border */\n    --about-card-glow: #B8956A;                    /* Gold glow effect */\n    --about-team-card-bg: #333333;                 /* Light Charcoal for team cards */\n    --about-team-card-border: rgba(124, 154, 133, 0.2); /* Very subtle sage border */\n    --about-team-name: #F5FAF7;                    /* Light Off-White for names */\n    --about-team-role: #7C9A85;                    /* Sage for roles */\n    --about-team-bio: #F0E6D9;                     /* Warm Beige for bios */\n    --about-shiny-button-bg: #B8956A;              /* Gold button background */\n    --about-shiny-button-text: #2C3137;            /* Dark text on button */\n    --about-shiny-button-shimmer: #F5FAF7;         /* Light shimmer */\n    --about-mission-bg: rgba(51, 51, 51, 0.5);     /* Semi-transparent charcoal */\n\n    /* ----- 📞 CONTACT PAGE ----- */\n    --contact-hero-title: #F5FAF7;                 /* Light Off-White for title */\n    --contact-hero-subtitle: #F0E6D9;              /* Warm Beige for subtitle */\n    --contact-form-bg: #333333;                    /* Light Charcoal for form */\n    --contact-form-border: rgba(124, 154, 133, 0.3); /* Subtle sage border */\n    --contact-input-bg: rgba(240, 230, 217, 0.1);  /* Very subtle warm background */\n    --contact-input-border: #7C9A85;               /* Sage border for inputs */\n    --contact-input-text: #F5FAF7;                 /* Light Off-White input text */\n    --contact-label-text: #F0E6D9;                 /* Warm Beige for labels */\n    --contact-input-focus: #B8956A;                /* Gold focus color */\n    --contact-shiny-button-bg: #B8956A;            /* Gold button background */\n    --contact-shiny-button-text: #2C3137;          /* Dark text on button */\n    --contact-shiny-button-shimmer: #F5FAF7;       /* Light shimmer */\n\n    /* ----- 🧩 GLOBAL COMPONENTS - Light Mode ----- */\n    --global-footer-bg: rgba(225, 230, 225, 0.9);  /* Light mode: light sage footer */\n    --global-footer-border: rgba(139, 154, 140, 0.3); /* Light mode: medium sage border */\n    --global-footer-text: #2e332f;                 /* Light mode: dark charcoal text */\n    --global-footer-link: #181b19;                 /* Light mode: very dark charcoal links */\n    --global-footer-link-hover: #5f6d61;           /* Light mode: dark sage hover */\n    --global-cookie-bg: #f6f7f6;                   /* Light mode: very light sage popup */\n    --global-cookie-border: #8b9a8c;               /* Light mode: medium sage border */\n    --global-cookie-text: #181b19;                 /* Light mode: very dark charcoal text */\n    --global-cookie-button-bg: #5f6d61;            /* Light mode: dark sage button */\n\n    /* ======================================== */\n    /* 🔮 UI CHROME MASTER CONTROLS - v1.5.6    */\n    /* ======================================== */\n\n    /* -- Header & Footer (Light Mode) - Natural Wellness Theme -- */\n    --header-footer-bg: rgba(244, 241, 232, 0.80); /* 80% opacity Warm Cream for semi-transparent effect */\n    --header-footer-backdrop-blur: blur(16px);\n    --header-footer-border: rgba(139, 154, 140, 0.3); /* Vierla Sage border */\n\n    /* -- Theme-Aware Icons (Light Mode) -- */\n    --icon-primary: #2e332f; /* Dark charcoal */\n    --icon-secondary: #5f6d61; /* Dark sage */\n    --icon-accent: #79887a; /* Medium-dark sage */\n    --icon-on-accent: #f6f7f6; /* Very light sage for use on colored buttons */\n\n    /* ----- 🎨 THEME-AWARE ICON COLORS ----- */\n    --icon-muted: #8b9a8c;                         /* Light mode: medium sage for muted icons */\n    --icon-on-dark: #f6f7f6;                       /* Light mode: very light sage for icons on dark backgrounds */\n    --global-cookie-button-text: #2C3137;          /* Dark text on cookie button */\n\n    /* ----- 🎴 BENTO GRID CARD CONTROLS - LIGHT MODE (NO GOLD) ----- */\n    --card-bg: rgba(246, 247, 246, 0.95);          /* Light mode: very light sage card background */\n    --shadow-card: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n    --accent-hover-overlay: rgba(139, 154, 140, 0.05); /* Light Mode: Vierla Sage overlay on hover (NO GOLD) */\n    --accent-bg: rgba(139, 154, 140, 0.15);        /* Light Mode: Vierla Sage accent background (NO GOLD) */\n    --border-accent: rgba(139, 154, 140, 0.3);     /* Light Mode: Vierla Sage border color (NO GOLD) */\n\n    /* ----- 📝 FORM INPUT CONTROLS ----- */\n    --input-bg: rgba(246, 247, 246, 0.8);          /* Light mode: input background */\n    --input-border: rgba(139, 154, 140, 0.3);      /* Light mode: input border */\n    --input-placeholder: rgba(46, 51, 47, 0.6);    /* Light mode: placeholder text */\n\n    /* ----- 📝 WCAG-COMPLIANT TEXT COLORS ----- */\n    /* Primary Body Text */\n    --text-primary: #2C3137;                       /* Light mode: Dark Charcoal for main text */\n    --text-secondary: #7C9A85;                     /* Light mode: Sage for secondary/muted text */\n    --text-headlines: #2C3137;                     /* Light mode: Dark Charcoal for headlines */\n    --text-links: #7C9A85;                         /* Light mode: Sage for links */\n    --text-links-hover: #6B8A73;                   /* Light mode: Darker sage for link hover */\n\n    /* ----- 🎨 PROVIDERS PAGE ----- */\n    --providers-hero-title: #F5FAF7;               /* Light Off-White for title */\n    --providers-hero-subtitle: #F0E6D9;            /* Warm Beige for subtitle */\n    --providers-card-bg: #333333;                  /* Light Charcoal for cards */\n    --providers-card-border: rgba(124, 154, 133, 0.3); /* Subtle sage border */\n    --providers-card-title: #F5FAF7;               /* Light Off-White for card titles */\n    --providers-card-text: #F0E6D9;                /* Warm Beige for card text */\n    --providers-icon-color: #7C9A85;               /* Sage for icons */\n    --providers-cta-bg: #B8956A;                   /* Gold for CTA buttons */\n    --providers-cta-text: #2C3137;                 /* Dark text on CTA */\n    --providers-coming-soon-bg: #333333;           /* Light Charcoal for coming soon section */\n\n    /* ----- 📝 APPLY PAGE ----- */\n    --apply-hero-title: #F5FAF7;                   /* Light Off-White for title */\n    --apply-hero-subtitle: #F0E6D9;                /* Warm Beige for subtitle */\n    --apply-form-bg: #333333;                      /* Light Charcoal for form */\n    --apply-form-border: rgba(124, 154, 133, 0.3); /* Subtle sage border */\n    --apply-progress-active: #7C9A85;              /* Sage for active progress */\n    --apply-progress-inactive: #333333;            /* Light Charcoal for inactive */\n    --apply-progress-bar: #7C9A85;                 /* Sage for progress bar */\n    --apply-step-text: #F5FAF7;                    /* Light Off-White for step text */\n\n    /* Shiny Button Global Controls */\n    --global-shiny-button-primary-bg: #B8956A;     /* Gold primary button */\n    --global-shiny-button-primary-text: #2C3137;   /* Dark text on primary */\n    --global-shiny-button-primary-shimmer: #F5FAF7; /* Light shimmer */\n    --global-shiny-button-secondary-bg: transparent; /* Transparent secondary */\n    --global-shiny-button-secondary-text: #7C9A85; /* Sage secondary text */\n    --global-shiny-button-secondary-shimmer: #7C9A85; /* Sage shimmer */\n\n    /* Shimmer Button Global Controls */\n    --global-shimmer-button-primary-bg: #B8956A;   /* Gold primary shimmer button */\n    --global-shimmer-button-primary-shimmer: #F5FAF7; /* Light shimmer */\n    --global-shimmer-button-secondary-bg: transparent; /* Transparent secondary */\n    --global-shimmer-button-secondary-shimmer: #7C9A85; /* Sage shimmer */\n\n    /* Text Shimmer Global Controls */\n    --global-text-shimmer-base: #F5FAF7;           /* Light Off-White base */\n    --global-text-shimmer-highlight: #B8956A;      /* Gold highlight */\n\n    /* Golden Glowing Card Global Controls */\n    --global-golden-card-bg: #333333;              /* Light Charcoal background */\n    --global-golden-card-border: rgba(124, 154, 133, 0.3); /* Subtle sage border */\n    --global-golden-card-glow: #B8956A;            /* Gold glow effect */\n\n    /*\n    ========================================\n    | 🎨 SHADCN UI THEME VARIABLES - Dark Mode (Default)\n    | \"Modern Luxury\" Theme - Gold & Charcoal Palette\n    ========================================\n    */\n    --background: 44 7% 17%;       /* Deep Charcoal (#2D2A26) - Main background */\n    --foreground: 44 15% 95%;      /* Warm Cream (#F4F1E8) - Main text */\n    --card: 0 0% 20%;              /* Light Charcoal (#333333) - Card backgrounds */\n    --card-foreground: 44 15% 95%; /* Warm Cream - Card text */\n    --popover: 0 0% 20%;           /* Light Charcoal - Popover backgrounds */\n    --popover-foreground: 44 15% 95%; /* Warm Cream - Popover text */\n    --primary: 40 35% 55%;         /* Vierla Gold (#B8956A) - Primary CTAs, active nav */\n    --primary-foreground: 44 7% 17%; /* Deep Charcoal - Text on primary */\n    --secondary: 40 20% 65%;       /* Muted Gold/Champagne (#A9A299) - Secondary buttons */\n    --secondary-foreground: 44 7% 17%; /* Deep Charcoal - Text on secondary */\n    --muted: 44 7% 17%;            /* Deep Charcoal - Muted backgrounds */\n    --muted-foreground: 40 20% 65%; /* Muted Gold/Champagne - Muted text */\n    --accent: 40 35% 55%;          /* Vierla Gold - Accent elements */\n    --accent-foreground: 44 7% 17%; /* Deep Charcoal - Text on accent */\n    --destructive: 25 95% 53%;     /* Terracotta (#D97706) - Error/destructive */\n    --destructive-foreground: 0 0% 98%; /* White - Text on destructive */\n    --border: 40 20% 65% / 0.3;    /* Muted Gold/Champagne with opacity - Borders */\n    --input: 40 20% 65% / 0.3;     /* Muted Gold/Champagne with opacity - Input borders */\n    --ring: 40 35% 55%;            /* Vierla Gold - Focus rings */\n    --radius: 0.5rem;              /* Border radius */\n  }\n\n  .dark {\n    /*\n    ========================================\n    | 🎨 SHADCN UI THEME VARIABLES - Dark Mode\n    | Dark theme maintains our current design\n    ========================================\n    */\n    --background: 44 12% 20%;      /* Dark Charcoal - Main background */\n    --foreground: 120 20% 97%;     /* Light Off-White - Main text */\n    --card: 0 0% 20%;              /* Light Charcoal - Card backgrounds */\n    --card-foreground: 120 20% 97%; /* Light Off-White - Card text */\n    --popover: 0 0% 20%;           /* Light Charcoal - Popover backgrounds */\n    --popover-foreground: 120 20% 97%; /* Light Off-White - Popover text */\n    --primary: 40 35% 55%;         /* Vierla Gold (#B8956A) - Primary elements (NO GREEN) */\n    --primary-foreground: 44 7% 17%; /* Deep Charcoal - Text on primary */\n    --secondary: 40 20% 65%;       /* Muted Gold/Champagne (#A9A299) - Secondary elements */\n    --secondary-foreground: 44 7% 17%; /* Deep Charcoal - Text on secondary */\n    --muted: 44 7% 17%;            /* Deep Charcoal - Muted backgrounds */\n    --muted-foreground: 40 20% 65%; /* Muted Gold/Champagne - Muted text */\n    --accent: 40 35% 55%;          /* Vierla Gold - Accent elements (NO GREEN) */\n    --accent-foreground: 44 7% 17%; /* Deep Charcoal - Text on accent */\n    --destructive: 25 95% 53%;     /* Terracotta - Error/destructive */\n    --destructive-foreground: 0 0% 98%; /* White - Text on destructive */\n    --border: 40 20% 65% / 0.3;    /* Muted Gold/Champagne with opacity - Borders (NO GREEN) */\n    --input: 40 20% 65% / 0.3;     /* Muted Gold/Champagne with opacity - Input borders (NO GREEN) */\n    --ring: 40 35% 55%;            /* Vierla Gold - Focus rings (NO GREEN) */\n\n    /* Dark mode master variables */\n    --master-brand-secondary: #2D2A26;\n    --master-text-on-dark: #F4F1E8;\n    --master-card-background: #333333;\n  }\n\n  /* Light mode overrides */\n  :root:not(.dark) {\n    /*\n    ========================================\n    | 🎨 SHADCN UI THEME VARIABLES - Light Mode\n    | \"Natural Wellness\" Theme - Green, Cream & Charcoal Palette\n    ========================================\n    */\n    --background: 44 15% 95%;      /* Warm Cream (#F4F1E8) - Main background */\n    --foreground: 44 7% 17%;       /* Deep Charcoal (#2D2A26) - Main text */\n    --card: 0 0% 95%;              /* Very light gray - Card backgrounds */\n    --card-foreground: 44 7% 17%;  /* Deep Charcoal - Card text */\n    --popover: 0 0% 98%;           /* Almost white - Popover backgrounds */\n    --popover-foreground: 44 7% 17%; /* Deep Charcoal - Popover text */\n    --primary: 150 8% 22%;         /* Vierla Forest (#364035) - Primary CTAs, active nav */\n    --primary-foreground: 44 15% 95%; /* Warm Cream - Text on primary */\n    --secondary: 150 12% 55%;      /* Vierla Sage (#8B9A8C) - Secondary buttons, sub-headings */\n    --secondary-foreground: 44 7% 17%; /* Deep Charcoal - Text on secondary */\n    --muted: 0 0% 95%;             /* Very light gray - Muted backgrounds */\n    --muted-foreground: 150 12% 55%; /* Vierla Sage - Muted text */\n    --accent: 150 8% 22%;          /* Vierla Forest (#364035) - Accent elements (NO GOLD in light mode) */\n    --accent-foreground: 44 15% 95%; /* Warm Cream - Text on accent */\n    --destructive: 25 95% 53%;     /* Terracotta - Error/destructive */\n    --destructive-foreground: 0 0% 98%; /* White - Text on destructive */\n    --border: 150 12% 55% / 0.3;   /* Vierla Sage with opacity - Borders */\n    --input: 150 12% 55% / 0.3;    /* Vierla Sage with opacity - Input borders */\n    --ring: 150 8% 22%;            /* Vierla Forest - Focus rings (NO GOLD in light mode) */\n\n    /* Light mode master variables - Natural Wellness Palette */\n    --master-brand-secondary: #F4F1E8;      /* Warm Cream background */\n    --master-text-on-dark: #2D2A26;         /* Deep Charcoal text */\n    --master-card-background: #F4F1E8;      /* Warm Cream cards */\n    --master-accent-primary-light: #364035; /* Vierla Forest - Primary accent for light mode */\n    --master-accent-secondary-light: #8B9A8C; /* Vierla Sage - Secondary accent for light mode */\n  }\n\n  /*\n  ========================================\n  | BASE ELEMENT STYLES\n  ========================================\n  */\n  * {\n  border-color: hsl(var(--border));\n}\n  body {\n  color: hsl(var(--foreground));\n    font-family: var(--font-family-body); /* Inter for body text */\n    background-color: var(--master-brand-secondary); /* Dark Charcoal primary background */\n    color: var(--master-text-on-dark); /* Light Off-White text */\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n    font-size: var(--font-size-body);\n}\n\n  /* Desktop Scale Reduction - 10% smaller content on non-mobile devices */\n  @media (min-width: 768px) {\n    .page-home,\n    .page-about,\n    .page-features,\n    .page-pricing,\n    .page-provider,\n    .page-apply,\n    .page-privacy,\n    .page-terms {\n      transform: scale(0.9);\n      transform-origin: top center;\n      width: 111.11%; /* Compensate for scale to maintain full width */\n      margin-left: -5.56%; /* Center the scaled content */\n    }\n  }\n\n  /* Typography Hierarchy - New Visual Identity */\n  h1 {\n    font-family: var(--font-family-h1);\n    font-size: var(--font-size-h1);\n    font-weight: 700;\n    line-height: 1.2;\n    text-transform: uppercase;\n    letter-spacing: 0.02em;\n  }\n  h2 {\n    font-family: var(--font-family-h2);\n    font-size: var(--font-size-h2);\n    font-weight: 700;\n    line-height: 1.3;\n  }\n  h3 {\n    font-family: var(--font-family-h3);\n    font-size: var(--font-size-h3);\n    font-weight: 400;\n    line-height: 1.4;\n  }\n  h4 {\n    font-family: var(--font-family-body);\n    font-size: var(--font-size-h4);\n    font-weight: 700;\n    line-height: 1.4;\n  }\n  h5 {\n    font-family: var(--font-family-body);\n    font-size: var(--font-size-h5);\n    font-weight: 700;\n    line-height: 1.5;\n  }\n  h6 {\n    font-family: var(--font-family-body);\n    font-size: var(--font-size-body);\n    font-weight: 600;\n    line-height: 1.5;\n  }\n\n  p {\n    font-family: var(--font-family-body);\n    font-size: var(--font-size-body);\n    font-weight: 400;\n    line-height: 1.6;\n  }\n\n  small, .text-small {\n    font-family: var(--font-family-body);\n    font-size: var(--font-size-small);\n    font-weight: 500;\n    line-height: 1.4;\n  }\n\n  /* Accent text class for optional signature/handwritten touch */\n.container {\n  width: 100%;\n}\n@media (min-width: 640px) {\n\n  .container {\n    max-width: 640px;\n  }\n}\n@media (min-width: 768px) {\n\n  .container {\n    max-width: 768px;\n  }\n}\n@media (min-width: 1024px) {\n\n  .container {\n    max-width: 1024px;\n  }\n}\n@media (min-width: 1280px) {\n\n  .container {\n    max-width: 1280px;\n  }\n}\n@media (min-width: 1536px) {\n\n  .container {\n    max-width: 1536px;\n  }\n}\n.sr-only {\n  position: absolute;\n  width: 1px;\n  height: 1px;\n  padding: 0;\n  margin: -1px;\n  overflow: hidden;\n  clip: rect(0, 0, 0, 0);\n  white-space: nowrap;\n  border-width: 0;\n}\n.pointer-events-none {\n  pointer-events: none;\n}\n.visible {\n  visibility: visible;\n}\n.fixed {\n  position: fixed;\n}\n.absolute {\n  position: absolute;\n}\n.relative {\n  position: relative;\n}\n.-inset-0\\.5 {\n  inset: -0.125rem;\n}\n.-inset-\\[10px\\] {\n  inset: -10px;\n}\n.-inset-full {\n  inset: -100%;\n}\n.-inset-px {\n  inset: -1px;\n}\n.inset-0 {\n  inset: 0px;\n}\n.inset-px {\n  inset: 1px;\n}\n.inset-x-0 {\n  left: 0px;\n  right: 0px;\n}\n.inset-y-0 {\n  top: 0px;\n  bottom: 0px;\n}\n.-right-20 {\n  right: calc(var(--space-20) * -1);\n}\n.-top-20 {\n  top: calc(var(--space-20) * -1);\n}\n.-top-3 {\n  top: calc(var(--space-3) * -1);\n}\n.bottom-0 {\n  bottom: 0px;\n}\n.bottom-1 {\n  bottom: var(--space-1);\n}\n.left-0 {\n  left: 0px;\n}\n.left-1\\/2 {\n  left: 50%;\n}\n.left-\\[20\\%\\] {\n  left: 20%;\n}\n.right-0 {\n  right: 0px;\n}\n.right-0\\.5 {\n  right: 0.125rem;\n}\n.right-4 {\n  right: var(--space-4);\n}\n.top-0 {\n  top: 0px;\n}\n.top-1 {\n  top: var(--space-1);\n}\n.top-4 {\n  top: var(--space-4);\n}\n.top-\\[40\\%\\] {\n  top: 40%;\n}\n.-z-10 {\n  z-index: -10;\n}\n.-z-20 {\n  z-index: -20;\n}\n.-z-30 {\n  z-index: -30;\n}\n.z-0 {\n  z-index: 0;\n}\n.z-10 {\n  z-index: 10;\n}\n.z-50 {\n  z-index: 50;\n}\n.col-span-3 {\n  grid-column: span 3 / span 3;\n}\n.mx-auto {\n  margin-left: auto;\n  margin-right: auto;\n}\n.-mr-1 {\n  margin-right: calc(var(--space-1) * -1);\n}\n.-mt-1 {\n  margin-top: calc(var(--space-1) * -1);\n}\n.mb-1 {\n  margin-bottom: var(--space-1);\n}\n.mb-12 {\n  margin-bottom: var(--space-12);\n}\n.mb-16 {\n  margin-bottom: var(--space-16);\n}\n.mb-2 {\n  margin-bottom: var(--space-2);\n}\n.mb-3 {\n  margin-bottom: var(--space-3);\n}\n.mb-4 {\n  margin-bottom: var(--space-4);\n}\n.mb-6 {\n  margin-bottom: var(--space-6);\n}\n.mb-8 {\n  margin-bottom: var(--space-8);\n}\n.ml-1 {\n  margin-left: var(--space-1);\n}\n.ml-2 {\n  margin-left: var(--space-2);\n}\n.mr-1 {\n  margin-right: var(--space-1);\n}\n.mr-2 {\n  margin-right: var(--space-2);\n}\n.mr-3 {\n  margin-right: var(--space-3);\n}\n.mr-8 {\n  margin-right: var(--space-8);\n}\n.mt-0\\.5 {\n  margin-top: 0.125rem;\n}\n.mt-1 {\n  margin-top: var(--space-1);\n}\n.mt-1\\.5 {\n  margin-top: 0.375rem;\n}\n.mt-12 {\n  margin-top: var(--space-12);\n}\n.mt-16 {\n  margin-top: var(--space-16);\n}\n.mt-2 {\n  margin-top: var(--space-2);\n}\n.mt-4 {\n  margin-top: var(--space-4);\n}\n.mt-6 {\n  margin-top: var(--space-6);\n}\n.mt-8 {\n  margin-top: var(--space-8);\n}\n.mt-auto {\n  margin-top: auto;\n}\n.\\!block {\n  display: block !important;\n}\n.block {\n  display: block;\n}\n.inline-block {\n  display: inline-block;\n}\n.inline {\n  display: inline;\n}\n.flex {\n  display: flex;\n}\n.inline-flex {\n  display: inline-flex;\n}\n.table {\n  display: table;\n}\n.grid {\n  display: grid;\n}\n.\\!hidden {\n  display: none !important;\n}\n.hidden {\n  display: none;\n}\n.size-full {\n  width: 100%;\n  height: 100%;\n}\n.h-1 {\n  height: var(--space-1);\n}\n.h-1\\.5 {\n  height: 0.375rem;\n}\n.h-10 {\n  height: var(--space-10);\n}\n.h-11 {\n  height: 2.75rem;\n}\n.h-12 {\n  height: var(--space-12);\n}\n.h-14 {\n  height: 3.5rem;\n}\n.h-16 {\n  height: var(--space-16);\n}\n.h-2 {\n  height: var(--space-2);\n}\n.h-20 {\n  height: var(--space-20);\n}\n.h-28 {\n  height: 7rem;\n}\n.h-32 {\n  height: 8rem;\n}\n.h-4 {\n  height: var(--space-4);\n}\n.h-40 {\n  height: 10rem;\n}\n.h-5 {\n  height: var(--space-5);\n}\n.h-6 {\n  height: var(--space-6);\n}\n.h-8 {\n  height: var(--space-8);\n}\n.h-9 {\n  height: var(--space-9);\n}\n.h-\\[100\\%\\] {\n  height: 100%;\n}\n.h-\\[100cqh\\] {\n  height: 100cqh;\n}\n.h-\\[100vh\\] {\n  height: 100vh;\n}\n.h-full {\n  height: 100%;\n}\n.h-px {\n  height: 1px;\n}\n.max-h-48 {\n  max-height: 12rem;\n}\n.max-h-60 {\n  max-height: 15rem;\n}\n.min-h-\\[1000px\\] {\n  min-height: 1000px;\n}\n.min-h-\\[300px\\] {\n  min-height: 300px;\n}\n.min-h-\\[400px\\] {\n  min-height: 400px;\n}\n.min-h-\\[800px\\] {\n  min-height: 800px;\n}\n.min-h-\\[80px\\] {\n  min-height: 80px;\n}\n.min-h-screen {\n  min-height: 100vh;\n}\n.w-1\\.5 {\n  width: 0.375rem;\n}\n.w-1\\/4 {\n  width: 25%;\n}\n.w-10 {\n  width: var(--space-10);\n}\n.w-12 {\n  width: var(--space-12);\n}\n.w-14 {\n  width: 3.5rem;\n}\n.w-16 {\n  width: var(--space-16);\n}\n.w-2 {\n  width: var(--space-2);\n}\n.w-20 {\n  width: var(--space-20);\n}\n.w-28 {\n  width: 7rem;\n}\n.w-3\\/4 {\n  width: 75%;\n}\n.w-32 {\n  width: 8rem;\n}\n.w-4 {\n  width: var(--space-4);\n}\n.w-40 {\n  width: 10rem;\n}\n.w-5 {\n  width: var(--space-5);\n}\n.w-6 {\n  width: var(--space-6);\n}\n.w-8 {\n  width: var(--space-8);\n}\n.w-9 {\n  width: var(--space-9);\n}\n.w-\\[100\\%\\] {\n  width: 100%;\n}\n.w-\\[300px\\] {\n  width: 300px;\n}\n.w-auto {\n  width: auto;\n}\n.w-full {\n  width: 100%;\n}\n.min-w-\\[280px\\] {\n  min-width: 280px;\n}\n.min-w-\\[300px\\] {\n  min-width: 300px;\n}\n.max-w-2xl {\n  max-width: 42rem;\n}\n.max-w-3xl {\n  max-width: 48rem;\n}\n.max-w-4xl {\n  max-width: 56rem;\n}\n.max-w-6xl {\n  max-width: 72rem;\n}\n.max-w-7xl {\n  max-width: 80rem;\n}\n.max-w-\\[100vw\\] {\n  max-width: 100vw;\n}\n.max-w-\\[180px\\] {\n  max-width: 180px;\n}\n.max-w-\\[400px\\] {\n  max-width: 400px;\n}\n.max-w-\\[42rem\\] {\n  max-width: 42rem;\n}\n.max-w-\\[58rem\\] {\n  max-width: 58rem;\n}\n.max-w-lg {\n  max-width: 32rem;\n}\n.max-w-md {\n  max-width: 28rem;\n}\n.max-w-none {\n  max-width: none;\n}\n.max-w-xs {\n  max-width: 20rem;\n}\n.flex-1 {\n  flex: 1 1 0%;\n}\n.flex-shrink-0 {\n  flex-shrink: 0;\n}\n.shrink-0 {\n  flex-shrink: 0;\n}\n.flex-grow {\n  flex-grow: 1;\n}\n.grow {\n  flex-grow: 1;\n}\n.origin-left {\n  transform-origin: left;\n}\n.-translate-x-1\\/2 {\n  --tw-translate-x: -50%;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-x-1 {\n  --tw-translate-x: var(--space-1);\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-x-12 {\n  --tw-translate-x: var(--space-12);\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.translate-y-2 {\n  --tw-translate-y: var(--space-2);\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.rotate-0 {\n  --tw-rotate: 0deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.rotate-180 {\n  --tw-rotate: 180deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.scale-\\[1\\] {\n  --tw-scale-x: 1;\n  --tw-scale-y: 1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.transform {\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n.transform-gpu {\n  transform: translate3d(var(--tw-translate-x), var(--tw-translate-y), 0) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n@keyframes shimmer-slide {\n\n  to {\n    transform: translate(calc(100cqw - 100%), 0);\n  }\n}\n.animate-shimmer-slide {\n  animation: shimmer-slide var(--speed) ease-in-out infinite alternate;\n}\n@keyframes spin-around {\n\n  0% {\n    transform: translateZ(0) rotate(0);\n  }\n\n  15%, 35% {\n    transform: translateZ(0) rotate(90deg);\n  }\n\n  65%, 85% {\n    transform: translateZ(0) rotate(270deg);\n  }\n\n  100% {\n    transform: translateZ(0) rotate(360deg);\n  }\n}\n.animate-spin-around {\n  animation: spin-around calc(var(--speed) * 2) infinite linear;\n}\n.cursor-not-allowed {\n  cursor: not-allowed;\n}\n.cursor-pointer {\n  cursor: pointer;\n}\n.select-none {\n  user-select: none;\n}\n.resize-none {\n  resize: none;\n}\n.auto-rows-\\[20rem\\] {\n  grid-auto-rows: 20rem;\n}\n.grid-cols-1 {\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n}\n.grid-cols-3 {\n  grid-template-columns: repeat(3, minmax(0, 1fr));\n}\n.flex-col {\n  flex-direction: column;\n}\n.flex-col-reverse {\n  flex-direction: column-reverse;\n}\n.flex-wrap {\n  flex-wrap: wrap;\n}\n.flex-nowrap {\n  flex-wrap: nowrap;\n}\n.place-items-center {\n  place-items: center;\n}\n.items-start {\n  align-items: flex-start;\n}\n.items-center {\n  align-items: center;\n}\n.justify-start {\n  justify-content: flex-start;\n}\n.justify-end {\n  justify-content: flex-end;\n}\n.justify-center {\n  justify-content: center;\n}\n.justify-between {\n  justify-content: space-between;\n}\n.gap-1 {\n  gap: var(--space-1);\n}\n.gap-12 {\n  gap: var(--space-12);\n}\n.gap-2 {\n  gap: var(--space-2);\n}\n.gap-3 {\n  gap: var(--space-3);\n}\n.gap-4 {\n  gap: var(--space-4);\n}\n.gap-6 {\n  gap: var(--space-6);\n}\n.gap-8 {\n  gap: var(--space-8);\n}\n.space-x-2 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(var(--space-2) * var(--tw-space-x-reverse));\n  margin-left: calc(var(--space-2) * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-3 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(var(--space-3) * var(--tw-space-x-reverse));\n  margin-left: calc(var(--space-3) * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-x-4 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-x-reverse: 0;\n  margin-right: calc(var(--space-4) * var(--tw-space-x-reverse));\n  margin-left: calc(var(--space-4) * calc(1 - var(--tw-space-x-reverse)));\n}\n.space-y-1 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(var(--space-1) * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(var(--space-1) * var(--tw-space-y-reverse));\n}\n.space-y-1\\.5 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));\n}\n.space-y-2 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(var(--space-2) * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(var(--space-2) * var(--tw-space-y-reverse));\n}\n.space-y-3 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(var(--space-3) * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(var(--space-3) * var(--tw-space-y-reverse));\n}\n.space-y-4 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(var(--space-4) * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(var(--space-4) * var(--tw-space-y-reverse));\n}\n.space-y-6 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(var(--space-6) * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(var(--space-6) * var(--tw-space-y-reverse));\n}\n.space-y-8 > :not([hidden]) ~ :not([hidden]) {\n  --tw-space-y-reverse: 0;\n  margin-top: calc(var(--space-8) * calc(1 - var(--tw-space-y-reverse)));\n  margin-bottom: calc(var(--space-8) * var(--tw-space-y-reverse));\n}\n.overflow-auto {\n  overflow: auto;\n}\n.overflow-hidden {\n  overflow: hidden;\n}\n.overflow-visible {\n  overflow: visible;\n}\n.overflow-x-auto {\n  overflow-x: auto;\n}\n.overflow-y-auto {\n  overflow-y: auto;\n}\n.truncate {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.whitespace-nowrap {\n  white-space: nowrap;\n}\n.text-nowrap {\n  text-wrap: nowrap;\n}\n.rounded-2xl {\n  border-radius: 1rem;\n}\n.rounded-3xl {\n  border-radius: 1.5rem;\n}\n.rounded-\\[1\\.25rem\\] {\n  border-radius: 1.25rem;\n}\n.rounded-\\[22px\\] {\n  border-radius: 22px;\n}\n.rounded-\\[inherit\\] {\n  border-radius: inherit;\n}\n.rounded-full {\n  border-radius: 9999px;\n}\n.rounded-lg {\n  border-radius: var(--radius);\n}\n.rounded-md {\n  border-radius: calc(var(--radius) - 2px);\n}\n.rounded-sm {\n  border-radius: calc(var(--radius) - 4px);\n}\n.rounded-xl {\n  border-radius: 0.75rem;\n}\n.border {\n  border-width: 1px;\n}\n.border-0 {\n  border-width: 0px;\n}\n.border-2 {\n  border-width: 2px;\n}\n.border-\\[0\\.75px\\] {\n  border-width: 0.75px;\n}\n.border-b {\n  border-bottom-width: 1px;\n}\n.border-l {\n  border-left-width: 1px;\n}\n.border-r {\n  border-right-width: 1px;\n}\n.border-t {\n  border-top-width: 1px;\n}\n.border-\\[var\\(--header-footer-border\\)\\] {\n  border-color: var(--header-footer-border);\n}\n.border-\\[var\\(--master-brand-accent\\)\\] {\n  border-color: var(--master-brand-accent);\n}\n.border-\\[var\\(--master-input-border\\)\\] {\n  border-color: var(--master-input-border);\n}\n.border-blue-400 {\n  --tw-border-opacity: 1;\n  border-color: rgb(96 165 250 / var(--tw-border-opacity, 1));\n}\n.border-brand-gold {\n  --tw-border-opacity: 1;\n  border-color: rgb(184 149 106 / var(--tw-border-opacity, 1));\n}\n.border-brand-sage {\n  --tw-border-opacity: 1;\n  border-color: rgb(139 154 140 / var(--tw-border-opacity, 1));\n}\n.border-brand-sage\\/10 {\n  border-color: rgb(139 154 140 / 0.1);\n}\n.border-brand-sage\\/20 {\n  border-color: rgb(139 154 140 / 0.2);\n}\n.border-error {\n  --tw-border-opacity: 1;\n  border-color: rgb(217 119 6 / var(--tw-border-opacity, 1));\n}\n.border-green-400 {\n  --tw-border-opacity: 1;\n  border-color: rgb(74 222 128 / var(--tw-border-opacity, 1));\n}\n.border-input {\n  border-color: hsl(var(--input));\n}\n.border-mantle-700 {\n  --tw-border-opacity: 1;\n  border-color: rgb(73 104 82 / var(--tw-border-opacity, 1));\n}\n.border-neutral-off-white\\/20 {\n  border-color: rgb(244 241 232 / 0.2);\n}\n.border-primary {\n  border-color: hsl(var(--primary));\n}\n.border-red-400 {\n  --tw-border-opacity: 1;\n  border-color: rgb(248 113 113 / var(--tw-border-opacity, 1));\n}\n.border-red-500\\/30 {\n  border-color: rgb(239 68 68 / 0.3);\n}\n.border-sage {\n  --tw-border-opacity: 1;\n  border-color: rgb(139 154 140 / var(--tw-border-opacity, 1));\n}\n.border-sage\\/10 {\n  border-color: rgb(139 154 140 / 0.1);\n}\n.border-sage\\/20 {\n  border-color: rgb(139 154 140 / 0.2);\n}\n.border-sage\\/30 {\n  border-color: rgb(139 154 140 / 0.3);\n}\n.border-transparent {\n  border-color: transparent;\n}\n.border-white {\n  --tw-border-opacity: 1;\n  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));\n}\n.border-white\\/10 {\n  border-color: rgb(255 255 255 / 0.1);\n}\n.border-white\\/20 {\n  border-color: rgb(255 255 255 / 0.2);\n}\n.border-yellow-400 {\n  --tw-border-opacity: 1;\n  border-color: rgb(250 204 21 / var(--tw-border-opacity, 1));\n}\n.bg-\\[var\\(--aurora-bg\\)\\] {\n  background-color: var(--aurora-bg);\n}\n.bg-\\[var\\(--header-footer-bg\\)\\] {\n  background-color: var(--header-footer-bg);\n}\n.bg-\\[var\\(--master-action-primary\\)\\] {\n  background-color: var(--master-action-primary);\n}\n.bg-\\[var\\(--master-action-secondary\\)\\] {\n  background-color: var(--master-action-secondary);\n}\n.bg-\\[var\\(--master-brand-accent\\)\\] {\n  background-color: var(--master-brand-accent);\n}\n.bg-\\[var\\(--master-input-bg-light\\)\\] {\n  background-color: var(--master-input-bg-light);\n}\n.bg-background {\n  background-color: hsl(var(--background));\n}\n.bg-black\\/50 {\n  background-color: rgb(0 0 0 / 0.5);\n}\n.bg-blue-500\\/90 {\n  background-color: rgb(59 130 246 / 0.9);\n}\n.bg-brand-gold {\n  --tw-bg-opacity: 1;\n  background-color: rgb(184 149 106 / var(--tw-bg-opacity, 1));\n}\n.bg-brand-gold\\/20 {\n  background-color: rgb(184 149 106 / 0.2);\n}\n.bg-card {\n  background-color: hsl(var(--card));\n}\n.bg-charcoal\\/50 {\n  background-color: rgb(44 49 55 / 0.5);\n}\n.bg-destructive {\n  background-color: hsl(var(--destructive));\n}\n.bg-gold {\n  --tw-bg-opacity: 1;\n  background-color: rgb(184 149 106 / var(--tw-bg-opacity, 1));\n}\n.bg-green-500\\/90 {\n  background-color: rgb(34 197 94 / 0.9);\n}\n.bg-neutral-charcoal-dark\\/40 {\n  background-color: rgb(45 42 38 / 0.4);\n}\n.bg-neutral-charcoal-dark\\/60 {\n  background-color: rgb(45 42 38 / 0.6);\n}\n.bg-neutral-charcoal-dark\\/80 {\n  background-color: rgb(45 42 38 / 0.8);\n}\n.bg-neutral-charcoal-dark\\/95 {\n  background-color: rgb(45 42 38 / 0.95);\n}\n.bg-neutral-charcoal-light\\/20 {\n  background-color: rgb(51 51 51 / 0.2);\n}\n.bg-neutral-charcoal-light\\/90 {\n  background-color: rgb(51 51 51 / 0.9);\n}\n.bg-primary {\n  background-color: hsl(var(--primary));\n}\n.bg-primary\\/20 {\n  background-color: hsl(var(--primary) / 0.2);\n}\n.bg-red-500\\/20 {\n  background-color: rgb(239 68 68 / 0.2);\n}\n.bg-red-500\\/90 {\n  background-color: rgb(239 68 68 / 0.9);\n}\n.bg-sage {\n  --tw-bg-opacity: 1;\n  background-color: rgb(139 154 140 / var(--tw-bg-opacity, 1));\n}\n.bg-sage\\/10 {\n  background-color: rgb(139 154 140 / 0.1);\n}\n.bg-sage\\/20 {\n  background-color: rgb(139 154 140 / 0.2);\n}\n.bg-secondary {\n  background-color: hsl(var(--secondary));\n}\n.bg-theme-primary\\/10 {\n  background-color: rgb(54 64 53 / 0.1);\n}\n.bg-transparent {\n  background-color: transparent;\n}\n.bg-white\\/10 {\n  background-color: rgb(255 255 255 / 0.1);\n}\n.bg-white\\/15 {\n  background-color: rgb(255 255 255 / 0.15);\n}\n.bg-white\\/5 {\n  background-color: rgb(255 255 255 / 0.05);\n}\n.bg-yellow-500\\/90 {\n  background-color: rgb(234 179 8 / 0.9);\n}\n.bg-gradient-to-br {\n  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));\n}\n.from-primary\\/20 {\n  --tw-gradient-from: hsl(var(--primary) / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.from-sage\\/20 {\n  --tw-gradient-from: rgb(139 154 140 / 0.2) var(--tw-gradient-from-position);\n  --tw-gradient-to: rgb(139 154 140 / 0) var(--tw-gradient-to-position);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n.to-primary\\/5 {\n  --tw-gradient-to: hsl(var(--primary) / 0.05) var(--tw-gradient-to-position);\n}\n.to-sage\\/10 {\n  --tw-gradient-to: rgb(139 154 140 / 0.1) var(--tw-gradient-to-position);\n}\n.bg-\\[length\\:250\\%_100\\%\\2c auto\\] {\n  background-size: 250% 100%,auto;\n}\n.bg-clip-text {\n  background-clip: text;\n}\n.p-10 {\n  padding: var(--space-10);\n}\n.p-12 {\n  padding: var(--space-12);\n}\n.p-2 {\n  padding: var(--space-2);\n}\n.p-4 {\n  padding: var(--space-4);\n}\n.p-6 {\n  padding: var(--space-6);\n}\n.p-8 {\n  padding: var(--space-8);\n}\n.p-px {\n  padding: 1px;\n}\n.px-10 {\n  padding-left: var(--space-10);\n  padding-right: var(--space-10);\n}\n.px-2 {\n  padding-left: var(--space-2);\n  padding-right: var(--space-2);\n}\n.px-2\\.5 {\n  padding-left: 0.625rem;\n  padding-right: 0.625rem;\n}\n.px-3 {\n  padding-left: var(--space-3);\n  padding-right: var(--space-3);\n}\n.px-4 {\n  padding-left: var(--space-4);\n  padding-right: var(--space-4);\n}\n.px-6 {\n  padding-left: var(--space-6);\n  padding-right: var(--space-6);\n}\n.px-8 {\n  padding-left: var(--space-8);\n  padding-right: var(--space-8);\n}\n.py-0\\.5 {\n  padding-top: 0.125rem;\n  padding-bottom: 0.125rem;\n}\n.py-1 {\n  padding-top: var(--space-1);\n  padding-bottom: var(--space-1);\n}\n.py-1\\.5 {\n  padding-top: 0.375rem;\n  padding-bottom: 0.375rem;\n}\n.py-10 {\n  padding-top: var(--space-10);\n  padding-bottom: var(--space-10);\n}\n.py-12 {\n  padding-top: var(--space-12);\n  padding-bottom: var(--space-12);\n}\n.py-16 {\n  padding-top: var(--space-16);\n  padding-bottom: var(--space-16);\n}\n.py-2 {\n  padding-top: var(--space-2);\n  padding-bottom: var(--space-2);\n}\n.py-20 {\n  padding-top: var(--space-20);\n  padding-bottom: var(--space-20);\n}\n.py-3 {\n  padding-top: var(--space-3);\n  padding-bottom: var(--space-3);\n}\n.py-4 {\n  padding-top: var(--space-4);\n  padding-bottom: var(--space-4);\n}\n.py-5 {\n  padding-top: var(--space-5);\n  padding-bottom: var(--space-5);\n}\n.py-8 {\n  padding-top: var(--space-8);\n  padding-bottom: var(--space-8);\n}\n.pb-4 {\n  padding-bottom: var(--space-4);\n}\n.pb-6 {\n  padding-bottom: var(--space-6);\n}\n.pb-8 {\n  padding-bottom: var(--space-8);\n}\n.pl-10 {\n  padding-left: var(--space-10);\n}\n.pl-3 {\n  padding-left: var(--space-3);\n}\n.pr-4 {\n  padding-right: var(--space-4);\n}\n.pt-0 {\n  padding-top: 0px;\n}\n.pt-16 {\n  padding-top: var(--space-16);\n}\n.pt-24 {\n  padding-top: var(--space-24);\n}\n.pt-32 {\n  padding-top: 8rem;\n}\n.pt-4 {\n  padding-top: var(--space-4);\n}\n.pt-6 {\n  padding-top: var(--space-6);\n}\n.text-left {\n  text-align: left;\n}\n.text-center {\n  text-align: center;\n}\n.font-inter {\n  font-family: var(--font-inter), Inter, system-ui, sans-serif;\n}\n.font-jost {\n  font-family: var(--font-jost), Jost, system-ui, sans-serif;\n}\n.font-sans {\n  font-family: var(--font-inter), Inter, system-ui, sans-serif;\n}\n.font-serif {\n  font-family: var(--font-tai-heritage), Georgia, serif;\n}\n.font-tai-heritage {\n  font-family: var(--font-tai-heritage), Georgia, serif;\n}\n.text-2xl {\n  font-size: 1.5rem;\n  line-height: 2rem;\n}\n.text-3xl {\n  font-size: 1.875rem;\n  line-height: 2.25rem;\n}\n.text-4xl {\n  font-size: 2.25rem;\n  line-height: 2.5rem;\n}\n.text-5xl {\n  font-size: 3rem;\n  line-height: 1;\n}\n.text-base {\n  font-size: 1rem;\n  line-height: 1.5rem;\n}\n.text-lg {\n  font-size: 1.125rem;\n  line-height: 1.75rem;\n}\n.text-sm {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\n.text-xl {\n  font-size: 1.25rem;\n  line-height: 1.75rem;\n}\n.text-xs {\n  font-size: 0.75rem;\n  line-height: 1rem;\n}\n.font-\\[var\\(--font-family-body\\)\\] {\n  font-weight: var(--font-family-body);\n}\n.font-black {\n  font-weight: 900;\n}\n.font-bold {\n  font-weight: 700;\n}\n.font-light {\n  font-weight: 300;\n}\n.font-medium {\n  font-weight: 500;\n}\n.font-semibold {\n  font-weight: 600;\n}\n.uppercase {\n  text-transform: uppercase;\n}\n.leading-\\[1\\.1\\] {\n  line-height: 1.1;\n}\n.leading-\\[5rem\\] {\n  line-height: 5rem;\n}\n.leading-none {\n  line-height: 1;\n}\n.leading-normal {\n  line-height: 1.5;\n}\n.leading-relaxed {\n  line-height: 1.625;\n}\n.leading-tight {\n  line-height: 1.25;\n}\n.tracking-\\[-0\\.02em\\] {\n  letter-spacing: -0.02em;\n}\n.tracking-tight {\n  letter-spacing: -0.025em;\n}\n.tracking-wide {\n  letter-spacing: 0.025em;\n}\n.text-\\[\\#2D2A26\\] {\n  --tw-text-opacity: 1;\n  color: rgb(45 42 38 / var(--tw-text-opacity, 1));\n}\n.text-\\[var\\(--master-action-tertiary\\)\\] {\n  color: var(--master-action-tertiary);\n}\n.text-\\[var\\(--master-icon-success\\)\\] {\n  color: var(--master-icon-success);\n}\n.text-\\[var\\(--master-label-color\\)\\] {\n  color: var(--master-label-color);\n}\n.text-\\[var\\(--master-text-primary-dark\\)\\] {\n  color: var(--master-text-primary-dark);\n}\n.text-\\[var\\(--master-text-primary-light\\)\\] {\n  color: var(--master-text-primary-light);\n}\n.text-\\[var\\(--master-text-secondary-dark\\)\\] {\n  color: var(--master-text-secondary-dark);\n}\n.text-brand-beige {\n  --tw-text-opacity: 1;\n  color: rgb(240 230 217 / var(--tw-text-opacity, 1));\n}\n.text-brand-beige\\/70 {\n  color: rgb(240 230 217 / 0.7);\n}\n.text-card-foreground {\n  color: hsl(var(--card-foreground));\n}\n.text-current {\n  color: currentColor;\n}\n.text-destructive-foreground {\n  color: hsl(var(--destructive-foreground));\n}\n.text-error {\n  --tw-text-opacity: 1;\n  color: rgb(217 119 6 / var(--tw-text-opacity, 1));\n}\n.text-foreground {\n  color: hsl(var(--foreground));\n}\n.text-gray-300 {\n  --tw-text-opacity: 1;\n  color: rgb(209 213 219 / var(--tw-text-opacity, 1));\n}\n.text-mantle-100 {\n  --tw-text-opacity: 1;\n  color: rgb(240 230 217 / var(--tw-text-opacity, 1));\n}\n.text-mantle-300 {\n  --tw-text-opacity: 1;\n  color: rgb(208 197 183 / var(--tw-text-opacity, 1));\n}\n.text-mantle-950 {\n  --tw-text-opacity: 1;\n  color: rgb(44 49 55 / var(--tw-text-opacity, 1));\n}\n.text-muted-foreground {\n  color: hsl(var(--muted-foreground));\n}\n.text-neutral-charcoal-dark {\n  --tw-text-opacity: 1;\n  color: rgb(45 42 38 / var(--tw-text-opacity, 1));\n}\n.text-neutral-off-white {\n  --tw-text-opacity: 1;\n  color: rgb(244 241 232 / var(--tw-text-opacity, 1));\n}\n.text-primary {\n  color: hsl(var(--primary));\n}\n.text-primary-foreground {\n  color: hsl(var(--primary-foreground));\n}\n.text-red-200 {\n  --tw-text-opacity: 1;\n  color: rgb(254 202 202 / var(--tw-text-opacity, 1));\n}\n.text-sage {\n  --tw-text-opacity: 1;\n  color: rgb(139 154 140 / var(--tw-text-opacity, 1));\n}\n.text-secondary-foreground {\n  color: hsl(var(--secondary-foreground));\n}\n.text-slate-950 {\n  --tw-text-opacity: 1;\n  color: rgb(2 6 23 / var(--tw-text-opacity, 1));\n}\n.text-theme-primary {\n  --tw-text-opacity: 1;\n  color: rgb(54 64 53 / var(--tw-text-opacity, 1));\n}\n.text-transparent {\n  color: transparent;\n}\n.text-white {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n.text-white\\/70 {\n  color: rgb(255 255 255 / 0.7);\n}\n.underline {\n  text-decoration-line: underline;\n}\n.underline-offset-4 {\n  text-underline-offset: 4px;\n}\n.antialiased {\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n.accent-brand-sage {\n  accent-color: #8B9A8C;\n}\n.accent-primary {\n  accent-color: hsl(var(--primary));\n}\n.accent-secondary {\n  accent-color: hsl(var(--secondary));\n}\n.opacity-0 {\n  opacity: 0;\n}\n.opacity-100 {\n  opacity: 1;\n}\n.opacity-50 {\n  opacity: 0.5;\n}\n.opacity-60 {\n  opacity: 0.6;\n}\n.opacity-70 {\n  opacity: 0.7;\n}\n.opacity-75 {\n  opacity: 0.75;\n}\n.opacity-85 {\n  opacity: 0.85;\n}\n.opacity-90 {\n  opacity: 0.9;\n}\n.shadow-2xl {\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-\\[0_1000px_0_0_var\\(--mantle-50\\)_inset\\] {\n  --tw-shadow: 0 1000px 0 0 var(--mantle-50) inset;\n  --tw-shadow-colored: inset 0 1000px 0 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-\\[inset_0_-8px_10px_\\#ffffff1f\\] {\n  --tw-shadow: inset 0 -8px 10px #ffffff1f;\n  --tw-shadow-colored: inset 0 -8px 10px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-lg {\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-sm {\n  --tw-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);\n  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.shadow-xl {\n  --tw-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n.outline {\n  outline-style: solid;\n}\n.ring {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.ring-2 {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.ring-4 {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n.ring-offset-background {\n  --tw-ring-offset-color: hsl(var(--background));\n}\n.blur {\n  --tw-blur: blur(8px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.blur-3xl {\n  --tw-blur: blur(64px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.blur-\\[2px\\] {\n  --tw-blur: blur(2px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.blur-\\[8px\\] {\n  --tw-blur: blur(8px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.blur-\\[var\\(--blur\\)\\] {\n  --tw-blur: blur(var(--blur));\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.blur-sm {\n  --tw-blur: blur(4px);\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.drop-shadow {\n  --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow(0 1px 1px rgb(0 0 0 / 0.06));\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.drop-shadow-lg {\n  --tw-drop-shadow: drop-shadow(0 10px 8px rgb(0 0 0 / 0.04)) drop-shadow(0 4px 3px rgb(0 0 0 / 0.1));\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.drop-shadow-md {\n  --tw-drop-shadow: drop-shadow(0 4px 3px rgb(0 0 0 / 0.07)) drop-shadow(0 2px 2px rgb(0 0 0 / 0.06));\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.drop-shadow-sm {\n  --tw-drop-shadow: drop-shadow(0 1px 1px rgb(0 0 0 / 0.05));\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.filter {\n  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);\n}\n.backdrop-blur-lg {\n  --tw-backdrop-blur: blur(16px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.backdrop-blur-md {\n  --tw-backdrop-blur: blur(12px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.backdrop-blur-sm {\n  --tw-backdrop-blur: blur(4px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.backdrop-blur-xl {\n  --tw-backdrop-blur: blur(24px);\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.backdrop-filter {\n  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);\n}\n.transition {\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-all {\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-colors {\n  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-opacity {\n  transition-property: opacity;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-shadow {\n  transition-property: box-shadow;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.transition-transform {\n  transition-property: transform;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n.delay-100 {\n  transition-delay: 100ms;\n}\n.duration-200 {\n  transition-duration: 200ms;\n}\n.duration-300 {\n  transition-duration: 300ms;\n}\n.duration-500 {\n  transition-duration: 500ms;\n}\n.ease-in-out {\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\n.ease-out {\n  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);\n}\n.will-change-transform {\n  will-change: transform;\n}\n@keyframes enter {\n\n  from {\n    opacity: var(--tw-enter-opacity, 1);\n    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));\n  }\n}\n@keyframes exit {\n\n  to {\n    opacity: var(--tw-exit-opacity, 1);\n    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));\n  }\n}\n.animate-in {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\n.slide-in-from-right-full {\n  --tw-enter-translate-x: 100%;\n}\n.duration-200 {\n  animation-duration: 200ms;\n}\n.duration-300 {\n  animation-duration: 300ms;\n}\n.duration-500 {\n  animation-duration: 500ms;\n}\n.delay-100 {\n  animation-delay: 100ms;\n}\n.ease-in-out {\n  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\n.ease-out {\n  animation-timing-function: cubic-bezier(0, 0, 0.2, 1);\n}\n.running {\n  animation-play-state: running;\n}\n.\\[--base-color\\:var\\(--global-text-shimmer-base\\)\\] {\n  --base-color: var(--global-text-shimmer-base);\n}\n.\\[--base-gradient-color\\:var\\(--global-text-shimmer-highlight\\)\\] {\n  --base-gradient-color: var(--global-text-shimmer-highlight);\n}\n.\\[--bg\\:linear-gradient\\(90deg\\2c \\#0000_calc\\(50\\%-var\\(--spread\\)\\)\\2c var\\(--base-gradient-color\\)\\2c \\#0000_calc\\(50\\%\\+var\\(--spread\\)\\)\\)\\] {\n  --bg: linear-gradient(90deg,#0000 calc(50% - var(--spread)),var(--base-gradient-color),#0000 calc(50% + var(--spread)));\n}\n.\\[aspect-ratio\\:1\\] {\n  aspect-ratio: 1;\n}\n.\\[background-repeat\\:no-repeat\\2c padding-box\\] {\n  background-repeat: no-repeat,padding-box;\n}\n.\\[background\\:conic-gradient\\(from_calc\\(270deg-\\(var\\(--spread\\)\\*0\\.5\\)\\)\\2c transparent_0\\2c var\\(--shimmer-color\\)_var\\(--spread\\)\\2c transparent_var\\(--spread\\)\\)\\] {\n  background: conic-gradient(from calc(270deg - (var(--spread) * 0.5)),transparent 0,var(--shimmer-color) var(--spread),transparent var(--spread));\n}\n.\\[background\\:var\\(--bg\\)\\] {\n  background: var(--bg);\n}\n.\\[border-radius\\:0\\] {\n  border-radius: 0;\n}\n.\\[border-radius\\:var\\(--radius\\)\\] {\n  border-radius: var(--radius);\n}\n.\\[container-type\\:size\\] {\n  container-type: size;\n}\n.\\[inset\\:var\\(--cut\\)\\] {\n  inset: var(--cut);\n}\n.\\[mask-image\\:radial-gradient\\(ellipse_at_100\\%_0\\%\\2c black_10\\%\\2c transparent_70\\%\\)\\] {\n  mask-image: radial-gradient(ellipse at 100% 0%,black 10%,transparent 70%);\n}\n.\\[mask\\:linear-gradient\\(var\\(--mantle-900\\)\\2c _transparent_50\\%\\)\\] {\n  mask: linear-gradient(var(--mantle-900), transparent 50%);\n}\n.\\[mask\\:none\\] {\n  mask: none;\n}\n.\\[translate\\:0_0\\] {\n  translate: 0 0;\n}\n\n/*\n================================================================================\n| VIERLA DESIGN SYSTEM - GLOBAL STYLES (REDESIGNED)\n|\n| This file contains the complete design system for the Vierla application.\n| It's organized into sections for easy maintenance and customization:\n|\n| 1. MASTER CONTROL PANEL - Core brand colors and theme variables\n| 2. PAGE-SPECIFIC CONTROLS - Individual page customization\n| 3. GLOBAL COMPONENTS - Shared component styling\n| 4. SHADCN UI THEME - UI library integration\n| 5. BASE ELEMENT STYLES - HTML element defaults\n| 6. PAGE & COMPONENT OVERRIDES - Specific styling rules\n| 7. UTILITIES & ANIMATIONS - Helper classes and keyframes\n|\n| Each component can be individually customized per page while maintaining\n| consistency through the master control variables.\n================================================================================\n*/\n\n/*\n========================================\n| PAGE & COMPONENT-SPECIFIC CLASS OVERRIDES\n========================================\n*/\n\n/* ----- Home Page ----- */\n.page-home .shiny-button {\n  background-color: var(--home-shiny-button-bg);\n  color: var(--home-shiny-button-text);\n  --shimmer-color: var(--home-shiny-button-shimmer);\n}\n\n/* Home Page Specific Shiny Button Variants */\n.page-home .shiny-button.hero-cta {\n  background-color: var(--home-shiny-button-hero-bg);\n  color: var(--home-shiny-button-hero-text);\n  --shimmer-color: var(--home-shiny-button-hero-shimmer);\n}\n\n.page-home .shiny-button.section-cta {\n  background-color: var(--home-shiny-button-cta-bg);\n  color: var(--home-shiny-button-cta-text);\n  --shimmer-color: var(--home-shiny-button-cta-shimmer);\n}\n\n.page-home .golden-glowing-card {\n  background: var(--home-golden-card-bg);\n  border-color: var(--home-golden-card-border);\n  --glow-color: var(--home-golden-card-glow);\n}\n\n.page-home .text-shimmer {\n  --base-color: var(--home-text-shimmer-base);\n  --base-gradient-color: var(--home-text-shimmer-highlight);\n}\n\n.page-home .word-pull-up {\n  color: var(--home-word-pullup-color);\n}\n\n.page-home .marquee-animation {\n  color: var(--home-marquee-text);\n  background: var(--home-marquee-bg);\n}\n\n/* ----- Navigation Styles ----- */\n.nav-link {\n  color: var(--text-primary);\n  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);\n  transition: all 0.3s ease;\n  padding: 0.375rem 0.75rem;\n  border-radius: 0.375rem;\n  font-weight: 500;\n}\n\n.nav-link:hover {\n  background: var(--master-header-nav-hover-bg);\n  color: var(--text-primary);\n}\n\n.nav-link-active {\n  background: var(--master-header-nav-active-bg);\n  color: var(--text-primary);\n  box-shadow: var(--master-header-nav-active-glow);\n}\n\n/* ----- Form Input Styles ----- */\n.theme-input {\n  background-color: var(--input-bg) !important;\n  border-color: var(--input-border) !important;\n  color: var(--text-primary) !important;\n  transition: all 0.3s ease;\n}\n\n.theme-input::placeholder {\n  color: var(--input-placeholder) !important;\n  opacity: 1;\n}\n\n.theme-input:focus {\n  border-color: var(--icon-accent) !important;\n  box-shadow: 0 0 0 2px var(--accent-hover-overlay) !important;\n}\n\n/* ----- Limelight Navigation Effect ----- */\n.limelight-nav {\n  position: relative;\n  overflow: hidden;\n}\n\n.limelight-nav::before {\n  content: '';\n  position: absolute;\n  top: 50%;\n  left: 50%;\n  width: 0;\n  height: 0;\n  background: radial-gradient(circle, #D97706 0%, transparent 70%);\n  transform: translate(-50%, -50%);\n  transition: all 0.3s ease;\n  z-index: -1;\n  border-radius: 50%;\n  opacity: 0;\n}\n\n.limelight-nav:hover::before,\n.limelight-nav.nav-link-active::before {\n  width: 120px;\n  height: 120px;\n  opacity: 0.3;\n}\n\n.limelight-nav:hover,\n.limelight-nav.nav-link-active {\n  color: #CA8A04;\n  text-shadow: 0 0 8px rgba(202, 138, 4, 0.5);\n}\n\n/*\n========================================\n| UTILITIES & ANIMATIONS\n========================================\n*/\n\n\n\n@keyframes beam {\n  0% {\n    transform: translateY(-100%);\n  }\n  100% {\n    transform: translateY(100%);\n  }\n}\n\n/*\n========================================\n| FIX FOR BACKGROUND ISSUE\n| This section is intentionally left blank. The problematic\n| `!important` override has been removed.\n========================================\n*/\n\n/*\n========================================\n| 🔮 GLASSMORPHISM UTILITY CLASSES\n| Consistent glassmorphism effects for UI chrome elements\n========================================\n*/\n\n.glassmorphism-light {\n  background: rgba(245, 250, 247, 0.1);\n  backdrop-filter: blur(16px);\n  -webkit-backdrop-filter: blur(16px);\n  border: 1px solid rgba(124, 154, 133, 0.2);\n  box-shadow: 0 8px 32px 0 rgba(44, 49, 55, 0.37);\n}\n\n.glassmorphism-medium {\n  background: rgba(44, 49, 55, 0.7);\n  backdrop-filter: blur(20px);\n  -webkit-backdrop-filter: blur(20px);\n  border: 1px solid rgba(124, 154, 133, 0.15);\n  box-shadow: 0 8px 32px 0 rgba(44, 49, 55, 0.5);\n}\n\n.glassmorphism-heavy {\n  background: rgba(44, 49, 55, 0.8);\n  backdrop-filter: blur(24px);\n  -webkit-backdrop-filter: blur(24px);\n  border: 1px solid rgba(124, 154, 133, 0.1);\n  box-shadow: 0 12px 40px 0 rgba(44, 49, 55, 0.6);\n}\n\n.glassmorphism-card {\n  background: rgba(51, 51, 51, 0.2);\n  backdrop-filter: blur(12px);\n  -webkit-backdrop-filter: blur(12px);\n  border: 1px solid rgba(124, 154, 133, 0.1);\n  box-shadow: 0 4px 24px 0 rgba(44, 49, 55, 0.3);\n}\n\n/* Fallback for browsers that don't support backdrop-filter */\n@supports not (backdrop-filter: blur(1px)) {\n  .glassmorphism-light,\n  .glassmorphism-medium,\n  .glassmorphism-heavy,\n  .glassmorphism-card {\n    background: rgba(44, 49, 55, 0.9);\n  }\n}\n\n/*\n========================================\n| 📱 RESPONSIVE DESIGN & MOBILE OPTIMIZATIONS\n========================================\n*/\n\n/* Mobile-First Responsive Typography */\n@media screen and (max-width: 767px) {\n  :root {\n    /* Override typography scale for mobile */\n    --font-size-h1: var(--font-size-h1-mobile);\n    --font-size-h2: var(--font-size-h2-mobile);\n    --font-size-h3: var(--font-size-h3-mobile);\n    --font-size-h4: var(--font-size-h4-mobile);\n    --font-size-h5: var(--font-size-h5-mobile);\n    --font-size-body: var(--font-size-body-mobile);\n    --font-size-small: var(--font-size-small-mobile);\n\n    /* Mobile Header Scaling - Increased by 11.5% from previous 33% reduction */\n    --header-height: 3.713rem;       /* Increased from 3.33rem by 11.5% (59.41px) */\n    --header-logo-size: 2.23rem;     /* Increased from 2rem by 11.5% (35.68px) */\n    --header-nav-font-size: 0.836rem; /* Increased from 0.75rem by 11.5% (13.38px) */\n    --header-button-height: 2.23rem; /* Increased from 2rem by 11.5% (35.68px) */\n    --header-padding: 0.747rem;      /* Increased from 0.67rem by 11.5% (11.95px) */\n  }\n}\n\n/* Dark Mode Theme Overrides - STRICT COLOR SEPARATION */\n.dark {\n  /* ========================================\n   * CRITICAL: OVERRIDE ALL GREEN/SAGE COLORS IN DARK MODE\n   * Replace with Gold/Champagne colors per guidelines\n   ======================================== */\n\n  /* Override Sage Colors to Gold in Dark Mode */\n  --tw-text-sage: #B8956A !important;           /* Dark Mode: Sage text becomes Gold */\n  --tw-bg-sage: #B8956A !important;             /* Dark Mode: Sage backgrounds become Gold */\n  --tw-border-sage: #B8956A !important;         /* Dark Mode: Sage borders become Gold */\n  --tw-from-sage: #B8956A !important;           /* Dark Mode: Sage gradients become Gold */\n  --tw-to-sage: #A9A299 !important;             /* Dark Mode: Sage gradient ends become Champagne */\n\n  /* Header/Navbar Dark Mode Colors - Modern Luxury Theme */\n  --master-header-background: #2D2A26;                           /* Dark mode: Deep Charcoal (NO GREEN) */\n  --master-header-backdrop-blur: 50%;                            /* 50% backdrop blur for proper glassmorphism */\n  --master-header-border: rgba(169, 162, 153, 0.3);              /* Dark mode: Muted Gold/Champagne border (NO GREEN) */\n  --master-header-logo-bg: linear-gradient(135deg, #B8956A, #A9A299); /* Dark mode: Gold to Champagne gradient */\n  --master-header-logo-border: rgba(184, 149, 106, 0.3);         /* Dark mode: Vierla Gold border */\n  --master-header-logo-icon: #2D2A26;                            /* Dark mode: Deep Charcoal icon for contrast */\n  --master-header-brand-text: #F4F1E8;                           /* Dark mode: Warm Cream text (NO GREEN) */\n\n  /* Footer Dark Mode Colors */\n  --global-footer-bg: rgba(46, 51, 47, 0.9);                     /* Dark mode: dark charcoal footer */\n  --global-footer-border: rgba(95, 109, 97, 0.3);               /* Dark mode: dark sage border */\n  --global-footer-text: #e1e6e1;                                 /* Dark mode: light sage text */\n  --global-footer-link: #f6f7f6;                                 /* Dark mode: very light sage links */\n  --global-footer-link-hover: #c3ccc3;                           /* Dark mode: medium-light sage hover */\n  --global-cookie-bg: #181b19;                                   /* Dark mode: very dark charcoal popup */\n  --global-cookie-border: #5f6d61;                               /* Dark mode: dark sage border */\n  --global-cookie-text: #f6f7f6;                                 /* Dark mode: very light sage text */\n  --global-cookie-button-bg: #79887a;                            /* Dark mode: medium-dark sage button */\n\n  /* ======================================== */\n  /* 🔮 UI CHROME MASTER CONTROLS - v1.5.6    */\n  /* ======================================== */\n\n  /* -- Header & Footer (Dark Mode) - Modern Luxury Theme -- */\n  --header-footer-bg: rgba(45, 42, 38, 0.80); /* 80% opacity Deep Charcoal for semi-transparent effect */\n  --header-footer-backdrop-blur: blur(16px);\n  --header-footer-border: rgba(169, 162, 153, 0.3); /* Muted Gold/Champagne border */\n\n  /* -- Theme-Aware Icons (Dark Mode) -- */\n  --icon-primary: #f6f7f6; /* Very light sage */\n  --icon-secondary: #e1e6e1; /* Light sage */\n  --icon-accent: #c3ccc3; /* Medium-light sage */\n  --icon-on-accent: #181b19; /* Very dark charcoal for use on colored buttons */\n\n  /* ----- 🎨 THEME-AWARE ICON COLORS - Dark Mode ----- */\n  --icon-muted: #8b9a8c;                                         /* Dark mode: medium sage for muted icons */\n  --icon-on-dark: #2e332f;                                       /* Dark mode: dark charcoal for icons on light backgrounds */\n\n  /* ----- 🎴 BENTO GRID CARD CONTROLS - Dark Mode ----- */\n  --card-bg: rgba(24, 27, 25, 0.95);                            /* Dark mode: very dark charcoal card background */\n  --shadow-card: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);\n  --accent-hover-overlay: rgba(184, 149, 106, 0.08);            /* Dark mode: slightly stronger gold overlay */\n  --accent-bg: rgba(184, 149, 106, 0.20);                       /* Dark mode: accent background for icons */\n  --border-accent: rgba(184, 149, 106, 0.4);                    /* Dark mode: accent border color */\n\n  /* ----- 📝 FORM INPUT CONTROLS - Dark Mode ----- */\n  --input-bg: rgba(24, 27, 25, 0.8);                            /* Dark mode: input background */\n  --input-border: rgba(95, 109, 97, 0.3);                       /* Dark mode: input border */\n  --input-placeholder: rgba(246, 247, 246, 0.5);                /* Dark mode: placeholder text */\n\n  /* ----- 📝 WCAG-COMPLIANT TEXT COLORS - Dark Mode ----- */\n  /* Primary Body Text */\n  --text-primary: #F5FAF7;                                       /* Dark mode: Light Off-White for main text */\n  --text-secondary: #F0E6D9;                                     /* Dark mode: Beige for secondary/muted text */\n  --text-headlines: #F5FAF7;                                     /* Dark mode: Light Off-White for headlines */\n  --text-links: #B8956A;                                         /* Dark mode: Gold for links */\n  --text-links-hover: #A6845C;                                   /* Dark mode: Darker gold for link hover */\n\n  /* ----- 🌙 AURORA BACKGROUND - Dark Mode Override - NO GREEN COLORS ----- */\n  --aurora-bg: #2D2A26;                                          /* Deep Charcoal - matches Modern Luxury theme */\n  --aurora-bg-dark: #333333;                                     /* Light Charcoal for variation */\n  --aurora-stripe-light: rgba(244, 241, 232, 0.02);             /* Very subtle warm cream stripes */\n  --aurora-stripe-dark: rgba(184, 149, 106, 0.15);              /* Vierla Gold for contrast (NO GREEN) */\n  --aurora-flow-1: rgba(184, 149, 106, 0.12);                   /* Vierla Gold - visible but subtle (NO GREEN) */\n  --aurora-flow-2: rgba(169, 162, 153, 0.08);                   /* Muted Gold/Champagne (NO GREEN) */\n  --aurora-flow-3: rgba(75, 61, 107, 0.10);                     /* Deep Plum/Violet for luxury (NO GREEN) */\n  --aurora-flow-4: rgba(45, 42, 38, 0.18);                      /* Deep charcoal variation */\n  --aurora-flow-5: rgba(184, 149, 106, 0.06);                   /* Vierla Gold accent (NO GREEN) */\n}\n\n/* ========================================\n * CRITICAL: DARK MODE COLOR OVERRIDES\n * Override ALL green/sage colors with gold in dark mode\n ======================================== */\n.dark .text-sage,\n.dark .text-sage-500,\n.dark .text-sage-400,\n.dark .text-sage-600 {\n  color: #B8956A !important; /* Vierla Gold instead of sage */\n}\n\n.dark .bg-sage,\n.dark .bg-sage-500,\n.dark .bg-sage-400,\n.dark .bg-sage-600,\n.dark .from-sage,\n.dark .to-sage {\n  background-color: #B8956A !important; /* Vierla Gold instead of sage */\n}\n\n.dark .border-sage,\n.dark .border-sage-500,\n.dark .border-sage-400,\n.dark .border-sage-600 {\n  border-color: #B8956A !important; /* Vierla Gold instead of sage */\n}\n\n.dark .from-sage\\/20 {\n  --tw-gradient-from: rgba(184, 149, 106, 0.2) !important; /* Gold instead of sage */\n}\n\n.dark .to-sage\\/10 {\n  --tw-gradient-to: rgba(184, 149, 106, 0.1) !important; /* Gold instead of sage */\n}\n\n.dark .border-sage\\/30 {\n  border-color: rgba(184, 149, 106, 0.3) !important; /* Gold instead of sage */\n}\n\n.dark .bg-sage\\/20 {\n  background-color: rgba(184, 149, 106, 0.2) !important; /* Gold instead of sage */\n}\n\n/* ========================================\n * CRITICAL: LIGHT MODE COLOR OVERRIDES\n * Override ALL gold colors with green/forest in light mode\n ======================================== */\n:root:not(.dark) .text-gold,\n:root:not(.dark) .text-muted-gold,\n:root:not(.dark) .text-brand-gold {\n  color: #364035 !important; /* Vierla Forest instead of gold */\n}\n\n:root:not(.dark) .bg-gold,\n:root:not(.dark) .bg-muted-gold,\n:root:not(.dark) .bg-brand-gold,\n:root:not(.dark) .from-gold,\n:root:not(.dark) .to-gold {\n  background-color: #364035 !important; /* Vierla Forest instead of gold */\n}\n\n:root:not(.dark) .border-gold,\n:root:not(.dark) .border-muted-gold,\n:root:not(.dark) .border-brand-gold {\n  border-color: #364035 !important; /* Vierla Forest instead of gold */\n}\n\n/* Override any gold opacity variants in light mode */\n:root:not(.dark) .bg-gold\\/20,\n:root:not(.dark) .bg-muted-gold\\/20 {\n  background-color: rgba(54, 64, 53, 0.2) !important; /* Vierla Forest instead of gold */\n}\n\n:root:not(.dark) .border-gold\\/30,\n:root:not(.dark) .border-muted-gold\\/30 {\n  border-color: rgba(54, 64, 53, 0.3) !important; /* Vierla Forest instead of gold */\n}\n\n/* ========================================\n * THEME-AWARE PRIMARY COLOR SYSTEM\n * Light Mode: Vierla Forest, Dark Mode: Vierla Gold\n ======================================== */\n:root:not(.dark) .text-theme-primary {\n  color: #364035 !important; /* Light Mode: Vierla Forest */\n}\n\n:root:not(.dark) .bg-theme-primary {\n  background-color: #364035 !important; /* Light Mode: Vierla Forest */\n}\n\n:root:not(.dark) .bg-theme-primary\\/10 {\n  background-color: rgba(54, 64, 53, 0.1) !important; /* Light Mode: Vierla Forest with opacity */\n}\n\n:root:not(.dark) .bg-theme-primary\\/5 {\n  background-color: rgba(54, 64, 53, 0.05) !important; /* Light Mode: Vierla Forest with opacity */\n}\n\n.dark .text-theme-primary {\n  color: #B8956A !important; /* Dark Mode: Vierla Gold */\n}\n\n.dark .bg-theme-primary {\n  background-color: #B8956A !important; /* Dark Mode: Vierla Gold */\n}\n\n.dark .bg-theme-primary\\/10 {\n  background-color: rgba(184, 149, 106, 0.1) !important; /* Dark Mode: Vierla Gold with opacity */\n}\n\n.dark .bg-theme-primary\\/5 {\n  background-color: rgba(184, 149, 106, 0.05) !important; /* Dark Mode: Vierla Gold with opacity */\n}\n\n/* Mobile-specific improvements */\n@media screen and (max-width: 767px) {\n  body {\n    overflow-x: hidden;\n    -webkit-text-size-adjust: 100%;\n    text-size-adjust: 100%;\n  }\n\n  /* Improve touch targets for mobile */\n  button, a, [role=\"button\"] {\n    min-height: 44px;\n    min-width: 44px;\n  }\n\n  /* Mobile-friendly spacing */\n  .container {\n    padding-left: 1rem;\n    padding-right: 1rem;\n  }\n\n  /* Mobile navigation improvements */\n  .nav-link {\n    padding: 0.75rem 1rem;\n    font-size: 1.125rem;\n  }\n\n  /* Mobile card spacing */\n  .golden-glowing-card-container {\n    margin: 0.75rem;\n  }\n\n  /* Mobile hero text adjustments */\n  .hero-title {\n    font-size: 2.5rem !important;\n    line-height: 1.1;\n  }\n\n  .hero-subtitle {\n    font-size: 1.125rem !important;\n    line-height: 1.5;\n  }\n}\n\n/* Tablet Responsive Design */\n@media screen and (min-width: 768px) and (max-width: 1023px) {\n  .container {\n    padding-left: 2rem;\n    padding-right: 2rem;\n  }\n\n  /* Tablet-specific grid adjustments */\n  .grid-responsive {\n    grid-template-columns: repeat(2, 1fr);\n  }\n}\n\n/* Desktop Responsive Design */\n@media screen and (min-width: 1024px) {\n  .container {\n    padding-left: 3rem;\n    padding-right: 3rem;\n  }\n\n  /* Desktop-specific grid adjustments */\n  .grid-responsive {\n    grid-template-columns: repeat(3, 1fr);\n  }\n}\n\n/*\n========================================\n| 🤖 ANDROID PERFORMANCE OPTIMIZATIONS\n| Fixes for flickering and performance issues on Android devices\n========================================\n*/\n\n/* Hardware acceleration for better performance */\n.aurora-background,\n.backdrop-blur-lg,\n.backdrop-blur-md,\n.backdrop-blur-sm {\n  transform: translateZ(0);\n  -webkit-transform: translateZ(0);\n  will-change: transform;\n  -webkit-backface-visibility: hidden;\n  backface-visibility: hidden;\n  -webkit-perspective: 1000px;\n  perspective: 1000px;\n}\n\n/* Reduce backdrop-filter complexity on Android */\n@media screen and (max-width: 768px) {\n  .backdrop-blur-lg {\n    backdrop-filter: blur(8px);\n    -webkit-backdrop-filter: blur(8px);\n  }\n\n  .backdrop-blur-md {\n    backdrop-filter: blur(6px);\n    -webkit-backdrop-filter: blur(6px);\n  }\n\n  .backdrop-blur-sm {\n    backdrop-filter: blur(4px);\n    -webkit-backdrop-filter: blur(4px);\n  }\n}\n\n/* Prevent flickering during animations */\n* {\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  text-rendering: optimizeLegibility;\n}\n\n/* Optimize aurora background for mobile */\n@media screen and (max-width: 768px) {\n  .aurora-background {\n    opacity: 0.7;\n    filter: blur(8px);\n  }\n\n  .aurora-background::after {\n    animation-duration: 120s; /* Slower animation for better performance */\n  }\n}\n\n/*\n========================================\n| 📱 MOBILE & SAFARI SPECIFIC FIXES\n========================================\n*/\n@media screen and (orientation: landscape) {\n  html, body {\n    min-height: 100lvh !important;\n  }\n}\n\n@supports (padding: max(0px)) {\n  html {\n    padding-top: max(var(--safe-area-inset-top), 0px);\n    padding-left: max(var(--safe-area-inset-left), 0px);\n    padding-right: max(var(--safe-area-inset-right), 0px);\n    padding-bottom: max(var(--safe-area-inset-bottom), 0px);\n  }\n}\n\n@supports (-webkit-touch-callout: none) {\n  html, body {\n    height: -webkit-fill-available;\n  }\n}\n\n.\\*\\:me-10 > * {\n  margin-inline-end: var(--space-10);\n}\n\n.\\*\\:block > * {\n  display: block;\n}\n\n.file\\:border-0::file-selector-button {\n  border-width: 0px;\n}\n\n.file\\:bg-transparent::file-selector-button {\n  background-color: transparent;\n}\n\n.file\\:text-sm::file-selector-button {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\n\n.file\\:font-medium::file-selector-button {\n  font-weight: 500;\n}\n\n.placeholder\\:text-\\[\\#F5F5DC\\]::placeholder {\n  --tw-text-opacity: 1;\n  color: rgb(245 245 220 / var(--tw-text-opacity, 1));\n}\n\n.placeholder\\:text-\\[var\\(--master-text-secondary-light\\)\\]::placeholder {\n  color: var(--master-text-secondary-light);\n}\n\n.before\\:absolute::before {\n  content: var(--tw-content);\n  position: absolute;\n}\n\n.before\\:aspect-square::before {\n  content: var(--tw-content);\n  aspect-ratio: 1 / 1;\n}\n\n.before\\:w-\\[200\\%\\]::before {\n  content: var(--tw-content);\n  width: 200%;\n}\n\n.before\\:rotate-\\[-90deg\\]::before {\n  content: var(--tw-content);\n  --tw-rotate: -90deg;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.before\\:bg-\\[conic-gradient\\(from_0deg\\2c transparent_0_340deg\\2c \\#D97706_360deg\\)\\]::before {\n  content: var(--tw-content);\n  background-image: conic-gradient(from 0deg,transparent 0 340deg,#D97706 360deg);\n}\n\n.before\\:content-\\[\\'\\'\\]::before {\n  --tw-content: '';\n  content: var(--tw-content);\n}\n\n.before\\:\\[inset\\:0_auto_auto_50\\%\\]::before {\n  content: var(--tw-content);\n  inset: 0 auto auto 50%;\n}\n\n.before\\:\\[translate\\:-50\\%_-15\\%\\]::before {\n  content: var(--tw-content);\n  translate: -50% -15%;\n}\n\n.after\\:absolute::after {\n  content: var(--tw-content);\n  position: absolute;\n}\n\n.after\\:inset-0::after {\n  content: var(--tw-content);\n  inset: 0px;\n}\n\n.after\\:inset-\\[calc\\(-1\\*var\\(--glowingeffect-border-width\\)\\)\\]::after {\n  content: var(--tw-content);\n  inset: calc(-1 * var(--glowingeffect-border-width));\n}\n\n@keyframes aurora {\n\n  0% {\n    content: var(--tw-content);\n    background-position: 50% 50%, 50% 50%;\n  }\n\n  25% {\n    content: var(--tw-content);\n    background-position: 200% 25%, 200% 75%;\n  }\n\n  50% {\n    content: var(--tw-content);\n    background-position: 350% 50%, 350% 50%;\n  }\n\n  75% {\n    content: var(--tw-content);\n    background-position: 100% 75%, 100% 25%;\n  }\n\n  100% {\n    content: var(--tw-content);\n    background-position: 50% 50%, 50% 50%;\n  }\n}\n\n.after\\:animate-aurora::after {\n  content: var(--tw-content);\n  animation: aurora 25s ease-in-out infinite;\n}\n\n.after\\:rounded-\\[inherit\\]::after {\n  content: var(--tw-content);\n  border-radius: inherit;\n}\n\n.after\\:opacity-\\[var\\(--active\\)\\]::after {\n  content: var(--tw-content);\n  opacity: var(--active);\n}\n\n.after\\:mix-blend-difference::after {\n  content: var(--tw-content);\n  mix-blend-mode: difference;\n}\n\n.after\\:transition-opacity::after {\n  content: var(--tw-content);\n  transition-property: opacity;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n\n.after\\:duration-300::after {\n  content: var(--tw-content);\n  transition-duration: 300ms;\n}\n\n.after\\:content-\\[\\\"\\\"\\]::after {\n  --tw-content: \"\";\n  content: var(--tw-content);\n}\n\n.after\\:duration-300::after {\n  content: var(--tw-content);\n  animation-duration: 300ms;\n}\n\n.after\\:\\[background-attachment\\:fixed\\]::after {\n  content: var(--tw-content);\n  background-attachment: fixed;\n}\n\n.after\\:\\[background-size\\:250\\%\\2c _150\\%\\]::after {\n  content: var(--tw-content);\n  background-size: 250%, 150%;\n}\n\n.after\\:\\[background\\:var\\(--gradient\\)\\]::after {\n  content: var(--tw-content);\n  background: var(--gradient);\n}\n\n.after\\:\\[border\\:var\\(--glowingeffect-border-width\\)_solid_transparent\\]::after {\n  content: var(--tw-content);\n  border: var(--glowingeffect-border-width) solid transparent;\n}\n\n.after\\:\\[mask-clip\\:padding-box\\2c border-box\\]::after {\n  content: var(--tw-content);\n  mask-clip: padding-box,border-box;\n}\n\n.after\\:\\[mask-composite\\:intersect\\]::after {\n  content: var(--tw-content);\n  mask-composite: intersect;\n}\n\n.after\\:\\[mask-image\\:linear-gradient\\(\\#0000\\2c \\#0000\\)\\2c conic-gradient\\(from_calc\\(\\(var\\(--start\\)-var\\(--spread\\)\\)\\*1deg\\)\\2c \\#00000000_0deg\\2c \\#fff\\2c \\#00000000_calc\\(var\\(--spread\\)\\*2deg\\)\\)\\]::after {\n  content: var(--tw-content);\n  mask-image: linear-gradient(#0000,#0000),conic-gradient(from calc((var(--start) - var(--spread)) * 1deg),#00000000 0deg,#fff,#00000000 calc(var(--spread) * 2deg));\n}\n\n.hover\\:scale-105:hover {\n  --tw-scale-x: 1.05;\n  --tw-scale-y: 1.05;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.hover\\:scale-\\[1\\.02\\]:hover {\n  --tw-scale-x: 1.02;\n  --tw-scale-y: 1.02;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.hover\\:border-gold:hover {\n  --tw-border-opacity: 1;\n  border-color: rgb(184 149 106 / var(--tw-border-opacity, 1));\n}\n\n.hover\\:border-sage\\/50:hover {\n  border-color: rgb(139 154 140 / 0.5);\n}\n\n.hover\\:bg-\\[var\\(--master-action-hover\\)\\]:hover {\n  background-color: var(--master-action-hover);\n}\n\n.hover\\:bg-accent:hover {\n  background-color: hsl(var(--accent));\n}\n\n.hover\\:bg-brand-sage\\/10:hover {\n  background-color: rgb(139 154 140 / 0.1);\n}\n\n.hover\\:bg-brand-sage\\/20:hover {\n  background-color: rgb(139 154 140 / 0.2);\n}\n\n.hover\\:bg-destructive\\/80:hover {\n  background-color: hsl(var(--destructive) / 0.8);\n}\n\n.hover\\:bg-destructive\\/90:hover {\n  background-color: hsl(var(--destructive) / 0.9);\n}\n\n.hover\\:bg-error\\/20:hover {\n  background-color: rgb(217 119 6 / 0.2);\n}\n\n.hover\\:bg-gold-600:hover {\n  --tw-bg-opacity: 1;\n  background-color: rgb(166 132 92 / var(--tw-bg-opacity, 1));\n}\n\n.hover\\:bg-primary\\/80:hover {\n  background-color: hsl(var(--primary) / 0.8);\n}\n\n.hover\\:bg-secondary\\/80:hover {\n  background-color: hsl(var(--secondary) / 0.8);\n}\n\n.hover\\:bg-theme-primary\\/5:hover {\n  background-color: rgb(54 64 53 / 0.05);\n}\n\n.hover\\:bg-white\\/10:hover {\n  background-color: rgb(255 255 255 / 0.1);\n}\n\n.hover\\:text-accent-foreground:hover {\n  color: hsl(var(--accent-foreground));\n}\n\n.hover\\:text-brand-gold:hover {\n  --tw-text-opacity: 1;\n  color: rgb(184 149 106 / var(--tw-text-opacity, 1));\n}\n\n.hover\\:text-neutral-off-white:hover {\n  --tw-text-opacity: 1;\n  color: rgb(244 241 232 / var(--tw-text-opacity, 1));\n}\n\n.hover\\:text-theme-primary:hover {\n  --tw-text-opacity: 1;\n  color: rgb(54 64 53 / var(--tw-text-opacity, 1));\n}\n\n.hover\\:text-white:hover {\n  --tw-text-opacity: 1;\n  color: rgb(255 255 255 / var(--tw-text-opacity, 1));\n}\n\n.hover\\:underline:hover {\n  text-decoration-line: underline;\n}\n\n.hover\\:opacity-100:hover {\n  opacity: 1;\n}\n\n.hover\\:opacity-80:hover {\n  opacity: 0.8;\n}\n\n.hover\\:shadow:hover {\n  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.hover\\:shadow-2xl:hover {\n  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);\n  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.hover\\:shadow-\\[0_0_20px_rgba\\(202\\2c 138\\2c 4\\2c 0\\.3\\)\\]:hover {\n  --tw-shadow: 0 0 20px rgba(202,138,4,0.3);\n  --tw-shadow-colored: 0 0 20px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.hover\\:shadow-lg:hover {\n  --tw-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.focus\\:outline-none:focus {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n\n.focus\\:ring-2:focus {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n\n.focus\\:ring-error\\/30:focus {\n  --tw-ring-color: rgb(217 119 6 / 0.3);\n}\n\n.focus\\:ring-ring:focus {\n  --tw-ring-color: hsl(var(--ring));\n}\n\n.focus\\:ring-offset-2:focus {\n  --tw-ring-offset-width: 2px;\n}\n\n.focus-visible\\:border-\\[var\\(--master-input-border-focus\\)\\]:focus-visible {\n  border-color: var(--master-input-border-focus);\n}\n\n.focus-visible\\:outline-none:focus-visible {\n  outline: 2px solid transparent;\n  outline-offset: 2px;\n}\n\n.focus-visible\\:ring-2:focus-visible {\n  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);\n  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);\n}\n\n.focus-visible\\:ring-\\[var\\(--master-input-border-focus\\)\\]:focus-visible {\n  --tw-ring-color: var(--master-input-border-focus);\n}\n\n.focus-visible\\:ring-ring:focus-visible {\n  --tw-ring-color: hsl(var(--ring));\n}\n\n.focus-visible\\:ring-offset-2:focus-visible {\n  --tw-ring-offset-width: 2px;\n}\n\n.active\\:translate-y-px:active {\n  --tw-translate-y: 1px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.disabled\\:pointer-events-none:disabled {\n  pointer-events: none;\n}\n\n.disabled\\:cursor-not-allowed:disabled {\n  cursor: not-allowed;\n}\n\n.disabled\\:opacity-50:disabled {\n  opacity: 0.5;\n}\n\n.group:hover .group-hover\\:left-\\[0\\%\\] {\n  left: 0%;\n}\n\n.group:hover .group-hover\\:top-\\[0\\%\\] {\n  top: 0%;\n}\n\n.group:hover .group-hover\\:h-full {\n  height: 100%;\n}\n\n.group:hover .group-hover\\:w-\\[calc\\(100\\%-0\\.5rem\\)\\] {\n  width: calc(100% - 0.5rem);\n}\n\n.group:hover .group-hover\\:w-full {\n  width: 100%;\n}\n\n.group:hover .group-hover\\:-translate-x-1 {\n  --tw-translate-x: calc(var(--space-1) * -1);\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.group:hover .group-hover\\:-translate-y-2 {\n  --tw-translate-y: calc(var(--space-2) * -1);\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.group:hover .group-hover\\:translate-x-1 {\n  --tw-translate-x: var(--space-1);\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.group:hover .group-hover\\:translate-x-12 {\n  --tw-translate-x: var(--space-12);\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.group:hover .group-hover\\:translate-y-0 {\n  --tw-translate-y: 0px;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.group:hover .group-hover\\:scale-110 {\n  --tw-scale-x: 1.1;\n  --tw-scale-y: 1.1;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.group:hover .group-hover\\:scale-\\[1\\.8\\] {\n  --tw-scale-x: 1.8;\n  --tw-scale-y: 1.8;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.group:hover .group-hover\\:transform {\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.group:hover .group-hover\\:bg-gold {\n  --tw-bg-opacity: 1;\n  background-color: rgb(184 149 106 / var(--tw-bg-opacity, 1));\n}\n\n.group\\/card:hover .group-hover\\/card\\:opacity-100 {\n  opacity: 1;\n}\n\n.group:hover .group-hover\\:opacity-0 {\n  opacity: 0;\n}\n\n.group:hover .group-hover\\:opacity-100 {\n  opacity: 1;\n}\n\n.group:hover .group-hover\\:shadow-\\[inset_0_-6px_10px_\\#ffffff3f\\] {\n  --tw-shadow: inset 0 -6px 10px #ffffff3f;\n  --tw-shadow-colored: inset 0 -6px 10px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.group:active .group-active\\:scale-95 {\n  --tw-scale-x: .95;\n  --tw-scale-y: .95;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.group:active .group-active\\:shadow-\\[inset_0_-10px_10px_\\#ffffff3f\\] {\n  --tw-shadow: inset 0 -10px 10px #ffffff3f;\n  --tw-shadow-colored: inset 0 -10px 10px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n.peer:disabled ~ .peer-disabled\\:cursor-not-allowed {\n  cursor: not-allowed;\n}\n\n.peer:disabled ~ .peer-disabled\\:opacity-70 {\n  opacity: 0.7;\n}\n\n.data-\\[state\\=checked\\]\\:bg-primary[data-state=\"checked\"] {\n  background-color: hsl(var(--primary));\n}\n\n.data-\\[state\\=open\\]\\:bg-secondary[data-state=\"open\"] {\n  background-color: hsl(var(--secondary));\n}\n\n.data-\\[state\\=checked\\]\\:text-primary-foreground[data-state=\"checked\"] {\n  color: hsl(var(--primary-foreground));\n}\n\n.data-\\[state\\=closed\\]\\:duration-300[data-state=\"closed\"] {\n  transition-duration: 300ms;\n}\n\n.data-\\[state\\=open\\]\\:duration-500[data-state=\"open\"] {\n  transition-duration: 500ms;\n}\n\n.data-\\[state\\=open\\]\\:animate-in[data-state=\"open\"] {\n  animation-name: enter;\n  animation-duration: 150ms;\n  --tw-enter-opacity: initial;\n  --tw-enter-scale: initial;\n  --tw-enter-rotate: initial;\n  --tw-enter-translate-x: initial;\n  --tw-enter-translate-y: initial;\n}\n\n.data-\\[state\\=closed\\]\\:animate-out[data-state=\"closed\"] {\n  animation-name: exit;\n  animation-duration: 150ms;\n  --tw-exit-opacity: initial;\n  --tw-exit-scale: initial;\n  --tw-exit-rotate: initial;\n  --tw-exit-translate-x: initial;\n  --tw-exit-translate-y: initial;\n}\n\n.data-\\[state\\=closed\\]\\:fade-out-0[data-state=\"closed\"] {\n  --tw-exit-opacity: 0;\n}\n\n.data-\\[state\\=open\\]\\:fade-in-0[data-state=\"open\"] {\n  --tw-enter-opacity: 0;\n}\n\n.data-\\[state\\=closed\\]\\:slide-out-to-bottom[data-state=\"closed\"] {\n  --tw-exit-translate-y: 100%;\n}\n\n.data-\\[state\\=closed\\]\\:slide-out-to-left[data-state=\"closed\"] {\n  --tw-exit-translate-x: -100%;\n}\n\n.data-\\[state\\=closed\\]\\:slide-out-to-right[data-state=\"closed\"] {\n  --tw-exit-translate-x: 100%;\n}\n\n.data-\\[state\\=closed\\]\\:slide-out-to-top[data-state=\"closed\"] {\n  --tw-exit-translate-y: -100%;\n}\n\n.data-\\[state\\=open\\]\\:slide-in-from-bottom[data-state=\"open\"] {\n  --tw-enter-translate-y: 100%;\n}\n\n.data-\\[state\\=open\\]\\:slide-in-from-left[data-state=\"open\"] {\n  --tw-enter-translate-x: -100%;\n}\n\n.data-\\[state\\=open\\]\\:slide-in-from-right[data-state=\"open\"] {\n  --tw-enter-translate-x: 100%;\n}\n\n.data-\\[state\\=open\\]\\:slide-in-from-top[data-state=\"open\"] {\n  --tw-enter-translate-y: -100%;\n}\n\n.data-\\[state\\=closed\\]\\:duration-300[data-state=\"closed\"] {\n  animation-duration: 300ms;\n}\n\n.data-\\[state\\=open\\]\\:duration-500[data-state=\"open\"] {\n  animation-duration: 500ms;\n}\n\n@supports (backdrop-filter: var(--tw)) {\n\n  .supports-\\[backdrop-filter\\]\\:bg-neutral-charcoal-dark\\/40 {\n    background-color: rgb(45 42 38 / 0.4);\n  }\n\n  .supports-\\[backdrop-filter\\]\\:bg-neutral-charcoal-dark\\/70 {\n    background-color: rgb(45 42 38 / 0.7);\n  }\n\n  .supports-\\[backdrop-filter\\]\\:bg-neutral-charcoal-light\\/15 {\n    background-color: rgb(51 51 51 / 0.15);\n  }\n}\n\n.dark\\:bg-\\[var\\(--master-input-bg-dark\\)\\]:is(.dark *) {\n  background-color: var(--master-input-bg-dark);\n}\n\n.dark\\:bg-brand-gold\\/10:is(.dark *) {\n  background-color: rgb(184 149 106 / 0.1);\n}\n\n.dark\\:font-light:is(.dark *) {\n  font-weight: 300;\n}\n\n.dark\\:text-\\[var\\(--master-text-primary-dark\\)\\]:is(.dark *) {\n  color: var(--master-text-primary-dark);\n}\n\n.dark\\:text-black:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(0 0 0 / var(--tw-text-opacity, 1));\n}\n\n.dark\\:text-brand-gold:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(184 149 106 / var(--tw-text-opacity, 1));\n}\n\n.dark\\:\\[--base-color\\:var\\(--global-text-shimmer-base\\)\\]:is(.dark *) {\n  --base-color: var(--global-text-shimmer-base);\n}\n\n.dark\\:\\[--base-gradient-color\\:var\\(--global-text-shimmer-highlight\\)\\]:is(.dark *) {\n  --base-gradient-color: var(--global-text-shimmer-highlight);\n}\n\n.dark\\:placeholder\\:text-\\[var\\(--master-text-secondary-dark\\)\\]:is(.dark *)::placeholder {\n  color: var(--master-text-secondary-dark);\n}\n\n.dark\\:hover\\:bg-brand-gold\\/5:hover:is(.dark *) {\n  background-color: rgb(184 149 106 / 0.05);\n}\n\n.dark\\:hover\\:text-brand-gold:hover:is(.dark *) {\n  --tw-text-opacity: 1;\n  color: rgb(184 149 106 / var(--tw-text-opacity, 1));\n}\n\n.dark\\:hover\\:shadow-\\[0_0_20px_hsl\\(var\\(--primary\\)\\/10\\%\\)\\]:hover:is(.dark *) {\n  --tw-shadow: 0 0 20px hsl(var(--primary)/10%);\n  --tw-shadow-colored: 0 0 20px var(--tw-shadow-color);\n  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);\n}\n\n@media (min-width: 640px) {\n\n  .sm\\:col-span-2 {\n    grid-column: span 2 / span 2;\n  }\n\n  .sm\\:mb-12 {\n    margin-bottom: var(--space-12);\n  }\n\n  .sm\\:mb-16 {\n    margin-bottom: var(--space-16);\n  }\n\n  .sm\\:mb-4 {\n    margin-bottom: var(--space-4);\n  }\n\n  .sm\\:mb-6 {\n    margin-bottom: var(--space-6);\n  }\n\n  .sm\\:mb-8 {\n    margin-bottom: var(--space-8);\n  }\n\n  .sm\\:mt-10 {\n    margin-top: var(--space-10);\n  }\n\n  .sm\\:block {\n    display: block;\n  }\n\n  .sm\\:h-10 {\n    height: var(--space-10);\n  }\n\n  .sm\\:h-16 {\n    height: var(--space-16);\n  }\n\n  .sm\\:h-20 {\n    height: var(--space-20);\n  }\n\n  .sm\\:h-8 {\n    height: var(--space-8);\n  }\n\n  .sm\\:w-10 {\n    width: var(--space-10);\n  }\n\n  .sm\\:w-16 {\n    width: var(--space-16);\n  }\n\n  .sm\\:w-20 {\n    width: var(--space-20);\n  }\n\n  .sm\\:w-8 {\n    width: var(--space-8);\n  }\n\n  .sm\\:w-\\[350px\\] {\n    width: 350px;\n  }\n\n  .sm\\:w-auto {\n    width: auto;\n  }\n\n  .sm\\:min-w-\\[320px\\] {\n    min-width: 320px;\n  }\n\n  .sm\\:max-w-sm {\n    max-width: 24rem;\n  }\n\n  .sm\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .sm\\:flex-row {\n    flex-direction: row;\n  }\n\n  .sm\\:justify-end {\n    justify-content: flex-end;\n  }\n\n  .sm\\:gap-6 {\n    gap: var(--space-6);\n  }\n\n  .sm\\:gap-8 {\n    gap: var(--space-8);\n  }\n\n  .sm\\:space-x-2 > :not([hidden]) ~ :not([hidden]) {\n    --tw-space-x-reverse: 0;\n    margin-right: calc(var(--space-2) * var(--tw-space-x-reverse));\n    margin-left: calc(var(--space-2) * calc(1 - var(--tw-space-x-reverse)));\n  }\n\n  .sm\\:p-6 {\n    padding: var(--space-6);\n  }\n\n  .sm\\:px-4 {\n    padding-left: var(--space-4);\n    padding-right: var(--space-4);\n  }\n\n  .sm\\:px-6 {\n    padding-left: var(--space-6);\n    padding-right: var(--space-6);\n  }\n\n  .sm\\:px-8 {\n    padding-left: var(--space-8);\n    padding-right: var(--space-8);\n  }\n\n  .sm\\:py-12 {\n    padding-top: var(--space-12);\n    padding-bottom: var(--space-12);\n  }\n\n  .sm\\:py-16 {\n    padding-top: var(--space-16);\n    padding-bottom: var(--space-16);\n  }\n\n  .sm\\:py-4 {\n    padding-top: var(--space-4);\n    padding-bottom: var(--space-4);\n  }\n\n  .sm\\:pt-28 {\n    padding-top: 7rem;\n  }\n\n  .sm\\:pt-8 {\n    padding-top: var(--space-8);\n  }\n\n  .sm\\:text-left {\n    text-align: left;\n  }\n\n  .sm\\:text-2xl {\n    font-size: 1.5rem;\n    line-height: 2rem;\n  }\n\n  .sm\\:text-3xl {\n    font-size: 1.875rem;\n    line-height: 2.25rem;\n  }\n\n  .sm\\:text-4xl {\n    font-size: 2.25rem;\n    line-height: 2.5rem;\n  }\n\n  .sm\\:text-5xl {\n    font-size: 3rem;\n    line-height: 1;\n  }\n\n  .sm\\:text-base {\n    font-size: 1rem;\n    line-height: 1.5rem;\n  }\n\n  .sm\\:text-lg {\n    font-size: 1.125rem;\n    line-height: 1.75rem;\n  }\n\n  .sm\\:text-sm {\n    font-size: 0.875rem;\n    line-height: 1.25rem;\n  }\n\n  .sm\\:text-xl {\n    font-size: 1.25rem;\n    line-height: 1.75rem;\n  }\n\n  .sm\\:leading-8 {\n    line-height: 2rem;\n  }\n\n  .sm\\:leading-none {\n    line-height: 1;\n  }\n}\n\n@media (min-width: 768px) {\n\n  .md\\:flex {\n    display: flex;\n  }\n\n  .md\\:hidden {\n    display: none;\n  }\n\n  .md\\:h-5 {\n    height: var(--space-5);\n  }\n\n  .md\\:h-6 {\n    height: var(--space-6);\n  }\n\n  .md\\:h-7 {\n    height: var(--space-7);\n  }\n\n  .md\\:w-5 {\n    width: var(--space-5);\n  }\n\n  .md\\:w-6 {\n    width: var(--space-6);\n  }\n\n  .md\\:w-7 {\n    width: var(--space-7);\n  }\n\n  .md\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .md\\:flex-row {\n    flex-direction: row;\n  }\n\n  .md\\:items-center {\n    align-items: center;\n  }\n\n  .md\\:rounded-\\[1\\.5rem\\] {\n    border-radius: 1.5rem;\n  }\n\n  .md\\:p-12 {\n    padding: var(--space-12);\n  }\n\n  .md\\:p-3 {\n    padding: var(--space-3);\n  }\n\n  .md\\:px-6 {\n    padding-left: var(--space-6);\n    padding-right: var(--space-6);\n  }\n\n  .md\\:py-12 {\n    padding-top: var(--space-12);\n    padding-bottom: var(--space-12);\n  }\n\n  .md\\:text-2xl {\n    font-size: 1.5rem;\n    line-height: 2rem;\n  }\n\n  .md\\:text-3xl {\n    font-size: 1.875rem;\n    line-height: 2.25rem;\n  }\n\n  .md\\:text-4xl {\n    font-size: 2.25rem;\n    line-height: 2.5rem;\n  }\n\n  .md\\:text-5xl {\n    font-size: 3rem;\n    line-height: 1;\n  }\n\n  .md\\:text-6xl {\n    font-size: 3.75rem;\n    line-height: 1;\n  }\n\n  .md\\:text-sm {\n    font-size: 0.875rem;\n    line-height: 1.25rem;\n  }\n\n  .md\\:text-xl {\n    font-size: 1.25rem;\n    line-height: 1.75rem;\n  }\n}\n\n@media (min-width: 1024px) {\n\n  .lg\\:order-1 {\n    order: 1;\n  }\n\n  .lg\\:order-2 {\n    order: 2;\n  }\n\n  .lg\\:col-span-1 {\n    grid-column: span 1 / span 1;\n  }\n\n  .lg\\:col-start-1 {\n    grid-column-start: 1;\n  }\n\n  .lg\\:col-start-2 {\n    grid-column-start: 2;\n  }\n\n  .lg\\:col-start-3 {\n    grid-column-start: 3;\n  }\n\n  .lg\\:col-end-2 {\n    grid-column-end: 2;\n  }\n\n  .lg\\:col-end-3 {\n    grid-column-end: 3;\n  }\n\n  .lg\\:col-end-4 {\n    grid-column-end: 4;\n  }\n\n  .lg\\:row-start-1 {\n    grid-row-start: 1;\n  }\n\n  .lg\\:row-start-2 {\n    grid-row-start: 2;\n  }\n\n  .lg\\:row-start-3 {\n    grid-row-start: 3;\n  }\n\n  .lg\\:row-start-4 {\n    grid-row-start: 4;\n  }\n\n  .lg\\:row-end-2 {\n    grid-row-end: 2;\n  }\n\n  .lg\\:row-end-3 {\n    grid-row-end: 3;\n  }\n\n  .lg\\:row-end-4 {\n    grid-row-end: 4;\n  }\n\n  .lg\\:row-end-5 {\n    grid-row-end: 5;\n  }\n\n  .lg\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-rows-3 {\n    grid-template-rows: repeat(3, minmax(0, 1fr));\n  }\n\n  .lg\\:grid-rows-4 {\n    grid-template-rows: repeat(4, minmax(0, 1fr));\n  }\n\n  .lg\\:gap-8 {\n    gap: var(--space-8);\n  }\n\n  .lg\\:p-8 {\n    padding: var(--space-8);\n  }\n\n  .lg\\:px-8 {\n    padding-left: var(--space-8);\n    padding-right: var(--space-8);\n  }\n\n  .lg\\:py-16 {\n    padding-top: var(--space-16);\n    padding-bottom: var(--space-16);\n  }\n\n  .lg\\:py-20 {\n    padding-top: var(--space-20);\n    padding-bottom: var(--space-20);\n  }\n\n  .lg\\:py-24 {\n    padding-top: var(--space-24);\n    padding-bottom: var(--space-24);\n  }\n\n  .lg\\:pt-32 {\n    padding-top: 8rem;\n  }\n\n  .lg\\:text-4xl {\n    font-size: 2.25rem;\n    line-height: 2.5rem;\n  }\n\n  .lg\\:text-5xl {\n    font-size: 3rem;\n    line-height: 1;\n  }\n\n  .lg\\:text-7xl {\n    font-size: 4.5rem;\n    line-height: 1;\n  }\n}\n\n@media (min-width: 1280px) {\n\n  .xl\\:grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n\n  .xl\\:text-8xl {\n    font-size: 6rem;\n    line-height: 1;\n  }\n}\n\n.\\[\\&_svg\\]\\:pointer-events-none svg {\n  pointer-events: none;\n}\n\n.\\[\\&_svg\\]\\:size-4 svg {\n  width: var(--space-4);\n  height: var(--space-4);\n}\n\n.\\[\\&_svg\\]\\:shrink-0 svg {\n  flex-shrink: 0;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsDA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2DA;;;;;AASA;;;;AAeA;;;;;;;;;;;AAiBA;;;;;AAWA;;;;;;AAUA;;;;;AAQA;;;;;AAcA;;;;;;AASA;;;;AAYA;;;;;;;AAcA;;;;AAQA;;;;;;;AAQA;;;;AAIA;;;;AAUA;;;;;;AAYA;;;;;;;;;;;;;AAqBA;;;;AAUA;;;;;;AAAA;;;;;;AAAA;;;;;;AAAA;;;;;;AAaA;;;;AAQA;;;;AAQA;;;;AAQA;;;;AAAA;;;;AAUA;;;;;AASA;;;;AASA;;;;;AASA;;;;AAQA;;;;AAgBA;;;;;AAKA;;;;AAIA;;;;;;AAWA;;;;AAQA;;;;AASA;;;;;AAUA;;;;AAQA;;;;AAUA;;;;;AAgBA;;;;;AAOA;;;;AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwZA;;;;;;;;;AAQE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2aA;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwCA;;;;AAGA;;;;;;;;;AAWA;EACE;;;;;;;;AAgBF;;;;;;;;;AAQA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAMA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAQF;;;;AAGA;EAEE;;;;;AAIF;EAEE;;;;;AAIF;EAEE;;;;;AAIF;EAEE;;;;;AAIF;EAEE;;;;;AAIF;;;;;;;;;;;;AAWA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAGA;;;;;;;AAGA;;;;;;;AAGA;;;;;;;AAGA;;;;;;;AAGA;;;;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAMA;;;;AAMA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;;AAMA;;;;AAGA;;;;;;;;;;;;;;;;;;AAkBA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAMA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;;;;AASA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAGA;;;;;AAGA;;;;;AAGA;;;;AA+BA;;;;;;AAOA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;AAIA;;;;;AAMA;;;;;;;;;AASA;;;;;AAKA;;;;;;AAOA;;;;;;;AAOA;;;;;AAKA;;;;;AAMA;;;;;AAKA;;;;;;;;;;;;;;;AAeA;;;;;;AAOA;;;;;AAcA;;;;;;;;;;AAwBA;;;;;;;AAQA;;;;;;;AAQA;;;;;;;AAQA;;;;;;;AASA;EACE;;;;;AAeF;EACE;;;;;;;;;;;;;;;;AAoBF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwFA;;;;AAOA;;;;AASA;;;;AAOA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAQA;;;;AAMA;;;;AAQA;;;;AAOA;;;;AAKA;;;;AASA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAKA;EACE;;;;;;EAOA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;;EAKA;;;;;;AAOF;EACE;;;;;EAMA;;;;;AAMF;EACE;;;;;EAMA;;;;;AAaF;;;;;;;;AAcA;EACE;;;;EAKA;;;;EAKA;;;;;AAOF;;;;;;AAOA;EACE;;;;;EAKA;;;;;AAUF;EACE;;;;;AAKF;EACE;;;;;;;;AAQF;EACE;;;;;AAKF;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAAA;;;;AAIA;;;;AAIA;;;;AAAA;;;;AAIA;;;;AAAA;;;;AAIA;;;;;AAAA;;;;;AAKA;;;;AAAA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;AAKA;;;;;AAKA;;;;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;;;AAKA;;;;;AAKA;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;;AAOA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;AAKA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;;;;;;AAUA;;;;;;;;;;AAUA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;EAEE;;;;EAIA;;;;EAIA;;;;;AAKF;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;AAIA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;;AAMA;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;;EAMA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;AAKF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;;AAMF;EAEE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;;AAMF;EAEE;;;;EAIA;;;;;;AAMF;;;;AAIA;;;;;AAKA", "debugId": null}}]}