{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/app/contact/layout.tsx"], "sourcesContent": ["import { Metadata } from 'next'\n\nexport const metadata: Metadata = {\n  title: 'Contact Us - Get in Touch',\n  description: 'Contact Vierla for support, partnerships, or general inquiries. We\\'re here to help you with your beauty service needs and business questions.',\n  openGraph: {\n    title: 'Contact Us - Get in Touch | Vierla',\n    description: 'Get in touch with <PERSON><PERSON><PERSON> for support, partnerships, or general inquiries. We\\'re here to help.',\n    url: 'https://vierla.com/contact',\n    siteName: 'Vierla',\n    locale: 'en_CA',\n    type: 'website',\n  },\n  twitter: {\n    card: 'summary_large_image',\n    title: 'Contact Us - Get in Touch | Vierla',\n    description: 'Get in touch with <PERSON><PERSON><PERSON> for support, partnerships, or general inquiries. We\\'re here to help.',\n  },\n}\n\nexport default function ContactLayout({\n  children,\n}: {\n  children: React.ReactNode\n}) {\n  return children\n}\n"], "names": [], "mappings": ";;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,WAAW;QACT,OAAO;QACP,aAAa;QACb,KAAK;QACL,UAAU;QACV,QAAQ;QACR,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;IACf;AACF;AAEe,SAAS,cAAc,EACpC,QAAQ,EAGT;IACC,OAAO;AACT", "debugId": null}}]}