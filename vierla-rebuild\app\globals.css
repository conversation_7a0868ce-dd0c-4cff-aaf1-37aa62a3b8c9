@tailwind base;
@tailwind components;
@tailwind utilities;

/*
================================================================================
| VIERLA DESIGN SYSTEM - GLOBAL STYLES (REDESIGNED)
|
| This file contains the complete design system for the Vierla application.
| It's organized into sections for easy maintenance and customization:
|
| 1. MASTER CONTROL PANEL - Core brand colors and theme variables
| 2. PAGE-SPECIFIC CONTROLS - Individual page customization
| 3. GLOBAL COMPONENTS - Shared component styling
| 4. SHADCN UI THEME - UI library integration
| 5. BASE ELEMENT STYLES - HTML element defaults
| 6. PAGE & COMPONENT OVERRIDES - Specific styling rules
| 7. UTILITIES & ANIMATIONS - Helper classes and keyframes
|
| Each component can be individually customized per page while maintaining
| consistency through the master control variables.
================================================================================
*/

@layer base {
  :root {
    /*
    ========================================
    | 🎨 MASTER CONTROL PANEL - New Visual Identity
    | Single source of truth for theme variables that map to Tailwind colors
    ========================================
    */

    /* iOS Safe Area Insets */
    --safe-area-inset-top: env(safe-area-inset-top);
    --safe-area-inset-right: env(safe-area-inset-right);
    --safe-area-inset-bottom: env(safe-area-inset-bottom);
    --safe-area-inset-left: env(safe-area-inset-left);

    /* Master Brand Colors - STRICT DUAL-THEME SEPARATION */
    --master-brand-primary-light: #364035;  /* Light Mode: Vierla Forest - Primary brand color */
    --master-brand-primary-dark: #B8956A;   /* Dark Mode: Vierla Gold - Primary brand color */
    --master-brand-secondary: #2D2A26;      /* Deep Charcoal - Main background */
    --master-brand-accent-light: #8B9A8C;   /* Light Mode: Vierla Sage - Accents */
    --master-brand-accent-dark: #B8956A;    /* Dark Mode: Vierla Gold - Accents */
    --master-brand-forest: #364035;         /* Light Mode Only: Vierla Forest */
    --master-brand-beige: #F0E6D9;          /* Warm Beige - Neutral accent */

    /* Master Neutral Colors - Light Mode */
    --master-bg-light: #F4F1E8;             /* Warm Cream - Primary light background */
    --master-text-primary-light: #2D2A26;   /* Deep Charcoal - Primary text on light */
    --master-text-secondary-light: #8B9A8C; /* Vierla-Sage - Secondary text on light */
    --master-borders-light: #B7C4B7;        /* Muted Sage - Borders and dividers */

    /* Master Neutral Colors - Dark Mode (Modern Luxury Palette) */
    --master-bg-dark: #2D2A26;              /* Deep Charcoal - Primary dark background */
    --master-text-primary-dark: #F4F1E8;    /* Warm Cream - Primary text on dark */
    --master-text-secondary-dark: #A9A299;  /* Muted Gold/Grey - Secondary text on dark */
    --master-borders-dark: #B8956A;         /* Vierla Gold - Borders in dark mode */
    --master-accent-primary-dark: #B8956A;  /* Vierla Gold - Primary accent for dark mode */
    --master-accent-secondary-dark: #A9A299; /* Muted Gold/Grey - Secondary accent for dark mode */

    /* Master Action Colors - STRICT THEME SEPARATION */
    --master-action-primary-light: #364035; /* Light Mode: Vierla Forest - Primary buttons */
    --master-action-primary-dark: #B8956A;  /* Dark Mode: Vierla Gold - Primary buttons */
    --master-action-hover-light: #2A302A;   /* Light Mode: Darker Forest - Hover states */
    --master-action-hover-dark: #A6845C;    /* Dark Mode: Darker Gold - Hover states */
    --master-action-focus-light: #364035;   /* Light Mode: Vierla Forest - Focus states */
    --master-action-focus-dark: #B8956A;    /* Dark Mode: Vierla Gold - Focus states */
    --master-action-secondary-light: #8B9A8C; /* Light Mode: Vierla Sage - Secondary buttons */
    --master-action-secondary-dark: #A9A299; /* Dark Mode: Muted Gold/Champagne - Secondary buttons */
    --master-action-tertiary-light: #8B9A8C; /* Light Mode: Vierla Sage - Tertiary buttons */
    --master-action-tertiary-dark: #A9A299; /* Dark Mode: Muted Gold/Champagne - Tertiary buttons */

    /* Master Semantic Colors - THEME-AWARE */
    --master-success-light: #8B9A8C;        /* Light Mode: Vierla Sage - Success states */
    --master-success-dark: #B8956A;         /* Dark Mode: Vierla Gold - Success states */
    --master-warning-light: #364035;        /* Light Mode: Vierla Forest - Warning states */
    --master-warning-dark: #B8956A;         /* Dark Mode: Vierla Gold - Warning states */
    --master-error: #D97706;                /* Terracotta - Error states (universal) */

    /* Master Text Colors - STRICT THEME SEPARATION */
    --master-text-primary-light: #2D2A26;   /* Light Mode: Deep Charcoal - Main text */
    --master-text-secondary-light: #8B9A8C; /* Light Mode: Vierla Sage - Subtitles */
    --master-text-primary-dark: #F4F1E8;    /* Dark Mode: Warm Cream - Main text */
    --master-text-secondary-dark: #A9A299;  /* Dark Mode: Muted Gold/Champagne - Subtitles */
    --master-text-muted: #F0E6D9;           /* Warm Beige - Muted text (universal) */

    /* Legacy Text Colors (for backward compatibility) */
    --master-text-primary: #2D2A26;         /* Deep Charcoal - Main text on light */
    --master-text-secondary: #8B9A8C;       /* Vierla-Sage - Subtitles */
    --master-text-on-dark: #F4F1E8;         /* Warm Cream - Text on dark backgrounds */

    /* Master Form & Input Colors - THEME-AWARE */
    --master-input-bg-light: #F4F1E8;       /* Light Mode: Warm Cream - Input backgrounds */
    --master-input-bg-dark: #333333;        /* Dark Mode: Light Charcoal - Input backgrounds */
    --master-input-border-light: #8B9A8C;   /* Light Mode: Vierla Sage - Input borders */
    --master-input-border-dark: #A9A299;    /* Dark Mode: Muted Gold/Champagne - Input borders */
    --master-input-border-focus-light: #364035; /* Light Mode: Vierla Forest - Input borders on focus */
    --master-input-border-focus-dark: #B8956A;  /* Dark Mode: Vierla Gold - Input borders on focus */
    --master-input-text: inherit;           /* Inherit text color from parent */
    --master-label-color-light: #8B9A8C;    /* Light Mode: Vierla Sage - Form labels */
    --master-label-color-dark: #A9A299;     /* Dark Mode: Muted Gold/Champagne - Form labels */

    /* Master Icon Colors - STRICT THEME SEPARATION */
    --master-icon-primary-light: #364035;   /* Light Mode: Vierla Forest - Primary icons */
    --master-icon-primary-dark: #B8956A;    /* Dark Mode: Vierla Gold - Primary icons */
    --master-icon-secondary-light: #364035; /* Light Mode: Vierla Forest - Secondary icons */
    --master-icon-secondary-dark: #A9A299;  /* Dark Mode: Muted Gold/Champagne - Secondary icons */
    --master-icon-muted-light: #364035;     /* Light Mode: Vierla Forest - Muted icons */
    --master-icon-muted-dark: #A9A299;      /* Dark Mode: Muted Gold/Champagne - Muted icons */
    --master-icon-success-light: #364035;   /* Light Mode: Vierla Forest - Success icons */
    --master-icon-success-dark: #B8956A;    /* Dark Mode: Vierla Gold - Success icons */

    /* Master Card & Container Colors - STRICT THEME SEPARATION */
    --master-card-bg-light: #F4F1E8;        /* Light Mode: Warm Cream - Card backgrounds */
    --master-card-bg-dark: #333333;         /* Dark Mode: Light Charcoal - Card backgrounds */
    --master-card-border-light: #8B9A8C;    /* Light Mode: Vierla Sage - Card borders */
    --master-card-border-dark: #A9A299;     /* Dark Mode: Muted Gold/Champagne - Card borders */
    --master-card-border-hover-light: #364035; /* Light Mode: Vierla Forest - Card borders on hover */
    --master-card-border-hover-dark: #B8956A; /* Dark Mode: Vierla Gold - Card borders on hover */
    --master-card-glow-light: #8B9A8C;      /* Light Mode: Vierla Sage - Card glow effects */
    --master-card-glow-dark: #B8956A;       /* Dark Mode: Vierla Gold - Card glow effects */
    --master-card-accent-light: #364035;    /* Light Mode: Vierla Forest - Card accent elements */
    --master-card-accent-dark: #B8956A;     /* Dark Mode: Vierla Gold - Card accent elements */

    /* Typography Scale (New Visual Identity) - Original Desktop Sizes */
    --font-size-h1-original: 3.052rem;    /* 48.83px - Notable (Oswald) Bold */
    --font-size-h2-original: 2.441rem;    /* 39.06px - Tai Heritage Pro (Playfair) Bold */
    --font-size-h3-original: 1.953rem;    /* 31.25px - Tai Heritage Pro (Playfair) Regular */
    --font-size-h4-original: 1.563rem;    /* 25px - Jost (Inter) Bold */
    --font-size-h5-original: 1.25rem;     /* 20px - Jost (Inter) Bold */
    --font-size-body-original: 1rem;      /* 16px - Jost (Inter) Regular */
    --font-size-small-original: 0.8rem;   /* 12.8px - Jost (Inter) Medium */

    /* Typography Scale - 33.33% Reduced for Non-Mobile Devices */
    --font-size-h1: 2.035rem;    /* 32.56px - Reduced by 33.33% */
    --font-size-h2: 1.627rem;    /* 26.04px - Reduced by 33.33% */
    --font-size-h3: 1.302rem;    /* 20.83px - Reduced by 33.33% */
    --font-size-h4: 1.042rem;    /* 16.67px - Reduced by 33.33% */
    --font-size-h5: 0.833rem;    /* 13.33px - Reduced by 33.33% */
    --font-size-body: 0.667rem;  /* 10.67px - Reduced by 33.33% */
    --font-size-small: 0.533rem; /* 8.53px - Reduced by 33.33% */

    /* Mobile Typography Scale (Responsive) - Keep existing mobile sizes */
    --font-size-h1-mobile: 2.25rem;  /* 36px - Scaled down for mobile */
    --font-size-h2-mobile: 1.875rem; /* 30px - Scaled down for mobile */
    --font-size-h3-mobile: 1.5rem;   /* 24px - Scaled down for mobile */
    --font-size-h4-mobile: 1.25rem;  /* 20px - Scaled down for mobile */
    --font-size-h5-mobile: 1.125rem; /* 18px - Scaled down for mobile */
    --font-size-body-mobile: 0.875rem; /* 14px - Slightly smaller for mobile */
    --font-size-small-mobile: 0.75rem; /* 12px - Smaller for mobile */

    /* Font Family Controls - Updated to New Typography Specifications */
    --font-family-h1: var(--font-playfair);       /* Playfair Display for H1 headlines - Elegant serif for impact */
    --font-family-h2: var(--font-playfair);       /* Playfair Display for H2 headlines - Consistent hierarchy */
    --font-family-h3: var(--font-tai-heritage);   /* Playfair Display for H3 - Elegant serif */
    --font-family-body: var(--font-inter);        /* Inter for UI/body text - Optimized readability */
    --font-family-ui: var(--font-inter);          /* Inter for UI elements - Consistent interface */
    --font-family-accent: var(--font-farsan);     /* Dancing Script for accent text */

    /* 8-Point Grid System (Vierla Design System) - Original Sizes */
    --space-1-original: 0.5rem;   /* 8px */
    --space-2-original: 1rem;     /* 16px */
    --space-3-original: 1.5rem;   /* 24px */
    --space-4-original: 2rem;     /* 32px */
    --space-5-original: 2.5rem;   /* 40px */
    --space-6-original: 3rem;     /* 48px */
    --space-7-original: 3.5rem;   /* 56px */
    --space-8-original: 4rem;     /* 64px */
    --space-9-original: 4.5rem;   /* 72px */
    --space-10-original: 5rem;    /* 80px */
    --space-12-original: 6rem;    /* 96px */
    --space-16-original: 8rem;    /* 128px */
    --space-20-original: 10rem;   /* 160px */
    --space-24-original: 12rem;   /* 192px */

    /* 8-Point Grid System - 33.33% Reduced for Non-Mobile Devices */
    --space-1: 0.333rem;   /* 5.33px - Reduced by 33.33% */
    --space-2: 0.667rem;   /* 10.67px - Reduced by 33.33% */
    --space-3: 1rem;       /* 16px - Reduced by 33.33% */
    --space-4: 1.333rem;   /* 21.33px - Reduced by 33.33% */
    --space-5: 1.667rem;   /* 26.67px - Reduced by 33.33% */
    --space-6: 2rem;       /* 32px - Reduced by 33.33% */
    --space-7: 2.333rem;   /* 37.33px - Reduced by 33.33% */
    --space-8: 2.667rem;   /* 42.67px - Reduced by 33.33% */
    --space-9: 3rem;       /* 48px - Reduced by 33.33% */
    --space-10: 3.333rem;  /* 53.33px - Reduced by 33.33% */
    --space-12: 4rem;      /* 64px - Reduced by 33.33% */
    --space-16: 5.333rem;  /* 85.33px - Reduced by 33.33% */
    --space-20: 6.667rem;  /* 106.67px - Reduced by 33.33% */
    --space-24: 8rem;      /* 128px - Reduced by 33.33% */

    /* Master Button Colors - Natural Wellness Palette for Light Mode */
    --master-button-primary-bg: #364035;           /* Vierla Forest - Primary button background for light mode */
    --master-button-primary-text: #F4F1E8;         /* Warm Cream - Text on primary buttons */
    --master-button-primary-hover: #2A302A;        /* Darker Forest - Primary button hover */
    --master-button-secondary-bg: #8B9A8C;         /* Vierla-Sage - Secondary button background */
    --master-button-secondary-text: #F4F1E8;       /* Warm Cream - Secondary button text */
    --master-button-secondary-hover: #7A8A7C;      /* Sage 600 - Secondary button hover */
    --master-button-tertiary-bg: transparent;      /* Transparent tertiary buttons */
    --master-button-tertiary-text: #8B9A8C;        /* Vierla-Sage - Tertiary button text */
    --master-button-tertiary-hover: underline;     /* Underline on hover for tertiary */

    /* Master Card & Container Colors */
    --master-card-background: theme('colors.neutral-charcoal-light'); /* #333333 - Card backgrounds */
    --master-card-border: theme('colors.sage.DEFAULT / 0.3');         /* Sage border with opacity */
    --master-card-glow: theme('colors.brand-gold');                   /* #B8956A - Card glow effects */

    /* Master Header/Navbar Colors - Proper Glassmorphism (Light Mode) */
    --master-header-background: #f6f7f6;                           /* Light mode: opaque very light sage */
    --master-header-backdrop-blur: 50%;                            /* 50% backdrop blur for proper glassmorphism */
    --master-header-border: rgba(139, 154, 140, 0.2);              /* Light mode: medium sage border */
    --master-header-logo-bg: linear-gradient(135deg, #364035, #8B9A8C); /* Light mode: Forest to Sage gradient */
    --master-header-logo-border: rgba(54, 64, 53, 0.3);            /* Light mode: Vierla Forest border */
    --master-header-logo-icon: #F4F1E8;                            /* Light mode: Warm Cream icon for contrast */
    --master-header-brand-text: #181b19;                           /* Light mode: very dark charcoal text */
    --master-header-nav-text: theme('colors.neutral-off-white');    /* #F5FAF7 - Nav text */
    --master-header-nav-text-muted: theme('colors.brand-gold');     /* #B8956A - Muted nav text */
    --master-header-nav-active-bg: rgba(124, 154, 133, 0.2);        /* Glassmorphic sage active background */
    --master-header-nav-active-glow: transparent;                   /* No active glow */
    --master-header-nav-hover-bg: rgba(184, 149, 106, 0.15);        /* Glassmorphic gold hover background */

    /*
    ========================================
    | 📄 PAGE-SPECIFIC COMPONENT CONTROLS
    ========================================
    */

    /* ----- 🏠 HOME PAGE - THEME-AWARE CONTROLS ----- */
    --home-hero-title-light: #2D2A26;              /* Light Mode: Deep Charcoal for main headline */
    --home-hero-title-dark: #F4F1E8;               /* Dark Mode: Warm Cream for main headline */
    --home-hero-subtitle-light: #8B9A8C;           /* Light Mode: Vierla Sage for subtitle */
    --home-hero-subtitle-dark: #A9A299;            /* Dark Mode: Muted Gold/Champagne for subtitle */
    --home-hero-cta-bg-light: #364035;             /* Light Mode: Vierla Forest for CTA */
    --home-hero-cta-bg-dark: #B8956A;              /* Dark Mode: Vierla Gold for CTA */
    --home-hero-cta-text-light: #F4F1E8;           /* Light Mode: Warm Cream text on CTA */
    --home-hero-cta-text-dark: #2D2A26;            /* Dark Mode: Deep Charcoal text on CTA */
    --home-card-background: #333333;               /* Light Charcoal for cards */
    --home-card-border-light: rgba(139, 154, 140, 0.3); /* Light Mode: Vierla Sage border */
    --home-card-border-dark: rgba(169, 162, 153, 0.3);  /* Dark Mode: Muted Gold/Champagne border */
    --home-card-glow-light: #8B9A8C;               /* Light Mode: Vierla Sage glow effect */
    --home-card-glow-dark: #B8956A;                /* Dark Mode: Vierla Gold glow effect */

    /* Home Page Shiny Button Controls - THEME-AWARE */
    --home-shiny-button-bg-light: #364035;         /* Light Mode: Vierla Forest background */
    --home-shiny-button-bg-dark: #B8956A;          /* Dark Mode: Vierla Gold background */
    --home-shiny-button-text-light: #F4F1E8;       /* Light Mode: Warm Cream text */
    --home-shiny-button-text-dark: #2D2A26;        /* Dark Mode: Deep Charcoal text */
    --home-shiny-button-shimmer-light: #8B9A8C;    /* Light Mode: Vierla Sage shimmer */
    --home-shiny-button-shimmer-dark: #F4F1E8;     /* Dark Mode: Warm Cream shimmer */

    /* Home Page Text Effects */
    --home-text-shimmer-base: var(--master-text-on-dark);
    --home-text-shimmer-highlight: var(--master-brand-accent);
    --home-word-pullup-color: var(--mantle-100);
    --home-marquee-text: var(--mantle-100);
    --home-marquee-bg: transparent;

    /* Home Page Golden Cards */
    --home-golden-card-bg: var(--master-card-background);
    --home-golden-card-border: var(--master-card-border);
    --home-golden-card-glow: var(--master-card-glow);

    /* ----- 🎯 FEATURES PAGE ----- */
    --features-hero-title: #F5FAF7;                /* Light Off-White for title */
    --features-hero-subtitle: #F0E6D9;             /* Warm Beige for subtitle */
    --features-bento-card-bg: #333333;             /* Light Charcoal for cards */
    --features-bento-card-border: rgba(124, 154, 133, 0.3); /* Subtle sage border */
    --features-bento-card-title: #F5FAF7;          /* Light Off-White for card titles */
    --features-bento-card-text: #F0E6D9;           /* Warm Beige for card text */
    --features-bento-icon-color: #7C9A85;          /* Sage for icons */
    --features-bento-grid-gap: 1.5rem;
    --features-cta-button-bg: #B8956A;             /* Gold for CTA buttons */
    --features-cta-button-text: #2C3137;           /* Dark text on CTA */
    --features-cta-button-hover: #A6845C;          /* Darker gold on hover */

    /* Features Page Component Controls */
    --features-shiny-button-bg: var(--master-button-primary-bg);
    --features-shiny-button-text: var(--master-button-primary-text);
    --features-shiny-button-shimmer: var(--master-brand-accent);
    --features-golden-card-bg: var(--master-card-background);
    --features-golden-card-border: var(--master-card-border);
    --features-golden-card-glow: var(--master-card-glow);

    /* ----- 💰 PRICING PAGE ----- */
    --pricing-hero-title: #F5FAF7;                 /* Light Off-White for title */
    --pricing-hero-subtitle: #F0E6D9;              /* Warm Beige for subtitle */
    --pricing-card-background: #333333;            /* Light Charcoal for cards */
    --pricing-card-border: rgba(124, 154, 133, 0.3); /* Subtle sage border */
    --pricing-card-glow: #B8956A;                  /* Gold glow effect */
    --pricing-card-title: #F5FAF7;                 /* Light Off-White for card titles */
    --pricing-card-price: #B8956A;                 /* Gold for pricing */
    --pricing-card-text: #F0E6D9;                  /* Warm Beige for card text */
    --pricing-popular-bg: rgba(184, 149, 106, 0.1); /* Subtle gold background */
    --pricing-popular-border: #B8956A;             /* Gold border for popular plan */
    --pricing-shiny-button-bg: #B8956A;            /* Gold button background */
    --pricing-shiny-button-text: #2C3137;          /* Dark text on button */
    --pricing-shiny-button-shimmer: #F5FAF7;       /* Light shimmer */
    --pricing-badge-bg: #7C9A85;                   /* Sage badge background */
    --pricing-badge-text: #F5FAF7;                 /* Light text on badge */

    /* ----- 👥 ABOUT PAGE ----- */
    --about-hero-title: #F5FAF7;                   /* Light Off-White for title */
    --about-hero-subtitle: #F0E6D9;                /* Warm Beige for subtitle */
    --about-card-background: #333333;              /* Light Charcoal for cards */
    --about-card-border: rgba(124, 154, 133, 0.3); /* Subtle sage border */
    --about-card-glow: #B8956A;                    /* Gold glow effect */
    --about-team-card-bg: #333333;                 /* Light Charcoal for team cards */
    --about-team-card-border: rgba(124, 154, 133, 0.2); /* Very subtle sage border */
    --about-team-name: #F5FAF7;                    /* Light Off-White for names */
    --about-team-role: #7C9A85;                    /* Sage for roles */
    --about-team-bio: #F0E6D9;                     /* Warm Beige for bios */
    --about-shiny-button-bg: #B8956A;              /* Gold button background */
    --about-shiny-button-text: #2C3137;            /* Dark text on button */
    --about-shiny-button-shimmer: #F5FAF7;         /* Light shimmer */
    --about-mission-bg: rgba(51, 51, 51, 0.5);     /* Semi-transparent charcoal */

    /* ----- 📞 CONTACT PAGE ----- */
    --contact-hero-title: #F5FAF7;                 /* Light Off-White for title */
    --contact-hero-subtitle: #F0E6D9;              /* Warm Beige for subtitle */
    --contact-form-bg: #333333;                    /* Light Charcoal for form */
    --contact-form-border: rgba(124, 154, 133, 0.3); /* Subtle sage border */
    --contact-input-bg: rgba(240, 230, 217, 0.1);  /* Very subtle warm background */
    --contact-input-border: #7C9A85;               /* Sage border for inputs */
    --contact-input-text: #F5FAF7;                 /* Light Off-White input text */
    --contact-label-text: #F0E6D9;                 /* Warm Beige for labels */
    --contact-input-focus: #B8956A;                /* Gold focus color */
    --contact-shiny-button-bg: #B8956A;            /* Gold button background */
    --contact-shiny-button-text: #2C3137;          /* Dark text on button */
    --contact-shiny-button-shimmer: #F5FAF7;       /* Light shimmer */

    /* ----- 🧩 GLOBAL COMPONENTS - Light Mode ----- */
    --global-footer-bg: rgba(225, 230, 225, 0.9);  /* Light mode: light sage footer */
    --global-footer-border: rgba(139, 154, 140, 0.3); /* Light mode: medium sage border */
    --global-footer-text: #2e332f;                 /* Light mode: dark charcoal text */
    --global-footer-link: #181b19;                 /* Light mode: very dark charcoal links */
    --global-footer-link-hover: #5f6d61;           /* Light mode: dark sage hover */
    --global-cookie-bg: #f6f7f6;                   /* Light mode: very light sage popup */
    --global-cookie-border: #8b9a8c;               /* Light mode: medium sage border */
    --global-cookie-text: #181b19;                 /* Light mode: very dark charcoal text */
    --global-cookie-button-bg: #5f6d61;            /* Light mode: dark sage button */

    /* ======================================== */
    /* 🔮 UI CHROME MASTER CONTROLS - v1.5.6    */
    /* ======================================== */

    /* -- Header & Footer (Light Mode) - Natural Wellness Theme -- */
    --header-footer-bg: rgba(246, 247, 246, 0.3); /* 30% opacity as preferred */
    --header-footer-backdrop-blur: blur(20px); /* Consistent 20px blur */
    --header-footer-border: rgba(139, 154, 140, 0.3); /* Vierla Sage border */

    /* -- Theme-Aware Icons (Light Mode) -- */
    --icon-primary: #364035; /* Dark Sage Green (Vierla Forest) */
    --icon-secondary: #364035; /* Dark Sage Green (Vierla Forest) */
    --icon-accent: #364035; /* Dark Sage Green (Vierla Forest) */
    --icon-on-accent: #f6f7f6; /* Very light sage for use on colored buttons */

    /* ----- 🎨 THEME-AWARE ICON COLORS ----- */
    --icon-muted: #364035;                         /* Light mode: Dark Sage Green (Vierla Forest) for muted icons */
    --icon-on-dark: #f6f7f6;                       /* Light mode: very light sage for icons on dark backgrounds */
    --global-cookie-button-text: #2C3137;          /* Dark text on cookie button */

    /* ----- 🎴 BENTO GRID CARD CONTROLS - LIGHT MODE (NO GOLD) ----- */
    --card-bg: rgba(244, 241, 232, 0.80);          /* Light mode: Warm Cream with absolute 80% transparency */
    --shadow-card: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --accent-hover-overlay: rgba(168, 195, 169, 0.05); /* Light Mode: Light Sage Green overlay on hover (NO GOLD) */
    --accent-bg: rgba(168, 195, 169, 0.15);        /* Light Mode: Light Sage Green accent background (NO GOLD) */
    --border-accent: rgba(168, 195, 169, 0.3);     /* Light Mode: Light Sage Green border color (NO GOLD) */

    /* ----- 📝 FORM INPUT CONTROLS ----- */
    --input-bg: rgba(246, 247, 246, 0.8);          /* Light mode: input background */
    --input-border: rgba(139, 154, 140, 0.3);      /* Light mode: input border */
    --input-placeholder: rgba(46, 51, 47, 0.6);    /* Light mode: placeholder text */

    /* ----- 📝 WCAG-COMPLIANT TEXT COLORS ----- */
    /* Primary Body Text */
    --text-primary: #2C3137;                       /* Light mode: Dark Charcoal for main text */
    --text-secondary: #2C3137;                     /* Light mode: Charcoal for footer text */
    --text-headlines: #2C3137;                     /* Light mode: Dark Charcoal for headlines */

    /* ----- 🎨 THEME-AWARE COMPONENT COLORS (LIGHT MODE) ----- */
    --theme-primary: #364035;                      /* Light Mode: Vierla Forest for primary buttons/CTAs */
    --theme-secondary: #A8C3A9;                    /* Light Mode: Light Sage Green for secondary elements */
    --text-links: #7C9A85;                         /* Light mode: Sage for links */
    --text-links-hover: #6B8A73;                   /* Light mode: Darker sage for link hover */

    /* ----- 🎨 PROVIDERS PAGE ----- */
    --providers-hero-title: #F5FAF7;               /* Light Off-White for title */
    --providers-hero-subtitle: #F0E6D9;            /* Warm Beige for subtitle */
    --providers-card-bg: #333333;                  /* Light Charcoal for cards */
    --providers-card-border: rgba(124, 154, 133, 0.3); /* Subtle sage border */
    --providers-card-title: #F5FAF7;               /* Light Off-White for card titles */
    --providers-card-text: #F0E6D9;                /* Warm Beige for card text */
    --providers-icon-color: #7C9A85;               /* Sage for icons */
    --providers-cta-bg: #B8956A;                   /* Gold for CTA buttons */
    --providers-cta-text: #2C3137;                 /* Dark text on CTA */
    --providers-coming-soon-bg: #333333;           /* Light Charcoal for coming soon section */

    /* ----- 📝 APPLY PAGE ----- */
    --apply-hero-title: #F5FAF7;                   /* Light Off-White for title */
    --apply-hero-subtitle: #F0E6D9;                /* Warm Beige for subtitle */
    --apply-form-bg: #333333;                      /* Light Charcoal for form */
    --apply-form-border: rgba(124, 154, 133, 0.3); /* Subtle sage border */
    --apply-progress-active: #7C9A85;              /* Sage for active progress */
    --apply-progress-inactive: #333333;            /* Light Charcoal for inactive */
    --apply-progress-bar: #7C9A85;                 /* Sage for progress bar */
    --apply-step-text: #F5FAF7;                    /* Light Off-White for step text */

    /* Shiny Button Global Controls */
    --global-shiny-button-primary-bg: #B8956A;     /* Gold primary button */
    --global-shiny-button-primary-text: #2C3137;   /* Dark text on primary */
    --global-shiny-button-primary-shimmer: #F5FAF7; /* Light shimmer */
    --global-shiny-button-secondary-bg: transparent; /* Transparent secondary */
    --global-shiny-button-secondary-text: #7C9A85; /* Sage secondary text */
    --global-shiny-button-secondary-shimmer: #7C9A85; /* Sage shimmer */

    /* Shimmer Button Global Controls */
    --global-shimmer-button-primary-bg: #B8956A;   /* Gold primary shimmer button */
    --global-shimmer-button-primary-shimmer: #F5FAF7; /* Light shimmer */
    --global-shimmer-button-secondary-bg: transparent; /* Transparent secondary */
    --global-shimmer-button-secondary-shimmer: #7C9A85; /* Sage shimmer */

    /* Text Shimmer Global Controls */
    --global-text-shimmer-base: #F5FAF7;           /* Light Off-White base */
    --global-text-shimmer-highlight: #B8956A;      /* Gold highlight */

    /* Golden Glowing Card Global Controls */
    --global-golden-card-bg: #333333;              /* Light Charcoal background */
    --global-golden-card-border: rgba(124, 154, 133, 0.3); /* Subtle sage border */
    --global-golden-card-glow: #B8956A;            /* Gold glow effect */

    /*
    ========================================
    | 🎨 SHADCN UI THEME VARIABLES - Dark Mode (Default)
    | "Modern Luxury" Theme - Gold & Charcoal Palette
    ========================================
    */
    --background: 44 7% 17%;       /* Deep Charcoal (#2D2A26) - Main background */
    --foreground: 44 15% 95%;      /* Warm Cream (#F4F1E8) - Main text */
    --card: 0 0% 20%;              /* Light Charcoal (#333333) - Card backgrounds */
    --card-foreground: 44 15% 95%; /* Warm Cream - Card text */
    --popover: 0 0% 20%;           /* Light Charcoal - Popover backgrounds */
    --popover-foreground: 44 15% 95%; /* Warm Cream - Popover text */
    --primary: 40 35% 55%;         /* Vierla Gold (#B8956A) - Primary CTAs, active nav */
    --primary-foreground: 44 7% 17%; /* Deep Charcoal - Text on primary */
    --secondary: 40 20% 65%;       /* Muted Gold/Champagne (#A9A299) - Secondary buttons */
    --secondary-foreground: 44 7% 17%; /* Deep Charcoal - Text on secondary */
    --muted: 44 7% 17%;            /* Deep Charcoal - Muted backgrounds */
    --muted-foreground: 40 20% 65%; /* Muted Gold/Champagne - Muted text */
    --accent: 40 35% 55%;          /* Vierla Gold - Accent elements */
    --accent-foreground: 44 7% 17%; /* Deep Charcoal - Text on accent */
    --destructive: 25 95% 53%;     /* Terracotta (#D97706) - Error/destructive */
    --destructive-foreground: 0 0% 98%; /* White - Text on destructive */
    --border: 40 20% 65% / 0.3;    /* Muted Gold/Champagne with opacity - Borders */
    --input: 40 20% 65% / 0.3;     /* Muted Gold/Champagne with opacity - Input borders */
    --ring: 40 35% 55%;            /* Vierla Gold - Focus rings */
    --radius: 0.5rem;              /* Border radius */
  }

  .dark {
    /*
    ========================================
    | 🎨 SHADCN UI THEME VARIABLES - Dark Mode
    | Dark theme maintains our current design
    ========================================
    */
    --background: 44 12% 20%;      /* Dark Charcoal - Main background */
    --foreground: 120 20% 97%;     /* Light Off-White - Main text */
    --card: 0 0% 20%;              /* Light Charcoal - Card backgrounds */
    --card-foreground: 120 20% 97%; /* Light Off-White - Card text */
    --popover: 0 0% 20%;           /* Light Charcoal - Popover backgrounds */
    --popover-foreground: 120 20% 97%; /* Light Off-White - Popover text */
    --primary: 40 35% 55%;         /* Vierla Gold (#B8956A) - Primary elements (NO GREEN) */
    --primary-foreground: 44 7% 17%; /* Deep Charcoal - Text on primary */
    --secondary: 40 20% 65%;       /* Muted Gold/Champagne (#A9A299) - Secondary elements */
    --secondary-foreground: 44 7% 17%; /* Deep Charcoal - Text on secondary */
    --muted: 44 7% 17%;            /* Deep Charcoal - Muted backgrounds */
    --muted-foreground: 40 20% 65%; /* Muted Gold/Champagne - Muted text */
    --accent: 40 35% 55%;          /* Vierla Gold - Accent elements (NO GREEN) */
    --accent-foreground: 44 7% 17%; /* Deep Charcoal - Text on accent */
    --destructive: 25 95% 53%;     /* Terracotta - Error/destructive */
    --destructive-foreground: 0 0% 98%; /* White - Text on destructive */
    --border: 40 20% 65% / 0.3;    /* Muted Gold/Champagne with opacity - Borders (NO GREEN) */
    --input: 40 20% 65% / 0.3;     /* Muted Gold/Champagne with opacity - Input borders (NO GREEN) */
    --ring: 40 35% 55%;            /* Vierla Gold - Focus rings (NO GREEN) */

    /* Dark mode master variables */
    --master-brand-secondary: theme('colors.neutral-charcoal-dark');
    --master-text-on-dark: theme('colors.neutral-off-white');
    --master-card-background: theme('colors.neutral-charcoal-light');
  }

  /* Light mode overrides */
  :root:not(.dark) {
    /*
    ========================================
    | 🎨 SHADCN UI THEME VARIABLES - Light Mode
    | "Natural Wellness" Theme - Green, Cream & Charcoal Palette
    ========================================
    */
    --background: 44 15% 95%;      /* Warm Cream (#F4F1E8) - Main background */
    --foreground: 44 7% 17%;       /* Deep Charcoal (#2D2A26) - Main text */
    --card: 0 0% 95%;              /* Very light gray - Card backgrounds */
    --card-foreground: 44 7% 17%;  /* Deep Charcoal - Card text */
    --popover: 0 0% 98%;           /* Almost white - Popover backgrounds */
    --popover-foreground: 44 7% 17%; /* Deep Charcoal - Popover text */
    --primary: 150 8% 22%;         /* Vierla Forest (#364035) - Primary CTAs, active nav */
    --primary-foreground: 44 15% 95%; /* Warm Cream - Text on primary */
    --secondary: 150 12% 55%;      /* Vierla Sage (#8B9A8C) - Secondary buttons, sub-headings */
    --secondary-foreground: 44 7% 17%; /* Deep Charcoal - Text on secondary */
    --muted: 0 0% 95%;             /* Very light gray - Muted backgrounds */
    --muted-foreground: 150 12% 55%; /* Vierla Sage - Muted text */
    --accent: 150 8% 22%;          /* Vierla Forest (#364035) - Accent elements (NO GOLD in light mode) */
    --accent-foreground: 44 15% 95%; /* Warm Cream - Text on accent */
    --destructive: 25 95% 53%;     /* Terracotta - Error/destructive */
    --destructive-foreground: 0 0% 98%; /* White - Text on destructive */
    --border: 150 12% 55% / 0.3;   /* Vierla Sage with opacity - Borders */
    --input: 150 12% 55% / 0.3;    /* Vierla Sage with opacity - Input borders */
    --ring: 150 8% 22%;            /* Vierla Forest - Focus rings (NO GOLD in light mode) */

    /* Light mode master variables - Natural Wellness Palette */
    --master-brand-secondary: #F4F1E8;      /* Warm Cream background */
    --master-text-on-dark: #2D2A26;         /* Deep Charcoal text */
    --master-card-background: #F4F1E8;      /* Warm Cream cards */
    --master-accent-primary-light: #364035; /* Vierla Forest - Primary accent for light mode */
    --master-accent-secondary-light: #8B9A8C; /* Vierla Sage - Secondary accent for light mode */
  }

  /*
  ========================================
  | BASE ELEMENT STYLES
  ========================================
  */
  * {
    @apply border-border;
  }
  body {
    @apply text-foreground;
    font-family: var(--font-family-body); /* Inter for body text */
    /* background-color: var(--master-brand-secondary); - Removed to allow aurora background transparency */
    color: var(--master-text-on-dark); /* Light Off-White text */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: var(--font-size-body);
  }

  /* ========================================
   * GLOBAL PAGE SIZING INCREASE - 25% (from 0.8 to 1.0 scale)
   ======================================== */
  main {
    transform: scale(1.0);
    transform-origin: top center;
    width: 100%; /* Full width at normal scale */
    margin-left: 0; /* No offset needed at normal scale */
  }

  /* Desktop Scale Reduction - 10% smaller content on non-mobile devices */
  @media (min-width: 768px) {
    .page-home,
    .page-about,
    .page-features,
    .page-pricing,
    .page-provider,
    .page-apply,
    .page-privacy,
    .page-terms,
    .page-contact {
      transform: scale(0.9);
      transform-origin: top center;
      width: 111.11%; /* Compensate for scale to maintain full width */
      margin-left: -5.56%; /* Center the scaled content */
    }
  }

  /* Typography Hierarchy - New Visual Identity */
  h1 {
    font-family: var(--font-family-h1);
    font-size: var(--font-size-h1);
    font-weight: 700;
    line-height: 1.2;
    text-transform: uppercase;
    letter-spacing: 0.02em;
  }
  h2 {
    font-family: var(--font-family-h2);
    font-size: var(--font-size-h2);
    font-weight: 700;
    line-height: 1.3;
  }
  h3 {
    font-family: var(--font-family-h3);
    font-size: var(--font-size-h3);
    font-weight: 400;
    line-height: 1.4;
  }
  h4 {
    font-family: var(--font-family-body);
    font-size: var(--font-size-h4);
    font-weight: 700;
    line-height: 1.4;
  }
  h5 {
    font-family: var(--font-family-body);
    font-size: var(--font-size-h5);
    font-weight: 700;
    line-height: 1.5;
  }
  h6 {
    font-family: var(--font-family-body);
    font-size: var(--font-size-body);
    font-weight: 600;
    line-height: 1.5;
  }

  p {
    font-family: var(--font-family-body);
    font-size: var(--font-size-body);
    font-weight: 400;
    line-height: 1.6;
  }

  small, .text-small {
    font-family: var(--font-family-body);
    font-size: var(--font-size-small);
    font-weight: 500;
    line-height: 1.4;
  }

  /* Accent text class for optional signature/handwritten touch */
  .text-accent {
    font-family: var(--font-family-accent);
    font-weight: 400;
  }
}

/*
========================================
| PAGE & COMPONENT-SPECIFIC CLASS OVERRIDES
========================================
*/

/* ----- Home Page ----- */
.page-home .shiny-button {
  background-color: var(--home-shiny-button-bg);
  color: var(--home-shiny-button-text);
  --shimmer-color: var(--home-shiny-button-shimmer);
}

/* Home Page Specific Shiny Button Variants */
.page-home .shiny-button.hero-cta {
  background-color: var(--home-shiny-button-hero-bg);
  color: var(--home-shiny-button-hero-text);
  --shimmer-color: var(--home-shiny-button-hero-shimmer);
}

.page-home .shiny-button.section-cta {
  background-color: var(--home-shiny-button-cta-bg);
  color: var(--home-shiny-button-cta-text);
  --shimmer-color: var(--home-shiny-button-cta-shimmer);
}

.page-home .golden-glowing-card {
  background: var(--home-golden-card-bg);
  border-color: var(--home-golden-card-border);
  --glow-color: var(--home-golden-card-glow);
}

.page-home .text-shimmer {
  --base-color: var(--home-text-shimmer-base);
  --base-gradient-color: var(--home-text-shimmer-highlight);
}

.page-home .word-pull-up {
  color: var(--home-word-pullup-color);
}

.page-home .marquee-animation {
  color: var(--home-marquee-text);
  background: var(--home-marquee-bg);
}

/* ----- Navigation Styles ----- */
.nav-link {
  color: var(--text-primary);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  font-weight: 500;
}

.nav-link:hover {
  background: var(--master-header-nav-hover-bg);
  color: var(--text-primary);
}

.nav-link-active {
  background: var(--master-header-nav-active-bg);
  color: var(--text-primary);
  box-shadow: var(--master-header-nav-active-glow);
}

/* ----- Form Input Styles ----- */
.theme-input {
  background-color: var(--input-bg) !important;
  border-color: var(--input-border) !important;
  color: var(--text-primary) !important;
  transition: all 0.3s ease;
}

.theme-input::placeholder {
  color: var(--input-placeholder) !important;
  opacity: 1;
}

.theme-input:focus {
  border-color: var(--icon-accent) !important;
  box-shadow: 0 0 0 2px var(--accent-hover-overlay) !important;
}

/* ----- Limelight Navigation Effect - THEME-AWARE ----- */
.limelight-nav {
  position: relative;
  overflow: hidden;
}

/* Light Mode: Vierla Forest spotlight */
:root:not(.dark) .limelight-nav::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, #364035 0%, transparent 70%); /* Vierla Forest */
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  z-index: -1;
  border-radius: 50%;
  opacity: 0;
}

:root:not(.dark) .limelight-nav:hover::before,
:root:not(.dark) .limelight-nav.nav-link-active::before {
  width: 120px;
  height: 120px;
  opacity: 0.3;
}

:root:not(.dark) .limelight-nav:hover,
:root:not(.dark) .limelight-nav.nav-link-active {
  color: #364035; /* Vierla Forest */
  text-shadow: 0 0 8px rgba(54, 64, 53, 0.5);
}

/* Dark Mode: Vierla Gold spotlight */
.dark .limelight-nav::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: radial-gradient(circle, #B8956A 0%, transparent 70%); /* Vierla Gold */
  transform: translate(-50%, -50%);
  transition: all 0.3s ease;
  z-index: -1;
  border-radius: 50%;
  opacity: 0;
}

.dark .limelight-nav:hover::before,
.dark .limelight-nav.nav-link-active::before {
  width: 120px;
  height: 120px;
  opacity: 0.3;
}

.dark .limelight-nav:hover,
.dark .limelight-nav.nav-link-active {
  color: #B8956A; /* Vierla Gold */
  text-shadow: 0 0 8px rgba(184, 149, 106, 0.5);
}

/*
========================================
| UTILITIES & ANIMATIONS
========================================
*/
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}



@keyframes beam {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(100%);
  }
}

/*
========================================
| FIX FOR BACKGROUND ISSUE
| This section is intentionally left blank. The problematic
| `!important` override has been removed.
========================================
*/

/*
========================================
| 🔮 GLASSMORPHISM UTILITY CLASSES
| Consistent glassmorphism effects for UI chrome elements
| Standardized with 20px blur and proper opacity levels
========================================
*/

/* Header/Footer Glassmorphism - 30% opacity as preferred */
.glassmorphism-header {
  background: rgba(246, 247, 246, 0.3); /* Light mode: 30% opacity */
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(124, 154, 133, 0.2);
  box-shadow: 0 8px 32px 0 rgba(44, 49, 55, 0.1);
}

/* Light glassmorphism for subtle effects */
.glassmorphism-light {
  background: rgba(245, 250, 247, 0.15);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(124, 154, 133, 0.2);
  box-shadow: 0 8px 32px 0 rgba(44, 49, 55, 0.1);
}

/* Medium glassmorphism for cards and modals */
.glassmorphism-medium {
  background: rgba(245, 250, 247, 0.25);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(124, 154, 133, 0.15);
  box-shadow: 0 8px 32px 0 rgba(44, 49, 55, 0.2);
}

/* Heavy glassmorphism for overlays and important elements */
.glassmorphism-heavy {
  background: rgba(245, 250, 247, 0.4);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(124, 154, 133, 0.1);
  box-shadow: 0 12px 40px 0 rgba(44, 49, 55, 0.3);
}

/* Card glassmorphism for content cards */
.glassmorphism-card {
  background: var(--master-card-bg-light);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(124, 154, 133, 0.1);
  box-shadow: 0 4px 24px 0 rgba(44, 49, 55, 0.15);
}

/* Dark mode glassmorphism adjustments */
.dark .glassmorphism-header {
  background: rgba(45, 42, 38, 0.3); /* Dark mode: 30% opacity */
  border: 1px solid rgba(169, 162, 153, 0.2);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.2);
}

.dark .glassmorphism-light {
  background: rgba(45, 42, 38, 0.15);
  border: 1px solid rgba(169, 162, 153, 0.2);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.1);
}

.dark .glassmorphism-medium {
  background: rgba(45, 42, 38, 0.25);
  border: 1px solid rgba(169, 162, 153, 0.15);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.2);
}

.dark .glassmorphism-heavy {
  background: rgba(45, 42, 38, 0.4);
  border: 1px solid rgba(169, 162, 153, 0.1);
  box-shadow: 0 12px 40px 0 rgba(0, 0, 0, 0.3);
}

.dark .glassmorphism-card {
  background: var(--master-card-bg-dark);
  border: 1px solid rgba(169, 162, 153, 0.1);
  box-shadow: 0 4px 24px 0 rgba(0, 0, 0, 0.15);
}

/* Fallback for browsers that don't support backdrop-filter */
@supports not (backdrop-filter: blur(1px)) {
  .glassmorphism-header,
  .glassmorphism-light,
  .glassmorphism-medium,
  .glassmorphism-heavy {
    background: rgba(245, 250, 247, 0.9);
  }

  .glassmorphism-card {
    background: var(--master-card-bg-light);
  }

  .dark .glassmorphism-header,
  .dark .glassmorphism-light,
  .dark .glassmorphism-medium,
  .dark .glassmorphism-heavy {
    background: rgba(45, 42, 38, 0.9);
  }

  .dark .glassmorphism-card {
    background: var(--master-card-bg-dark);
  }
}

/*
========================================
| 📱 RESPONSIVE DESIGN & MOBILE OPTIMIZATIONS
========================================
*/

/* Mobile-First Responsive Typography */
@media screen and (max-width: 767px) {
  :root {
    /* Override typography scale for mobile */
    --font-size-h1: var(--font-size-h1-mobile);
    --font-size-h2: var(--font-size-h2-mobile);
    --font-size-h3: var(--font-size-h3-mobile);
    --font-size-h4: var(--font-size-h4-mobile);
    --font-size-h5: var(--font-size-h5-mobile);
    --font-size-body: var(--font-size-body-mobile);
    --font-size-small: var(--font-size-small-mobile);

    /* Mobile Header Scaling - Increased by 11.5% from previous 33% reduction */
    --header-height: 3.713rem;       /* Increased from 3.33rem by 11.5% (59.41px) */
    --header-logo-size: 2.23rem;     /* Increased from 2rem by 11.5% (35.68px) */
    --header-nav-font-size: 0.836rem; /* Increased from 0.75rem by 11.5% (13.38px) */
    --header-button-height: 2.23rem; /* Increased from 2rem by 11.5% (35.68px) */
    --header-padding: 0.747rem;      /* Increased from 0.67rem by 11.5% (11.95px) */
  }
}

/* Dark Mode Theme Overrides - STRICT COLOR SEPARATION */
.dark {
  /* ========================================
   * CRITICAL: OVERRIDE ALL GREEN/SAGE COLORS IN DARK MODE
   * Replace with Gold/Champagne colors per guidelines
   ======================================== */

  /* Override Sage Colors to Gold in Dark Mode */
  --tw-text-sage: #B8956A !important;           /* Dark Mode: Sage text becomes Gold */
  --tw-bg-sage: #B8956A !important;             /* Dark Mode: Sage backgrounds become Gold */
  --tw-border-sage: #B8956A !important;         /* Dark Mode: Sage borders become Gold */
  --tw-from-sage: #B8956A !important;           /* Dark Mode: Sage gradients become Gold */
  --tw-to-sage: #A9A299 !important;             /* Dark Mode: Sage gradient ends become Champagne */

  /* Header/Navbar Dark Mode Colors - Modern Luxury Theme */
  --master-header-background: #2D2A26;                           /* Dark mode: Deep Charcoal (NO GREEN) */
  --master-header-backdrop-blur: 50%;                            /* 50% backdrop blur for proper glassmorphism */
  --master-header-border: rgba(169, 162, 153, 0.3);              /* Dark mode: Muted Gold/Champagne border (NO GREEN) */
  --master-header-logo-bg: linear-gradient(135deg, #B8956A, #A9A299); /* Dark mode: Gold to Champagne gradient */
  --master-header-logo-border: rgba(184, 149, 106, 0.3);         /* Dark mode: Vierla Gold border */
  --master-header-logo-icon: #2D2A26;                            /* Dark mode: Deep Charcoal icon for contrast */
  --master-header-brand-text: #F4F1E8;                           /* Dark mode: Warm Cream text (NO GREEN) */

  /* Footer Dark Mode Colors - NO GREEN/SAGE COLORS */
  --global-footer-bg: rgba(45, 42, 38, 0.9);                     /* Dark mode: Deep Charcoal footer (NO GREEN) */
  --global-footer-border: rgba(169, 162, 153, 0.3);              /* Dark mode: Muted Gold/Champagne border (NO GREEN) */
  --global-footer-text: #F4F1E8;                                 /* Dark mode: Warm Cream text (NO GREEN) */
  --global-footer-link: #F4F1E8;                                 /* Dark mode: Warm Cream links (NO GREEN) */
  --global-footer-link-hover: #B8956A;                           /* Dark mode: Vierla Gold hover (NO GREEN) */
  --global-cookie-bg: #2D2A26;                                   /* Dark mode: Deep Charcoal popup (NO GREEN) */
  --global-cookie-border: #B8956A;                               /* Dark mode: Vierla Gold border (NO GREEN) */
  --global-cookie-text: #F4F1E8;                                 /* Dark mode: Warm Cream text (NO GREEN) */
  --global-cookie-button-bg: #B8956A;                            /* Dark mode: Vierla Gold button (NO GREEN) */

  /* ======================================== */
  /* 🔮 UI CHROME MASTER CONTROLS - v1.5.6    */
  /* ======================================== */

  /* -- Header & Footer (Dark Mode) - Modern Luxury Theme -- */
  --header-footer-bg: rgba(45, 42, 38, 0.3); /* 30% opacity as preferred */
  --header-footer-backdrop-blur: blur(20px); /* Consistent 20px blur */
  --header-footer-border: rgba(169, 162, 153, 0.3); /* Muted Gold/Champagne border */

  /* -- Theme-Aware Icons (Dark Mode) - NO GREEN/SAGE COLORS -- */
  --icon-primary: #F4F1E8; /* Warm Cream (NO GREEN) */
  --icon-secondary: #A9A299; /* Muted Gold/Champagne (NO GREEN) */
  --icon-accent: #B8956A; /* Vierla Gold (NO GREEN) */
  --icon-on-accent: #2D2A26; /* Deep Charcoal for use on colored buttons */

  /* ----- 🎨 THEME-AWARE ICON COLORS - Dark Mode (NO GREEN) ----- */
  --icon-muted: #A9A299;                                         /* Dark mode: Muted Gold/Champagne for muted icons (NO GREEN) */
  --icon-on-dark: #2D2A26;                                       /* Dark mode: Deep Charcoal for icons on light backgrounds */

  /* ----- 🎨 THEME-AWARE COMPONENT COLORS (DARK MODE) ----- */
  --theme-primary: #B8956A;                                      /* Dark Mode: Vierla Gold for primary buttons/CTAs */
  --theme-secondary: #A9A299;                                    /* Dark Mode: Muted Gold/Champagne for secondary elements */

  /* ----- 🎴 BENTO GRID CARD CONTROLS - Dark Mode ----- */
  --card-bg: rgba(45, 42, 38, 0.80);                            /* Dark mode: Deep Charcoal with absolute 80% transparency */
  --shadow-card: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --accent-hover-overlay: rgba(184, 149, 106, 0.08);            /* Dark mode: slightly stronger gold overlay */
  --accent-bg: rgba(184, 149, 106, 0.20);                       /* Dark mode: accent background for icons */
  --border-accent: rgba(184, 149, 106, 0.4);                    /* Dark mode: accent border color */

  /* ----- 📝 FORM INPUT CONTROLS - Dark Mode ----- */
  --input-bg: rgba(24, 27, 25, 0.8);                            /* Dark mode: input background */
  --input-border: rgba(95, 109, 97, 0.3);                       /* Dark mode: input border */
  --input-placeholder: rgba(246, 247, 246, 0.5);                /* Dark mode: placeholder text */

  /* ----- 📝 WCAG-COMPLIANT TEXT COLORS - Dark Mode ----- */
  /* Primary Body Text */
  --text-primary: #F5FAF7;                                       /* Dark mode: Light Off-White for main text */
  --text-secondary: #F0E6D9;                                     /* Dark mode: Beige for secondary/muted text */
  --text-headlines: #F5FAF7;                                     /* Dark mode: Light Off-White for headlines */
  --text-links: #B8956A;                                         /* Dark mode: Gold for links */
  --text-links-hover: #A6845C;                                   /* Dark mode: Darker gold for link hover */

  /* ----- 🌙 AURORA BACKGROUND - Dark Mode Override - NO GREEN COLORS ----- */
  --aurora-bg: #2D2A26;                                          /* Deep Charcoal - matches Modern Luxury theme */
  --aurora-bg-dark: #333333;                                     /* Light Charcoal for variation */
  --aurora-stripe-light: rgba(244, 241, 232, 0.02);             /* Very subtle warm cream stripes */
  --aurora-stripe-dark: rgba(184, 149, 106, 0.15);              /* Vierla Gold for contrast (NO GREEN) */
  --aurora-flow-1: rgba(184, 149, 106, 0.12);                   /* Vierla Gold - visible but subtle (NO GREEN) */
  --aurora-flow-2: rgba(169, 162, 153, 0.08);                   /* Muted Gold/Champagne (NO GREEN) */
  --aurora-flow-3: rgba(75, 61, 107, 0.10);                     /* Deep Plum/Violet for luxury (NO GREEN) */
  --aurora-flow-4: rgba(45, 42, 38, 0.18);                      /* Deep charcoal variation */
  --aurora-flow-5: rgba(184, 149, 106, 0.06);                   /* Vierla Gold accent (NO GREEN) */
}

/* ========================================
 * CRITICAL: DARK MODE COLOR OVERRIDES
 * Override ALL green/sage colors with gold in dark mode
 ======================================== */
.dark .text-sage,
.dark .text-sage-500,
.dark .text-sage-400,
.dark .text-sage-600 {
  color: #B8956A !important; /* Vierla Gold instead of sage */
}

.dark .bg-sage,
.dark .bg-sage-500,
.dark .bg-sage-400,
.dark .bg-sage-600,
.dark .from-sage,
.dark .to-sage {
  background-color: #B8956A !important; /* Vierla Gold instead of sage */
}

.dark .border-sage,
.dark .border-sage-500,
.dark .border-sage-400,
.dark .border-sage-600 {
  border-color: #B8956A !important; /* Vierla Gold instead of sage */
}

.dark .from-sage\/20 {
  --tw-gradient-from: rgba(184, 149, 106, 0.2) !important; /* Gold instead of sage */
}

.dark .to-sage\/10 {
  --tw-gradient-to: rgba(184, 149, 106, 0.1) !important; /* Gold instead of sage */
}

.dark .border-sage\/30 {
  border-color: rgba(184, 149, 106, 0.3) !important; /* Gold instead of sage */
}

.dark .bg-sage\/20 {
  background-color: rgba(184, 149, 106, 0.2) !important; /* Gold instead of sage */
}

/* ========================================
 * CRITICAL: LIGHT MODE COLOR OVERRIDES
 * Override ALL gold colors with green/forest in light mode
 ======================================== */
:root:not(.dark) .text-gold,
:root:not(.dark) .text-muted-gold,
:root:not(.dark) .text-brand-gold {
  color: #364035 !important; /* Vierla Forest instead of gold */
}

/* Override sage colors to use dark sage green (Vierla Forest) in light mode */
:root:not(.dark) .text-sage,
:root:not(.dark) .text-sage-500,
:root:not(.dark) .text-sage-400,
:root:not(.dark) .text-sage-600 {
  color: #364035 !important; /* Vierla Forest (Dark Sage Green) instead of light sage */
}

:root:not(.dark) .bg-gold,
:root:not(.dark) .bg-muted-gold,
:root:not(.dark) .bg-brand-gold,
:root:not(.dark) .from-gold,
:root:not(.dark) .to-gold {
  background-color: #364035 !important; /* Vierla Forest instead of gold */
}

:root:not(.dark) .border-gold,
:root:not(.dark) .border-muted-gold,
:root:not(.dark) .border-brand-gold {
  border-color: #364035 !important; /* Vierla Forest instead of gold */
}

/* Override any gold opacity variants in light mode */
:root:not(.dark) .bg-gold\/20,
:root:not(.dark) .bg-muted-gold\/20 {
  background-color: rgba(54, 64, 53, 0.2) !important; /* Vierla Forest instead of gold */
}

:root:not(.dark) .border-gold\/30,
:root:not(.dark) .border-muted-gold\/30 {
  border-color: rgba(54, 64, 53, 0.3) !important; /* Vierla Forest instead of gold */
}

/* ========================================
 * THEME-AWARE PRIMARY COLOR SYSTEM
 * Light Mode: Vierla Forest, Dark Mode: Vierla Gold
 ======================================== */
:root:not(.dark) .text-theme-primary {
  color: #364035 !important; /* Light Mode: Vierla Forest */
}

:root:not(.dark) .bg-theme-primary {
  background-color: #364035 !important; /* Light Mode: Vierla Forest */
}

:root:not(.dark) .bg-theme-primary\/10 {
  background-color: rgba(54, 64, 53, 0.1) !important; /* Light Mode: Vierla Forest with opacity */
}

:root:not(.dark) .bg-theme-primary\/5 {
  background-color: rgba(54, 64, 53, 0.05) !important; /* Light Mode: Vierla Forest with opacity */
}

.dark .text-theme-primary {
  color: #B8956A !important; /* Dark Mode: Vierla Gold */
}

/* ========================================
 * MOBILE BENTO CARD BEHAVIOR
 * Two-tap behavior: first tap reveals content, second tap navigates
 ======================================== */
@media (max-width: 767px) {
  .mobile-bento-card {
    /* Disable default hover effects on mobile */
    transform: none !important;
    box-shadow: var(--shadow-card) !important;
  }

  .mobile-bento-card:hover {
    transform: none !important;
    box-shadow: var(--shadow-card) !important;
  }

  .mobile-bento-card:hover .mobile-bento-icon {
    transform: none !important;
  }

  .mobile-bento-card:hover .mobile-bento-title {
    opacity: 1 !important;
    transform: none !important;
  }

  .mobile-bento-card:hover .mobile-bento-description {
    opacity: 0 !important;
    transform: translateY(8px) !important;
  }

  .mobile-bento-card:hover .mobile-bento-overlay {
    opacity: 0 !important;
  }

  /* Active state (after first tap) */
  .mobile-bento-card:active,
  .mobile-bento-card.mobile-active {
    transform: scale(1.02) !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
  }

  .mobile-bento-card:active .mobile-bento-icon,
  .mobile-bento-card.mobile-active .mobile-bento-icon {
    transform: scale(1.1) !important;
  }

  .mobile-bento-card:active .mobile-bento-title,
  .mobile-bento-card.mobile-active .mobile-bento-title {
    opacity: 0 !important;
    transform: translateY(-8px) !important;
  }

  .mobile-bento-card:active .mobile-bento-description,
  .mobile-bento-card.mobile-active .mobile-bento-description {
    opacity: 1 !important;
    transform: translateY(0) !important;
  }

  .mobile-bento-card:active .mobile-bento-overlay,
  .mobile-bento-card.mobile-active .mobile-bento-overlay {
    opacity: 1 !important;
  }
}

.dark .bg-theme-primary {
  background-color: #B8956A !important; /* Dark Mode: Vierla Gold */
}

.dark .bg-theme-primary\/10 {
  background-color: rgba(184, 149, 106, 0.1) !important; /* Dark Mode: Vierla Gold with opacity */
}

.dark .bg-theme-primary\/5 {
  background-color: rgba(184, 149, 106, 0.05) !important; /* Dark Mode: Vierla Gold with opacity */
}

/* Mobile-specific improvements */
@media screen and (max-width: 767px) {
  body {
    overflow-x: hidden;
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%;
  }

  /* Improve touch targets for mobile */
  button, a, [role="button"] {
    min-height: 44px;
    min-width: 44px;
  }

  /* Mobile-friendly spacing */
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* Mobile navigation improvements */
  .nav-link {
    padding: 0.75rem 1rem;
    font-size: 1.125rem;
  }

  /* Mobile card spacing */
  .golden-glowing-card-container {
    margin: 0.75rem;
  }

  /* Mobile hero text adjustments */
  .hero-title {
    font-size: 2.5rem !important;
    line-height: 1.1;
  }

  .hero-subtitle {
    font-size: 1.125rem !important;
    line-height: 1.5;
  }
}

/* Tablet Responsive Design */
@media screen and (min-width: 768px) and (max-width: 1023px) {
  .container {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  /* Tablet-specific grid adjustments */
  .grid-responsive {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Desktop Responsive Design */
@media screen and (min-width: 1024px) {
  .container {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  /* Desktop-specific grid adjustments */
  .grid-responsive {
    grid-template-columns: repeat(3, 1fr);
  }
}

/*
========================================
| 🤖 ANDROID PERFORMANCE OPTIMIZATIONS
| Fixes for flickering and performance issues on Android devices
========================================
*/

/* Hardware acceleration for better performance */
.aurora-background,
.backdrop-blur-lg,
.backdrop-blur-md,
.backdrop-blur-sm {
  transform: translateZ(0);
  -webkit-transform: translateZ(0);
  will-change: transform;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  -webkit-perspective: 1000px;
  perspective: 1000px;
}

/* Reduce backdrop-filter complexity on Android */
@media screen and (max-width: 768px) {
  .backdrop-blur-lg {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }

  .backdrop-blur-md {
    backdrop-filter: blur(6px);
    -webkit-backdrop-filter: blur(6px);
  }

  .backdrop-blur-sm {
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
  }
}

/* Prevent flickering during animations */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Optimize aurora background for mobile */
@media screen and (max-width: 768px) {
  .aurora-background {
    opacity: 0.7;
    filter: blur(8px);
  }

  .aurora-background::after {
    animation-duration: 120s; /* Slower animation for better performance */
  }
}

/*
========================================
| 📱 MOBILE & SAFARI SPECIFIC FIXES
========================================
*/
@media screen and (orientation: landscape) {
  html, body {
    min-height: 100lvh !important;
  }
}

@supports (padding: max(0px)) {
  html {
    padding-top: max(var(--safe-area-inset-top), 0px);
    padding-left: max(var(--safe-area-inset-left), 0px);
    padding-right: max(var(--safe-area-inset-right), 0px);
    padding-bottom: max(var(--safe-area-inset-bottom), 0px);
  }
}

@supports (-webkit-touch-callout: none) {
  html, body {
    height: -webkit-fill-available;
  }
}
