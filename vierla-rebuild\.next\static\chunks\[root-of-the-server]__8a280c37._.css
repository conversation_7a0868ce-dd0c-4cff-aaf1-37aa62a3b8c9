/* [next]/internal/font/google/jost_cd42f72b.module.css [app-client] (css) */
@font-face {
  font-family: Jost;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/84530c28ff9c87e6-s.55edd5f8.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Jost;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/853c5792ac9a8261-s.506cdeeb.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Jost;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/361ada22a6fc641e-s.p.ba172912.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Jost;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/84530c28ff9c87e6-s.55edd5f8.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Jost;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/853c5792ac9a8261-s.506cdeeb.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Jost;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/361ada22a6fc641e-s.p.ba172912.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Jost;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/84530c28ff9c87e6-s.55edd5f8.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Jost;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/853c5792ac9a8261-s.506cdeeb.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Jost;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/361ada22a6fc641e-s.p.ba172912.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Jost;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/84530c28ff9c87e6-s.55edd5f8.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Jost;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/853c5792ac9a8261-s.506cdeeb.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Jost;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/361ada22a6fc641e-s.p.ba172912.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Jost Fallback;
  src: local(Arial);
  ascent-override: 111.45%;
  descent-override: 39.06%;
  line-gap-override: 0.0%;
  size-adjust: 96.01%;
}

.jost_cd42f72b-module__Wo-Ogq__className {
  font-family: Jost, Jost Fallback;
  font-style: normal;
}

.jost_cd42f72b-module__Wo-Ogq__variable {
  --font-jost: "Jost", "Jost Fallback";
}

/* [next]/internal/font/google/inter_3c883839.module.css [app-client] (css) */
@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/2a2d10660758e7fa-s.91b7455f.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/d6f0f7ef0a66b318-s.927aef78.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/c0062fcfb5f4a9e6-s.b7398c1c.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/1a97932d2ea76c90-s.ac666cb5.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/e27fd546b8a0677f-s.569fab99.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/a973f82a0d056f9e-s.99c7dd4e.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/06ba6ef833b337bc-s.p.0faac26c.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/2a2d10660758e7fa-s.91b7455f.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/d6f0f7ef0a66b318-s.927aef78.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/c0062fcfb5f4a9e6-s.b7398c1c.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/1a97932d2ea76c90-s.ac666cb5.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/e27fd546b8a0677f-s.569fab99.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/a973f82a0d056f9e-s.99c7dd4e.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/06ba6ef833b337bc-s.p.0faac26c.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/2a2d10660758e7fa-s.91b7455f.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/d6f0f7ef0a66b318-s.927aef78.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/c0062fcfb5f4a9e6-s.b7398c1c.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/1a97932d2ea76c90-s.ac666cb5.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/e27fd546b8a0677f-s.569fab99.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/a973f82a0d056f9e-s.99c7dd4e.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/06ba6ef833b337bc-s.p.0faac26c.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/2a2d10660758e7fa-s.91b7455f.woff2") format("woff2");
  unicode-range: U+460-52F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/d6f0f7ef0a66b318-s.927aef78.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/c0062fcfb5f4a9e6-s.b7398c1c.woff2") format("woff2");
  unicode-range: U+1F??;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/1a97932d2ea76c90-s.ac666cb5.woff2") format("woff2");
  unicode-range: U+370-377, U+37A-37F, U+384-38A, U+38C, U+38E-3A1, U+3A3-3FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/e27fd546b8a0677f-s.569fab99.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/a973f82a0d056f9e-s.99c7dd4e.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Inter;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/06ba6ef833b337bc-s.p.0faac26c.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Inter Fallback;
  src: local(Arial);
  ascent-override: 90.44%;
  descent-override: 22.52%;
  line-gap-override: 0.0%;
  size-adjust: 107.12%;
}

.inter_3c883839-module__u_8pEG__className {
  font-family: Inter, Inter Fallback;
  font-style: normal;
}

.inter_3c883839-module__u_8pEG__variable {
  --font-inter: "Inter", "Inter Fallback";
}

/* [next]/internal/font/google/playfair_display_70aae4f1.module.css [app-client] (css) */
@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/69e3c8cfe83819b6-s.4b44f2b9.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/0912cdbb44e8f752-s.390b23fc.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/6b2ebfdb5aabfad9-s.5aa3982d.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/0a3c6a62758faac5-s.p.3ce64794.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/69e3c8cfe83819b6-s.4b44f2b9.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/0912cdbb44e8f752-s.390b23fc.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/6b2ebfdb5aabfad9-s.5aa3982d.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/0a3c6a62758faac5-s.p.3ce64794.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/69e3c8cfe83819b6-s.4b44f2b9.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/0912cdbb44e8f752-s.390b23fc.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/6b2ebfdb5aabfad9-s.5aa3982d.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/0a3c6a62758faac5-s.p.3ce64794.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/69e3c8cfe83819b6-s.4b44f2b9.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/0912cdbb44e8f752-s.390b23fc.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/6b2ebfdb5aabfad9-s.5aa3982d.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Playfair Display;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/0a3c6a62758faac5-s.p.3ce64794.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Playfair Display Fallback;
  src: local(Times New Roman);
  ascent-override: 97.25%;
  descent-override: 22.56%;
  line-gap-override: 0.0%;
  size-adjust: 111.26%;
}

.playfair_display_70aae4f1-module__fh3YGq__className {
  font-family: Playfair Display, Playfair Display Fallback;
  font-style: normal;
}

.playfair_display_70aae4f1-module__fh3YGq__variable {
  --font-tai-heritage: "Playfair Display", "Playfair Display Fallback";
}

/* [next]/internal/font/google/dancing_script_13037ae7.module.css [app-client] (css) */
@font-face {
  font-family: Dancing Script;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/57b784c92e25366c-s.4d3382ad.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Dancing Script;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/a52ffde7e3bcfb93-s.a8293835.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Dancing Script;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/48756ae8ddf4ca79-s.p.b553b83a.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Dancing Script;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/57b784c92e25366c-s.4d3382ad.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Dancing Script;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/a52ffde7e3bcfb93-s.a8293835.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Dancing Script;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/48756ae8ddf4ca79-s.p.b553b83a.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Dancing Script;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/57b784c92e25366c-s.4d3382ad.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Dancing Script;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/a52ffde7e3bcfb93-s.a8293835.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Dancing Script;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/48756ae8ddf4ca79-s.p.b553b83a.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Dancing Script;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/57b784c92e25366c-s.4d3382ad.woff2") format("woff2");
  unicode-range: U+102-103, U+110-111, U+128-129, U+168-169, U+1A0-1A1, U+1AF-1B0, U+300-301, U+303-304, U+308-309, U+323, U+329, U+1EA0-1EF9, U+20AB;
}

@font-face {
  font-family: Dancing Script;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/a52ffde7e3bcfb93-s.a8293835.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Dancing Script;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/48756ae8ddf4ca79-s.p.b553b83a.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Dancing Script Fallback;
  src: local(Arial);
  ascent-override: 112.99%;
  descent-override: 34.39%;
  line-gap-override: 0.0%;
  size-adjust: 81.43%;
}

.dancing_script_13037ae7-module__EJGhma__className {
  font-family: Dancing Script, Dancing Script Fallback;
  font-style: normal;
}

.dancing_script_13037ae7-module__EJGhma__variable {
  --font-farsan: "Dancing Script", "Dancing Script Fallback";
}

/* [project]/app/globals.css [app-client] (css) */
*, :before, :after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x: ;
  --tw-pan-y: ;
  --tw-pinch-zoom: ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position: ;
  --tw-gradient-via-position: ;
  --tw-gradient-to-position: ;
  --tw-ordinal: ;
  --tw-slashed-zero: ;
  --tw-numeric-figure: ;
  --tw-numeric-spacing: ;
  --tw-numeric-fraction: ;
  --tw-ring-inset: ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgba(59, 130, 246, .5);
  --tw-ring-offset-shadow: 0 0 rgba(0, 0, 0, 0);
  --tw-ring-shadow: 0 0 rgba(0, 0, 0, 0);
  --tw-shadow: 0 0 rgba(0, 0, 0, 0);
  --tw-shadow-colored: 0 0 rgba(0, 0, 0, 0);
  --tw-blur: ;
  --tw-brightness: ;
  --tw-contrast: ;
  --tw-grayscale: ;
  --tw-hue-rotate: ;
  --tw-invert: ;
  --tw-saturate: ;
  --tw-sepia: ;
  --tw-drop-shadow: ;
  --tw-backdrop-blur: ;
  --tw-backdrop-brightness: ;
  --tw-backdrop-contrast: ;
  --tw-backdrop-grayscale: ;
  --tw-backdrop-hue-rotate: ;
  --tw-backdrop-invert: ;
  --tw-backdrop-opacity: ;
  --tw-backdrop-saturate: ;
  --tw-backdrop-sepia: ;
  --tw-contain-size: ;
  --tw-contain-layout: ;
  --tw-contain-paint: ;
  --tw-contain-style: ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x: ;
  --tw-pan-y: ;
  --tw-pinch-zoom: ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position: ;
  --tw-gradient-via-position: ;
  --tw-gradient-to-position: ;
  --tw-ordinal: ;
  --tw-slashed-zero: ;
  --tw-numeric-figure: ;
  --tw-numeric-spacing: ;
  --tw-numeric-fraction: ;
  --tw-ring-inset: ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgba(59, 130, 246, .5);
  --tw-ring-offset-shadow: 0 0 rgba(0, 0, 0, 0);
  --tw-ring-shadow: 0 0 rgba(0, 0, 0, 0);
  --tw-shadow: 0 0 rgba(0, 0, 0, 0);
  --tw-shadow-colored: 0 0 rgba(0, 0, 0, 0);
  --tw-blur: ;
  --tw-brightness: ;
  --tw-contrast: ;
  --tw-grayscale: ;
  --tw-hue-rotate: ;
  --tw-invert: ;
  --tw-saturate: ;
  --tw-sepia: ;
  --tw-drop-shadow: ;
  --tw-backdrop-blur: ;
  --tw-backdrop-brightness: ;
  --tw-backdrop-contrast: ;
  --tw-backdrop-grayscale: ;
  --tw-backdrop-hue-rotate: ;
  --tw-backdrop-invert: ;
  --tw-backdrop-opacity: ;
  --tw-backdrop-saturate: ;
  --tw-backdrop-sepia: ;
  --tw-contain-size: ;
  --tw-contain-layout: ;
  --tw-contain-paint: ;
  --tw-contain-style: ;
}

*, :before, :after {
  box-sizing: border-box;
  border: 0 solid #e5e7eb;
}

:before, :after {
  --tw-content: "";
}

html, :host {
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
  line-height: 1.5;
  font-family: var(--font-inter), Inter, system-ui, sans-serif;
  font-feature-settings: normal;
  font-variation-settings: normal;
  -webkit-tap-highlight-color: transparent;
}

body {
  line-height: inherit;
  margin: 0;
}

hr {
  height: 0;
  color: inherit;
  border-top-width: 1px;
}

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
  text-decoration: underline dotted;
}

h1, h2, h3, h4, h5, h6 {
  font-size: inherit;
  font-weight: inherit;
}

a {
  color: inherit;
  -webkit-text-decoration: inherit;
  text-decoration: inherit;
}

b, strong {
  font-weight: bolder;
}

code, kbd, samp, pre {
  font-feature-settings: normal;
  font-variation-settings: normal;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;
  font-size: 1em;
}

small {
  font-size: 80%;
}

sub, sup {
  vertical-align: baseline;
  font-size: 75%;
  line-height: 0;
  position: relative;
}

sub {
  bottom: -.25em;
}

sup {
  top: -.5em;
}

table {
  text-indent: 0;
  border-color: inherit;
  border-collapse: collapse;
}

button, input, optgroup, select, textarea {
  font-feature-settings: inherit;
  font-variation-settings: inherit;
  font-family: inherit;
  font-size: 100%;
  font-weight: inherit;
  line-height: inherit;
  letter-spacing: inherit;
  color: inherit;
  margin: 0;
  padding: 0;
}

button, select {
  text-transform: none;
}

button {
  -webkit-appearance: button;
  background-color: rgba(0, 0, 0, 0);
  background-image: none;
}

input:where([type="button"]) {
  -webkit-appearance: button;
  background-color: rgba(0, 0, 0, 0);
  background-image: none;
}

input:where([type="reset"]) {
  -webkit-appearance: button;
  background-color: rgba(0, 0, 0, 0);
  background-image: none;
}

input:where([type="submit"]) {
  -webkit-appearance: button;
  background-color: rgba(0, 0, 0, 0);
  background-image: none;
}

:-moz-focusring {
  outline: auto;
}

:-moz-ui-invalid {
  box-shadow: none;
}

progress {
  vertical-align: baseline;
}

::-webkit-inner-spin-button {
  height: auto;
}

::-webkit-outer-spin-button {
  height: auto;
}

[type="search"] {
  -webkit-appearance: textfield;
  outline-offset: -2px;
}

::-webkit-search-decoration {
  -webkit-appearance: none;
}

::-webkit-file-upload-button {
  -webkit-appearance: button;
  font: inherit;
}

summary {
  display: list-item;
}

blockquote, dl, dd, h1, h2, h3, h4, h5, h6, hr, figure, p, pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol, ul, menu {
  margin: 0;
  padding: 0;
  list-style: none;
}

dialog {
  padding: 0;
}

textarea {
  resize: vertical;
}

input::placeholder, textarea::placeholder {
  opacity: 1;
  color: #9ca3af;
}

button, [role="button"] {
  cursor: pointer;
}

:disabled {
  cursor: default;
}

img, svg, video, canvas, audio, iframe, embed, object {
  vertical-align: middle;
  display: block;
}

img, video {
  max-width: 100%;
  height: auto;
}

[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}

:root {
  --inherit: inherit;
  --current: currentColor;
  --transparent: transparent;
  --black: #000;
  --white: #fff;
  --slate-50: #f8fafc;
  --slate-100: #f1f5f9;
  --slate-200: #e2e8f0;
  --slate-300: #cbd5e1;
  --slate-400: #94a3b8;
  --slate-500: #64748b;
  --slate-600: #475569;
  --slate-700: #334155;
  --slate-800: #1e293b;
  --slate-900: #0f172a;
  --slate-950: #020617;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  --gray-950: #030712;
  --zinc-50: #fafafa;
  --zinc-100: #f4f4f5;
  --zinc-200: #e4e4e7;
  --zinc-300: #d4d4d8;
  --zinc-400: #a1a1aa;
  --zinc-500: #71717a;
  --zinc-600: #52525b;
  --zinc-700: #3f3f46;
  --zinc-800: #27272a;
  --zinc-900: #18181b;
  --zinc-950: #09090b;
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #e5e5e5;
  --neutral-300: #d4d4d4;
  --neutral-400: #a3a3a3;
  --neutral-500: #737373;
  --neutral-600: #525252;
  --neutral-700: #404040;
  --neutral-800: #262626;
  --neutral-900: #171717;
  --neutral-950: #0a0a0a;
  --stone-50: #fafaf9;
  --stone-100: #f5f5f4;
  --stone-200: #e7e5e4;
  --stone-300: #d6d3d1;
  --stone-400: #a8a29e;
  --stone-500: #78716c;
  --stone-600: #57534e;
  --stone-700: #44403c;
  --stone-800: #292524;
  --stone-900: #1c1917;
  --stone-950: #0c0a09;
  --red-50: #fef2f2;
  --red-100: #fee2e2;
  --red-200: #fecaca;
  --red-300: #fca5a5;
  --red-400: #f87171;
  --red-500: #ef4444;
  --red-600: #dc2626;
  --red-700: #b91c1c;
  --red-800: #991b1b;
  --red-900: #7f1d1d;
  --red-950: #450a0a;
  --orange-50: #fff7ed;
  --orange-100: #ffedd5;
  --orange-200: #fed7aa;
  --orange-300: #fdba74;
  --orange-400: #fb923c;
  --orange-500: #f97316;
  --orange-600: #ea580c;
  --orange-700: #c2410c;
  --orange-800: #9a3412;
  --orange-900: #7c2d12;
  --orange-950: #431407;
  --amber-50: #fffbeb;
  --amber-100: #fef3c7;
  --amber-200: #fde68a;
  --amber-300: #fcd34d;
  --amber-400: #fbbf24;
  --amber-500: #f59e0b;
  --amber-600: #d97706;
  --amber-700: #b45309;
  --amber-800: #92400e;
  --amber-900: #78350f;
  --amber-950: #451a03;
  --yellow-50: #fefce8;
  --yellow-100: #fef9c3;
  --yellow-200: #fef08a;
  --yellow-300: #fde047;
  --yellow-400: #facc15;
  --yellow-500: #eab308;
  --yellow-600: #ca8a04;
  --yellow-700: #a16207;
  --yellow-800: #854d0e;
  --yellow-900: #713f12;
  --yellow-950: #422006;
  --lime-50: #f7fee7;
  --lime-100: #ecfccb;
  --lime-200: #d9f99d;
  --lime-300: #bef264;
  --lime-400: #a3e635;
  --lime-500: #84cc16;
  --lime-600: #65a30d;
  --lime-700: #4d7c0f;
  --lime-800: #3f6212;
  --lime-900: #365314;
  --lime-950: #1a2e05;
  --green-50: #f0fdf4;
  --green-100: #dcfce7;
  --green-200: #bbf7d0;
  --green-300: #86efac;
  --green-400: #4ade80;
  --green-500: #22c55e;
  --green-600: #16a34a;
  --green-700: #15803d;
  --green-800: #166534;
  --green-900: #14532d;
  --green-950: #052e16;
  --emerald-50: #ecfdf5;
  --emerald-100: #d1fae5;
  --emerald-200: #a7f3d0;
  --emerald-300: #6ee7b7;
  --emerald-400: #34d399;
  --emerald-500: #10b981;
  --emerald-600: #059669;
  --emerald-700: #047857;
  --emerald-800: #065f46;
  --emerald-900: #064e3b;
  --emerald-950: #022c22;
  --teal-50: #f0fdfa;
  --teal-100: #ccfbf1;
  --teal-200: #99f6e4;
  --teal-300: #5eead4;
  --teal-400: #2dd4bf;
  --teal-500: #14b8a6;
  --teal-600: #0d9488;
  --teal-700: #0f766e;
  --teal-800: #115e59;
  --teal-900: #134e4a;
  --teal-950: #042f2e;
  --cyan-50: #ecfeff;
  --cyan-100: #cffafe;
  --cyan-200: #a5f3fc;
  --cyan-300: #67e8f9;
  --cyan-400: #22d3ee;
  --cyan-500: #06b6d4;
  --cyan-600: #0891b2;
  --cyan-700: #0e7490;
  --cyan-800: #155e75;
  --cyan-900: #164e63;
  --cyan-950: #083344;
  --sky-50: #f0f9ff;
  --sky-100: #e0f2fe;
  --sky-200: #bae6fd;
  --sky-300: #7dd3fc;
  --sky-400: #38bdf8;
  --sky-500: #0ea5e9;
  --sky-600: #0284c7;
  --sky-700: #0369a1;
  --sky-800: #075985;
  --sky-900: #0c4a6e;
  --sky-950: #082f49;
  --blue-50: #eff6ff;
  --blue-100: #dbeafe;
  --blue-200: #bfdbfe;
  --blue-300: #93c5fd;
  --blue-400: #60a5fa;
  --blue-500: #3b82f6;
  --blue-600: #2563eb;
  --blue-700: #1d4ed8;
  --blue-800: #1e40af;
  --blue-900: #1e3a8a;
  --blue-950: #172554;
  --indigo-50: #eef2ff;
  --indigo-100: #e0e7ff;
  --indigo-200: #c7d2fe;
  --indigo-300: #a5b4fc;
  --indigo-400: #818cf8;
  --indigo-500: #6366f1;
  --indigo-600: #4f46e5;
  --indigo-700: #4338ca;
  --indigo-800: #3730a3;
  --indigo-900: #312e81;
  --indigo-950: #1e1b4b;
  --violet-50: #f5f3ff;
  --violet-100: #ede9fe;
  --violet-200: #ddd6fe;
  --violet-300: #c4b5fd;
  --violet-400: #a78bfa;
  --violet-500: #8b5cf6;
  --violet-600: #7c3aed;
  --violet-700: #6d28d9;
  --violet-800: #5b21b6;
  --violet-900: #4c1d95;
  --violet-950: #2e1065;
  --purple-50: #faf5ff;
  --purple-100: #f3e8ff;
  --purple-200: #e9d5ff;
  --purple-300: #d8b4fe;
  --purple-400: #c084fc;
  --purple-500: #a855f7;
  --purple-600: #9333ea;
  --purple-700: #7e22ce;
  --purple-800: #6b21a8;
  --purple-900: #581c87;
  --purple-950: #3b0764;
  --fuchsia-50: #fdf4ff;
  --fuchsia-100: #fae8ff;
  --fuchsia-200: #f5d0fe;
  --fuchsia-300: #f0abfc;
  --fuchsia-400: #e879f9;
  --fuchsia-500: #d946ef;
  --fuchsia-600: #c026d3;
  --fuchsia-700: #a21caf;
  --fuchsia-800: #86198f;
  --fuchsia-900: #701a75;
  --fuchsia-950: #4a044e;
  --pink-50: #fdf2f8;
  --pink-100: #fce7f3;
  --pink-200: #fbcfe8;
  --pink-300: #f9a8d4;
  --pink-400: #f472b6;
  --pink-500: #ec4899;
  --pink-600: #db2777;
  --pink-700: #be185d;
  --pink-800: #9d174d;
  --pink-900: #831843;
  --pink-950: #500724;
  --rose-50: #fff1f2;
  --rose-100: #ffe4e6;
  --rose-200: #fecdd3;
  --rose-300: #fda4af;
  --rose-400: #fb7185;
  --rose-500: #f43f5e;
  --rose-600: #e11d48;
  --rose-700: #be123c;
  --rose-800: #9f1239;
  --rose-900: #881337;
  --rose-950: #4c0519;
  --natural-wellness-background: #f4f1e8;
  --natural-wellness-text-primary: #2d2a26;
  --natural-wellness-accent-primary: #364035;
  --natural-wellness-accent-secondary: #8b9a8c;
  --natural-wellness-accent-energetic: #7c9a85;
  --modern-luxury-background: #2d2a26;
  --modern-luxury-text-primary: #f4f1e8;
  --modern-luxury-accent-primary: #b8956a;
  --modern-luxury-accent-secondary: #a9a299;
  --vierla-sage: #8b9a8c;
  --vierla-gold: #b8956a;
  --vierla-forest: #364035;
  --brand-sage: #8b9a8c;
  --brand-gold: #b8956a;
  --brand-beige: #f0e6d9;
  --neutral-background-light: #f4f1e8;
  --neutral-text-primary-light: #2d2a26;
  --neutral-text-secondary-light: #8b9a8c;
  --neutral-borders-light: #b7c4b7;
  --neutral-background-dark: #2d2a26;
  --neutral-text-primary-dark: #f4f1e8;
  --neutral-text-secondary-dark: #8b9a8c;
  --neutral-borders-dark: #657d6d;
  --neutral-charcoal-dark: #2d2a26;
  --neutral-charcoal-light: #333;
  --neutral-off-white: #f4f1e8;
  --theme-primary-DEFAULT: #364035;
  --theme-primary-light: #364035;
  --theme-primary-dark: #b8956a;
  --theme-secondary-DEFAULT: #8b9a8c;
  --theme-secondary-light: #8b9a8c;
  --theme-secondary-dark: #a9a299;
  --sage-50: #f5f7f6;
  --sage-100: #eaefeb;
  --sage-200: #d4dfd6;
  --sage-300: #becfc1;
  --sage-400: #a8bfac;
  --sage-500: #8b9a8c;
  --sage-600: #7a8a7c;
  --sage-700: #697a6b;
  --sage-800: #586a5a;
  --sage-900: #475a49;
  --sage-DEFAULT: #8b9a8c;
  --gold-50: #faf7f2;
  --gold-100: #f5efe5;
  --gold-200: #ebdfcb;
  --gold-300: #e1cfb1;
  --gold-400: #d7bf97;
  --gold-500: #b8956a;
  --gold-600: #a6845c;
  --gold-700: #94734e;
  --gold-800: #826240;
  --gold-900: #705132;
  --gold-DEFAULT: #b8956a;
  --forest-50: #f4f5f4;
  --forest-100: #e8eae8;
  --forest-200: #d1d5d1;
  --forest-300: #bac0ba;
  --forest-400: #a3aaa3;
  --forest-500: #8c948c;
  --forest-600: #757d75;
  --forest-700: #5e665e;
  --forest-800: #475047;
  --forest-900: #364035;
  --forest-950: #2a302a;
  --forest-DEFAULT: #364035;
  --beige-50: #fefcfa;
  --beige-100: #f0e6d9;
  --beige-200: #e0d5c7;
  --beige-300: #d0c5b7;
  --beige-400: #c0b5a7;
  --beige-500: #b0a597;
  --beige-600: #a09587;
  --beige-700: #908577;
  --beige-800: #807567;
  --beige-900: #706557;
  --beige-DEFAULT: #f0e6d9;
  --charcoal-50: #f5f6f6;
  --charcoal-100: #e6e8e9;
  --charcoal-200: #cdd1d3;
  --charcoal-300: #b4babd;
  --charcoal-400: #9ba3a7;
  --charcoal-500: #828c91;
  --charcoal-600: #69757b;
  --charcoal-700: #505e65;
  --charcoal-800: #37474f;
  --charcoal-900: #333;
  --charcoal-950: #2c3137;
  --charcoal-DEFAULT: #2c3137;
  --success-50: #f4f7f5;
  --success-100: #e8efea;
  --success-200: #d1dfd4;
  --success-300: #bacfbe;
  --success-400: #a3bfa8;
  --success-500: #7c9a85;
  --success-600: #6b8a74;
  --success-700: #5a7963;
  --success-800: #496852;
  --success-900: #3a5441;
  --success-DEFAULT: #7c9a85;
  --warning-50: #faf7f2;
  --warning-100: #f5efe5;
  --warning-200: #ebdfcb;
  --warning-300: #e1cfb1;
  --warning-400: #d7bf97;
  --warning-500: #b8956a;
  --warning-600: #a6845c;
  --warning-700: #94734e;
  --warning-800: #826240;
  --warning-900: #705132;
  --warning-DEFAULT: #b8956a;
  --error-50: #fef7ed;
  --error-100: #fdedd3;
  --error-200: #fbd5a5;
  --error-300: #f9bc6d;
  --error-400: #f59e0b;
  --error-500: #d97706;
  --error-600: #c2410c;
  --error-700: #9a3412;
  --error-800: #7c2d12;
  --error-900: #431407;
  --error-DEFAULT: #d97706;
  --mantle-50: #f5faf7;
  --mantle-100: #f0e6d9;
  --mantle-200: #e0d5c7;
  --mantle-300: #d0c5b7;
  --mantle-400: #7c9a85;
  --mantle-500: #6b8a74;
  --mantle-600: #5a7963;
  --mantle-700: #496852;
  --mantle-800: #3a5441;
  --mantle-900: #333;
  --mantle-950: #2c3137;
  --border: hsl(var(--border));
  --input: hsl(var(--input));
  --ring: hsl(var(--ring));
  --background: hsl(var(--background));
  --foreground: hsl(var(--foreground));
  --primary-DEFAULT: hsl(var(--primary));
  --primary-foreground: hsl(var(--primary-foreground));
  --secondary-DEFAULT: hsl(var(--secondary));
  --secondary-foreground: hsl(var(--secondary-foreground));
  --destructive-DEFAULT: hsl(var(--destructive));
  --destructive-foreground: hsl(var(--destructive-foreground));
  --muted-DEFAULT: hsl(var(--muted));
  --muted-foreground: hsl(var(--muted-foreground));
  --accent-DEFAULT: hsl(var(--accent));
  --accent-foreground: hsl(var(--accent-foreground));
  --popover-DEFAULT: hsl(var(--popover));
  --popover-foreground: hsl(var(--popover-foreground));
  --card-DEFAULT: hsl(var(--card));
  --card-foreground: hsl(var(--card-foreground));
  --aurora-bg: #f4f1e8;
  --aurora-stripes: repeating-linear-gradient(100deg, #f4f1e8 0%, #f4f1e8 7%, transparent 10%, transparent 12%, #f4f1e8 16%);
  --aurora-flow: repeating-linear-gradient(100deg, #f4f1e8 10%, #364035 15%, #8b9a8c 20%, #f0e6d9 25%, #7c9a85 30%);
  --aurora-feature-bg: #f4f1e8;
  --aurora-feature-stripes: repeating-linear-gradient(100deg, #f4f1e8 0%, #f4f1e8 7%, transparent 10%, transparent 12%, #f4f1e8 16%);
  --aurora-feature-flow: repeating-linear-gradient(100deg, rgba(244, 241, 232, .25) 10%, rgba(54, 64, 53, .2) 15%, rgba(139, 154, 140, .18) 20%, rgba(240, 230, 217, .22) 25%, rgba(124, 154, 133, .15) 30%);
}

.dark {
  --aurora-bg: #2d2a26;
  --aurora-stripes: repeating-linear-gradient(100deg, #2d2a26 0%, #2d2a26 7%, transparent 10%, transparent 12%, #2d2a26 16%);
  --aurora-flow: repeating-linear-gradient(100deg, rgba(45, 42, 38, .45) 10%, rgba(184, 149, 106, .35) 15%, rgba(75, 61, 107, .3) 20%, rgba(169, 162, 153, .25) 25%, rgba(45, 42, 38, .4) 30%);
  --aurora-feature-bg: #2d2a26;
  --aurora-feature-stripes: repeating-linear-gradient(100deg, #2d2a26 0%, #2d2a26 7%, transparent 10%, transparent 12%, #2d2a26 16%);
  --aurora-feature-flow: repeating-linear-gradient(100deg, rgba(45, 42, 38, .35) 10%, rgba(184, 149, 106, .25) 15%, rgba(75, 61, 107, .2) 20%, rgba(169, 162, 153, .18) 25%, rgba(45, 42, 38, .3) 30%);
}

:root {
  --safe-area-inset-top: env(safe-area-inset-top);
  --safe-area-inset-right: env(safe-area-inset-right);
  --safe-area-inset-bottom: env(safe-area-inset-bottom);
  --safe-area-inset-left: env(safe-area-inset-left);
  --master-brand-primary-light: #364035;
  --master-brand-primary-dark: #b8956a;
  --master-brand-secondary: #2d2a26;
  --master-brand-accent-light: #8b9a8c;
  --master-brand-accent-dark: #b8956a;
  --master-brand-forest: #364035;
  --master-brand-beige: #f0e6d9;
  --master-bg-light: #f4f1e8;
  --master-borders-light: #b7c4b7;
  --master-bg-dark: #2d2a26;
  --master-borders-dark: #b8956a;
  --master-accent-primary-dark: #b8956a;
  --master-accent-secondary-dark: #a9a299;
  --master-action-primary-light: #364035;
  --master-action-primary-dark: #b8956a;
  --master-action-hover-light: #2a302a;
  --master-action-hover-dark: #a6845c;
  --master-action-focus-light: #364035;
  --master-action-focus-dark: #b8956a;
  --master-action-secondary-light: #8b9a8c;
  --master-action-secondary-dark: #a9a299;
  --master-action-tertiary-light: #8b9a8c;
  --master-action-tertiary-dark: #a9a299;
  --master-success-light: #8b9a8c;
  --master-success-dark: #b8956a;
  --master-warning-light: #364035;
  --master-warning-dark: #b8956a;
  --master-error: #d97706;
  --master-text-primary-light: #2d2a26;
  --master-text-secondary-light: #8b9a8c;
  --master-text-primary-dark: #f4f1e8;
  --master-text-secondary-dark: #a9a299;
  --master-text-muted: #f0e6d9;
  --master-text-primary: #2d2a26;
  --master-text-secondary: #8b9a8c;
  --master-text-on-dark: #f4f1e8;
  --master-input-bg-light: #f4f1e8;
  --master-input-bg-dark: #333;
  --master-input-border-light: #8b9a8c;
  --master-input-border-dark: #a9a299;
  --master-input-border-focus-light: #364035;
  --master-input-border-focus-dark: #b8956a;
  --master-input-text: inherit;
  --master-label-color-light: #8b9a8c;
  --master-label-color-dark: #a9a299;
  --master-icon-primary-light: #364035;
  --master-icon-primary-dark: #b8956a;
  --master-icon-secondary-light: #8b9a8c;
  --master-icon-secondary-dark: #a9a299;
  --master-icon-muted-light: #8b9a8c;
  --master-icon-muted-dark: #a9a299;
  --master-icon-success-light: #8b9a8c;
  --master-icon-success-dark: #b8956a;
  --master-card-bg-light: #f4f1e8;
  --master-card-bg-dark: #333;
  --master-card-border-light: #8b9a8c;
  --master-card-border-dark: #a9a299;
  --master-card-border-hover-light: #364035;
  --master-card-border-hover-dark: #b8956a;
  --master-card-glow-light: #8b9a8c;
  --master-card-glow-dark: #b8956a;
  --master-card-accent-light: #364035;
  --master-card-accent-dark: #b8956a;
  --font-size-h1-original: 3.052rem;
  --font-size-h2-original: 2.441rem;
  --font-size-h3-original: 1.953rem;
  --font-size-h4-original: 1.563rem;
  --font-size-h5-original: 1.25rem;
  --font-size-body-original: 1rem;
  --font-size-small-original: .8rem;
  --font-size-h1: 2.035rem;
  --font-size-h2: 1.627rem;
  --font-size-h3: 1.302rem;
  --font-size-h4: 1.042rem;
  --font-size-h5: .833rem;
  --font-size-body: .667rem;
  --font-size-small: .533rem;
  --font-size-h1-mobile: 2.25rem;
  --font-size-h2-mobile: 1.875rem;
  --font-size-h3-mobile: 1.5rem;
  --font-size-h4-mobile: 1.25rem;
  --font-size-h5-mobile: 1.125rem;
  --font-size-body-mobile: .875rem;
  --font-size-small-mobile: .75rem;
  --font-family-h1: var(--font-jost);
  --font-family-h2: var(--font-jost);
  --font-family-h3: var(--font-tai-heritage);
  --font-family-body: var(--font-inter);
  --font-family-ui: var(--font-inter);
  --font-family-accent: var(--font-farsan);
  --space-1-original: .5rem;
  --space-2-original: 1rem;
  --space-3-original: 1.5rem;
  --space-4-original: 2rem;
  --space-5-original: 2.5rem;
  --space-6-original: 3rem;
  --space-7-original: 3.5rem;
  --space-8-original: 4rem;
  --space-9-original: 4.5rem;
  --space-10-original: 5rem;
  --space-12-original: 6rem;
  --space-16-original: 8rem;
  --space-20-original: 10rem;
  --space-24-original: 12rem;
  --space-1: .333rem;
  --space-2: .667rem;
  --space-3: 1rem;
  --space-4: 1.333rem;
  --space-5: 1.667rem;
  --space-6: 2rem;
  --space-7: 2.333rem;
  --space-8: 2.667rem;
  --space-9: 3rem;
  --space-10: 3.333rem;
  --space-12: 4rem;
  --space-16: 5.333rem;
  --space-20: 6.667rem;
  --space-24: 8rem;
  --master-button-primary-bg: #364035;
  --master-button-primary-text: #f4f1e8;
  --master-button-primary-hover: #2a302a;
  --master-button-secondary-bg: #8b9a8c;
  --master-button-secondary-text: #f4f1e8;
  --master-button-secondary-hover: #7a8a7c;
  --master-button-tertiary-bg: transparent;
  --master-button-tertiary-text: #8b9a8c;
  --master-button-tertiary-hover: underline;
  --master-card-background: #333;
  --master-card-border: rgba(139, 154, 140, .3);
  --master-card-glow: #b8956a;
  --master-header-background: #f6f7f6;
  --master-header-backdrop-blur: 50%;
  --master-header-border: rgba(139, 154, 140, .2);
  --master-header-logo-bg: linear-gradient(135deg, #364035, #8b9a8c);
  --master-header-logo-border: rgba(54, 64, 53, .3);
  --master-header-logo-icon: #f4f1e8;
  --master-header-brand-text: #181b19;
  --master-header-nav-text: #f4f1e8;
  --master-header-nav-text-muted: #b8956a;
  --master-header-nav-active-bg: rgba(124, 154, 133, .2);
  --master-header-nav-active-glow: transparent;
  --master-header-nav-hover-bg: rgba(184, 149, 106, .15);
  --home-hero-title-light: #2d2a26;
  --home-hero-title-dark: #f4f1e8;
  --home-hero-subtitle-light: #8b9a8c;
  --home-hero-subtitle-dark: #a9a299;
  --home-hero-cta-bg-light: #364035;
  --home-hero-cta-bg-dark: #b8956a;
  --home-hero-cta-text-light: #f4f1e8;
  --home-hero-cta-text-dark: #2d2a26;
  --home-card-background: #333;
  --home-card-border-light: rgba(139, 154, 140, .3);
  --home-card-border-dark: rgba(169, 162, 153, .3);
  --home-card-glow-light: #8b9a8c;
  --home-card-glow-dark: #b8956a;
  --home-shiny-button-bg-light: #364035;
  --home-shiny-button-bg-dark: #b8956a;
  --home-shiny-button-text-light: #f4f1e8;
  --home-shiny-button-text-dark: #2d2a26;
  --home-shiny-button-shimmer-light: #8b9a8c;
  --home-shiny-button-shimmer-dark: #f4f1e8;
  --home-text-shimmer-base: var(--master-text-on-dark);
  --home-text-shimmer-highlight: var(--master-brand-accent);
  --home-word-pullup-color: var(--mantle-100);
  --home-marquee-text: var(--mantle-100);
  --home-marquee-bg: transparent;
  --home-golden-card-bg: var(--master-card-background);
  --home-golden-card-border: var(--master-card-border);
  --home-golden-card-glow: var(--master-card-glow);
  --features-hero-title: #f5faf7;
  --features-hero-subtitle: #f0e6d9;
  --features-bento-card-bg: #333;
  --features-bento-card-border: rgba(124, 154, 133, .3);
  --features-bento-card-title: #f5faf7;
  --features-bento-card-text: #f0e6d9;
  --features-bento-icon-color: #7c9a85;
  --features-bento-grid-gap: 1.5rem;
  --features-cta-button-bg: #b8956a;
  --features-cta-button-text: #2c3137;
  --features-cta-button-hover: #a6845c;
  --features-shiny-button-bg: var(--master-button-primary-bg);
  --features-shiny-button-text: var(--master-button-primary-text);
  --features-shiny-button-shimmer: var(--master-brand-accent);
  --features-golden-card-bg: var(--master-card-background);
  --features-golden-card-border: var(--master-card-border);
  --features-golden-card-glow: var(--master-card-glow);
  --pricing-hero-title: #f5faf7;
  --pricing-hero-subtitle: #f0e6d9;
  --pricing-card-background: #333;
  --pricing-card-border: rgba(124, 154, 133, .3);
  --pricing-card-glow: #b8956a;
  --pricing-card-title: #f5faf7;
  --pricing-card-price: #b8956a;
  --pricing-card-text: #f0e6d9;
  --pricing-popular-bg: rgba(184, 149, 106, .1);
  --pricing-popular-border: #b8956a;
  --pricing-shiny-button-bg: #b8956a;
  --pricing-shiny-button-text: #2c3137;
  --pricing-shiny-button-shimmer: #f5faf7;
  --pricing-badge-bg: #7c9a85;
  --pricing-badge-text: #f5faf7;
  --about-hero-title: #f5faf7;
  --about-hero-subtitle: #f0e6d9;
  --about-card-background: #333;
  --about-card-border: rgba(124, 154, 133, .3);
  --about-card-glow: #b8956a;
  --about-team-card-bg: #333;
  --about-team-card-border: rgba(124, 154, 133, .2);
  --about-team-name: #f5faf7;
  --about-team-role: #7c9a85;
  --about-team-bio: #f0e6d9;
  --about-shiny-button-bg: #b8956a;
  --about-shiny-button-text: #2c3137;
  --about-shiny-button-shimmer: #f5faf7;
  --about-mission-bg: rgba(51, 51, 51, .5);
  --contact-hero-title: #f5faf7;
  --contact-hero-subtitle: #f0e6d9;
  --contact-form-bg: #333;
  --contact-form-border: rgba(124, 154, 133, .3);
  --contact-input-bg: rgba(240, 230, 217, .1);
  --contact-input-border: #7c9a85;
  --contact-input-text: #f5faf7;
  --contact-label-text: #f0e6d9;
  --contact-input-focus: #b8956a;
  --contact-shiny-button-bg: #b8956a;
  --contact-shiny-button-text: #2c3137;
  --contact-shiny-button-shimmer: #f5faf7;
  --global-footer-bg: rgba(225, 230, 225, .9);
  --global-footer-border: rgba(139, 154, 140, .3);
  --global-footer-text: #2e332f;
  --global-footer-link: #181b19;
  --global-footer-link-hover: #5f6d61;
  --global-cookie-bg: #f6f7f6;
  --global-cookie-border: #8b9a8c;
  --global-cookie-text: #181b19;
  --global-cookie-button-bg: #5f6d61;
  --header-footer-bg: rgba(244, 241, 232, .8);
  --header-footer-backdrop-blur: blur(16px);
  --header-footer-border: rgba(139, 154, 140, .3);
  --icon-primary: #2e332f;
  --icon-secondary: #5f6d61;
  --icon-accent: #79887a;
  --icon-on-accent: #f6f7f6;
  --icon-muted: #8b9a8c;
  --icon-on-dark: #f6f7f6;
  --global-cookie-button-text: #2c3137;
  --card-bg: rgba(246, 247, 246, .95);
  --shadow-card: 0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -1px rgba(0, 0, 0, .06);
  --accent-hover-overlay: rgba(139, 154, 140, .05);
  --accent-bg: rgba(139, 154, 140, .15);
  --border-accent: rgba(139, 154, 140, .3);
  --input-bg: rgba(246, 247, 246, .8);
  --input-border: rgba(139, 154, 140, .3);
  --input-placeholder: rgba(46, 51, 47, .6);
  --text-primary: #2c3137;
  --text-secondary: #7c9a85;
  --text-headlines: #2c3137;
  --text-links: #7c9a85;
  --text-links-hover: #6b8a73;
  --providers-hero-title: #f5faf7;
  --providers-hero-subtitle: #f0e6d9;
  --providers-card-bg: #333;
  --providers-card-border: rgba(124, 154, 133, .3);
  --providers-card-title: #f5faf7;
  --providers-card-text: #f0e6d9;
  --providers-icon-color: #7c9a85;
  --providers-cta-bg: #b8956a;
  --providers-cta-text: #2c3137;
  --providers-coming-soon-bg: #333;
  --apply-hero-title: #f5faf7;
  --apply-hero-subtitle: #f0e6d9;
  --apply-form-bg: #333;
  --apply-form-border: rgba(124, 154, 133, .3);
  --apply-progress-active: #7c9a85;
  --apply-progress-inactive: #333;
  --apply-progress-bar: #7c9a85;
  --apply-step-text: #f5faf7;
  --global-shiny-button-primary-bg: #b8956a;
  --global-shiny-button-primary-text: #2c3137;
  --global-shiny-button-primary-shimmer: #f5faf7;
  --global-shiny-button-secondary-bg: transparent;
  --global-shiny-button-secondary-text: #7c9a85;
  --global-shiny-button-secondary-shimmer: #7c9a85;
  --global-shimmer-button-primary-bg: #b8956a;
  --global-shimmer-button-primary-shimmer: #f5faf7;
  --global-shimmer-button-secondary-bg: transparent;
  --global-shimmer-button-secondary-shimmer: #7c9a85;
  --global-text-shimmer-base: #f5faf7;
  --global-text-shimmer-highlight: #b8956a;
  --global-golden-card-bg: #333;
  --global-golden-card-border: rgba(124, 154, 133, .3);
  --global-golden-card-glow: #b8956a;
  --background: 44 7% 17%;
  --foreground: 44 15% 95%;
  --card: 0 0% 20%;
  --card-foreground: 44 15% 95%;
  --popover: 0 0% 20%;
  --popover-foreground: 44 15% 95%;
  --primary: 40 35% 55%;
  --primary-foreground: 44 7% 17%;
  --secondary: 40 20% 65%;
  --secondary-foreground: 44 7% 17%;
  --muted: 44 7% 17%;
  --muted-foreground: 40 20% 65%;
  --accent: 40 35% 55%;
  --accent-foreground: 44 7% 17%;
  --destructive: 25 95% 53%;
  --destructive-foreground: 0 0% 98%;
  --border: 40 20% 65% / .3;
  --input: 40 20% 65% / .3;
  --ring: 40 35% 55%;
  --radius: .5rem;
}

.dark {
  --background: 44 12% 20%;
  --foreground: 120 20% 97%;
  --card: 0 0% 20%;
  --card-foreground: 120 20% 97%;
  --popover: 0 0% 20%;
  --popover-foreground: 120 20% 97%;
  --primary: 40 35% 55%;
  --primary-foreground: 44 7% 17%;
  --secondary: 40 20% 65%;
  --secondary-foreground: 44 7% 17%;
  --muted: 44 7% 17%;
  --muted-foreground: 40 20% 65%;
  --accent: 40 35% 55%;
  --accent-foreground: 44 7% 17%;
  --destructive: 25 95% 53%;
  --destructive-foreground: 0 0% 98%;
  --border: 40 20% 65% / .3;
  --input: 40 20% 65% / .3;
  --ring: 40 35% 55%;
  --master-brand-secondary: #2d2a26;
  --master-text-on-dark: #f4f1e8;
  --master-card-background: #333;
}

:root:not(.dark) {
  --background: 44 15% 95%;
  --foreground: 44 7% 17%;
  --card: 0 0% 95%;
  --card-foreground: 44 7% 17%;
  --popover: 0 0% 98%;
  --popover-foreground: 44 7% 17%;
  --primary: 150 8% 22%;
  --primary-foreground: 44 15% 95%;
  --secondary: 150 12% 55%;
  --secondary-foreground: 44 7% 17%;
  --muted: 0 0% 95%;
  --muted-foreground: 150 12% 55%;
  --accent: 150 8% 22%;
  --accent-foreground: 44 15% 95%;
  --destructive: 25 95% 53%;
  --destructive-foreground: 0 0% 98%;
  --border: 150 12% 55% / .3;
  --input: 150 12% 55% / .3;
  --ring: 150 8% 22%;
  --master-brand-secondary: #f4f1e8;
  --master-text-on-dark: #2d2a26;
  --master-card-background: #f4f1e8;
  --master-accent-primary-light: #364035;
  --master-accent-secondary-light: #8b9a8c;
}

* {
  border-color: hsl(var(--border));
}

body {
  color: var(--master-text-on-dark);
  font-family: var(--font-family-body);
  background-color: var(--master-brand-secondary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-size: var(--font-size-body);
}

@media (min-width: 768px) {
  .page-home, .page-about, .page-features, .page-pricing, .page-provider, .page-apply, .page-privacy, .page-terms {
    transform-origin: top;
    width: 111.11%;
    margin-left: -5.56%;
    transform: scale(.9);
  }
}

h1 {
  font-family: var(--font-family-h1);
  font-size: var(--font-size-h1);
  text-transform: uppercase;
  letter-spacing: .02em;
  font-weight: 700;
  line-height: 1.2;
}

h2 {
  font-family: var(--font-family-h2);
  font-size: var(--font-size-h2);
  font-weight: 700;
  line-height: 1.3;
}

h3 {
  font-family: var(--font-family-h3);
  font-size: var(--font-size-h3);
  font-weight: 400;
  line-height: 1.4;
}

h4 {
  font-family: var(--font-family-body);
  font-size: var(--font-size-h4);
  font-weight: 700;
  line-height: 1.4;
}

h5 {
  font-family: var(--font-family-body);
  font-size: var(--font-size-h5);
  font-weight: 700;
  line-height: 1.5;
}

h6 {
  font-family: var(--font-family-body);
  font-size: var(--font-size-body);
  font-weight: 600;
  line-height: 1.5;
}

p {
  font-family: var(--font-family-body);
  font-size: var(--font-size-body);
  font-weight: 400;
  line-height: 1.6;
}

small, .text-small {
  font-family: var(--font-family-body);
  font-size: var(--font-size-small);
  font-weight: 500;
  line-height: 1.4;
}

.container {
  width: 100%;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

.sr-only {
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  position: absolute;
  overflow: hidden;
}

.pointer-events-none {
  pointer-events: none;
}

.visible {
  visibility: visible;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.-inset-0\.5 {
  top: -.125rem;
  bottom: -.125rem;
  left: -.125rem;
  right: -.125rem;
}

.-inset-\[10px\] {
  top: -10px;
  bottom: -10px;
  left: -10px;
  right: -10px;
}

.-inset-full {
  top: -100%;
  bottom: -100%;
  left: -100%;
  right: -100%;
}

.-inset-px {
  top: -1px;
  bottom: -1px;
  left: -1px;
  right: -1px;
}

.inset-0 {
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.inset-px {
  top: 1px;
  bottom: 1px;
  left: 1px;
  right: 1px;
}

.inset-x-0 {
  left: 0;
  right: 0;
}

.inset-y-0 {
  top: 0;
  bottom: 0;
}

.-right-20 {
  right: calc(var(--space-20) * -1);
}

.-top-20 {
  top: calc(var(--space-20) * -1);
}

.-top-3 {
  top: calc(var(--space-3) * -1);
}

.bottom-0 {
  bottom: 0;
}

.bottom-1 {
  bottom: var(--space-1);
}

.left-0 {
  left: 0;
}

.left-1\/2 {
  left: 50%;
}

.left-\[20\%\] {
  left: 20%;
}

.right-0 {
  right: 0;
}

.right-0\.5 {
  right: .125rem;
}

.right-4 {
  right: var(--space-4);
}

.top-0 {
  top: 0;
}

.top-1 {
  top: var(--space-1);
}

.top-4 {
  top: var(--space-4);
}

.top-\[40\%\] {
  top: 40%;
}

.-z-10 {
  z-index: -10;
}

.-z-20 {
  z-index: -20;
}

.-z-30 {
  z-index: -30;
}

.z-0 {
  z-index: 0;
}

.z-10 {
  z-index: 10;
}

.z-50 {
  z-index: 50;
}

.col-span-3 {
  grid-column: span 3 / span 3;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.-mr-1 {
  margin-right: calc(var(--space-1) * -1);
}

.-mt-1 {
  margin-top: calc(var(--space-1) * -1);
}

.mb-1 {
  margin-bottom: var(--space-1);
}

.mb-12 {
  margin-bottom: var(--space-12);
}

.mb-16 {
  margin-bottom: var(--space-16);
}

.mb-2 {
  margin-bottom: var(--space-2);
}

.mb-3 {
  margin-bottom: var(--space-3);
}

.mb-4 {
  margin-bottom: var(--space-4);
}

.mb-6 {
  margin-bottom: var(--space-6);
}

.mb-8 {
  margin-bottom: var(--space-8);
}

.ml-1 {
  margin-left: var(--space-1);
}

.ml-2 {
  margin-left: var(--space-2);
}

.mr-1 {
  margin-right: var(--space-1);
}

.mr-2 {
  margin-right: var(--space-2);
}

.mr-3 {
  margin-right: var(--space-3);
}

.mr-8 {
  margin-right: var(--space-8);
}

.mt-0\.5 {
  margin-top: .125rem;
}

.mt-1 {
  margin-top: var(--space-1);
}

.mt-1\.5 {
  margin-top: .375rem;
}

.mt-12 {
  margin-top: var(--space-12);
}

.mt-16 {
  margin-top: var(--space-16);
}

.mt-2 {
  margin-top: var(--space-2);
}

.mt-4 {
  margin-top: var(--space-4);
}

.mt-6 {
  margin-top: var(--space-6);
}

.mt-8 {
  margin-top: var(--space-8);
}

.mt-auto {
  margin-top: auto;
}

.\!block {
  display: block !important;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.inline {
  display: inline;
}

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.table {
  display: table;
}

.grid {
  display: grid;
}

.\!hidden {
  display: none !important;
}

.hidden {
  display: none;
}

.size-full {
  width: 100%;
  height: 100%;
}

.h-1 {
  height: var(--space-1);
}

.h-1\.5 {
  height: .375rem;
}

.h-10 {
  height: var(--space-10);
}

.h-11 {
  height: 2.75rem;
}

.h-12 {
  height: var(--space-12);
}

.h-14 {
  height: 3.5rem;
}

.h-16 {
  height: var(--space-16);
}

.h-2 {
  height: var(--space-2);
}

.h-20 {
  height: var(--space-20);
}

.h-28 {
  height: 7rem;
}

.h-32 {
  height: 8rem;
}

.h-4 {
  height: var(--space-4);
}

.h-40 {
  height: 10rem;
}

.h-5 {
  height: var(--space-5);
}

.h-6 {
  height: var(--space-6);
}

.h-8 {
  height: var(--space-8);
}

.h-9 {
  height: var(--space-9);
}

.h-\[100\%\] {
  height: 100%;
}

.h-\[100cqh\] {
  height: 100cqh;
}

.h-\[100vh\] {
  height: 100vh;
}

.h-full {
  height: 100%;
}

.h-px {
  height: 1px;
}

.max-h-48 {
  max-height: 12rem;
}

.max-h-60 {
  max-height: 15rem;
}

.min-h-\[1000px\] {
  min-height: 1000px;
}

.min-h-\[300px\] {
  min-height: 300px;
}

.min-h-\[400px\] {
  min-height: 400px;
}

.min-h-\[800px\] {
  min-height: 800px;
}

.min-h-\[80px\] {
  min-height: 80px;
}

.min-h-screen {
  min-height: 100vh;
}

.w-1\.5 {
  width: .375rem;
}

.w-1\/4 {
  width: 25%;
}

.w-10 {
  width: var(--space-10);
}

.w-12 {
  width: var(--space-12);
}

.w-14 {
  width: 3.5rem;
}

.w-16 {
  width: var(--space-16);
}

.w-2 {
  width: var(--space-2);
}

.w-20 {
  width: var(--space-20);
}

.w-28 {
  width: 7rem;
}

.w-3\/4 {
  width: 75%;
}

.w-32 {
  width: 8rem;
}

.w-4 {
  width: var(--space-4);
}

.w-40 {
  width: 10rem;
}

.w-5 {
  width: var(--space-5);
}

.w-6 {
  width: var(--space-6);
}

.w-8 {
  width: var(--space-8);
}

.w-9 {
  width: var(--space-9);
}

.w-\[100\%\] {
  width: 100%;
}

.w-\[300px\] {
  width: 300px;
}

.w-auto {
  width: auto;
}

.w-full {
  width: 100%;
}

.min-w-\[280px\] {
  min-width: 280px;
}

.min-w-\[300px\] {
  min-width: 300px;
}

.max-w-2xl {
  max-width: 42rem;
}

.max-w-3xl {
  max-width: 48rem;
}

.max-w-4xl {
  max-width: 56rem;
}

.max-w-6xl {
  max-width: 72rem;
}

.max-w-7xl {
  max-width: 80rem;
}

.max-w-\[100vw\] {
  max-width: 100vw;
}

.max-w-\[180px\] {
  max-width: 180px;
}

.max-w-\[400px\] {
  max-width: 400px;
}

.max-w-\[42rem\] {
  max-width: 42rem;
}

.max-w-\[58rem\] {
  max-width: 58rem;
}

.max-w-lg {
  max-width: 32rem;
}

.max-w-md {
  max-width: 28rem;
}

.max-w-none {
  max-width: none;
}

.max-w-xs {
  max-width: 20rem;
}

.flex-1 {
  flex: 1;
}

.flex-shrink-0, .shrink-0 {
  flex-shrink: 0;
}

.flex-grow, .grow {
  flex-grow: 1;
}

.origin-left {
  transform-origin: 0;
}

.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-1 {
  --tw-translate-x: var(--space-1);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-12 {
  --tw-translate-x: var(--space-12);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-y-2 {
  --tw-translate-y: var(--space-2);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-0 {
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-\[1\] {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform-gpu {
  transform: translate3d(var(--tw-translate-x), var(--tw-translate-y), 0) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes shimmer-slide {
  to {
    transform: translate(calc(100cqw - 100%));
  }
}

.animate-shimmer-slide {
  animation: shimmer-slide var(--speed) ease-in-out infinite alternate;
}

@keyframes spin-around {
  0% {
    transform: translateZ(0)rotate(0);
  }

  15%, 35% {
    transform: translateZ(0)rotate(90deg);
  }

  65%, 85% {
    transform: translateZ(0)rotate(270deg);
  }

  100% {
    transform: translateZ(0)rotate(360deg);
  }
}

.animate-spin-around {
  animation: spin-around calc(var(--speed) * 2) infinite linear;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.cursor-pointer {
  cursor: pointer;
}

.select-none {
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}

.resize-none {
  resize: none;
}

.auto-rows-\[20rem\] {
  grid-auto-rows: 20rem;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.flex-col {
  flex-direction: column;
}

.flex-col-reverse {
  flex-direction: column-reverse;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-nowrap {
  flex-wrap: nowrap;
}

.place-items-center {
  place-items: center;
}

.items-start {
  align-items: flex-start;
}

.items-center {
  align-items: center;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-1 {
  gap: var(--space-1);
}

.gap-12 {
  gap: var(--space-12);
}

.gap-2 {
  gap: var(--space-2);
}

.gap-3 {
  gap: var(--space-3);
}

.gap-4 {
  gap: var(--space-4);
}

.gap-6 {
  gap: var(--space-6);
}

.gap-8 {
  gap: var(--space-8);
}

.space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(var(--space-2) * var(--tw-space-x-reverse));
  margin-left: calc(var(--space-2) * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(var(--space-3) * var(--tw-space-x-reverse));
  margin-left: calc(var(--space-3) * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(var(--space-4) * var(--tw-space-x-reverse));
  margin-left: calc(var(--space-4) * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(var(--space-1) * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(var(--space-1) * var(--tw-space-y-reverse));
}

.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(.375rem * var(--tw-space-y-reverse));
}

.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(var(--space-2) * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(var(--space-2) * var(--tw-space-y-reverse));
}

.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(var(--space-3) * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(var(--space-3) * var(--tw-space-y-reverse));
}

.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(var(--space-4) * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(var(--space-4) * var(--tw-space-y-reverse));
}

.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(var(--space-6) * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(var(--space-6) * var(--tw-space-y-reverse));
}

.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(var(--space-8) * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(var(--space-8) * var(--tw-space-y-reverse));
}

.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-visible {
  overflow: visible;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.truncate {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.whitespace-nowrap {
  white-space: nowrap;
}

.text-nowrap {
  text-wrap: nowrap;
}

.rounded-2xl {
  border-radius: 1rem;
}

.rounded-3xl {
  border-radius: 1.5rem;
}

.rounded-\[1\.25rem\] {
  border-radius: 1.25rem;
}

.rounded-\[22px\] {
  border-radius: 22px;
}

.rounded-\[inherit\] {
  border-radius: inherit;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: var(--radius);
}

.rounded-md {
  border-radius: calc(var(--radius)  - 2px);
}

.rounded-sm {
  border-radius: calc(var(--radius)  - 4px);
}

.rounded-xl {
  border-radius: .75rem;
}

.border {
  border-width: 1px;
}

.border-0 {
  border-width: 0;
}

.border-2 {
  border-width: 2px;
}

.border-\[0\.75px\] {
  border-width: .75px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-l {
  border-left-width: 1px;
}

.border-r {
  border-right-width: 1px;
}

.border-t {
  border-top-width: 1px;
}

.border-\[var\(--header-footer-border\)\] {
  border-color: var(--header-footer-border);
}

.border-\[var\(--master-brand-accent\)\] {
  border-color: var(--master-brand-accent);
}

.border-\[var\(--master-input-border\)\] {
  border-color: var(--master-input-border);
}

.border-blue-400 {
  --tw-border-opacity: 1;
  border-color: rgba(96, 165, 250, var(--tw-border-opacity, 1));
}

.border-brand-gold {
  --tw-border-opacity: 1;
  border-color: rgba(184, 149, 106, var(--tw-border-opacity, 1));
}

.border-brand-sage {
  --tw-border-opacity: 1;
  border-color: rgba(139, 154, 140, var(--tw-border-opacity, 1));
}

.border-brand-sage\/10 {
  border-color: rgba(139, 154, 140, .1);
}

.border-brand-sage\/20 {
  border-color: rgba(139, 154, 140, .2);
}

.border-error {
  --tw-border-opacity: 1;
  border-color: rgba(217, 119, 6, var(--tw-border-opacity, 1));
}

.border-green-400 {
  --tw-border-opacity: 1;
  border-color: rgba(74, 222, 128, var(--tw-border-opacity, 1));
}

.border-input {
  border-color: hsl(var(--input));
}

.border-mantle-700 {
  --tw-border-opacity: 1;
  border-color: rgba(73, 104, 82, var(--tw-border-opacity, 1));
}

.border-neutral-off-white\/20 {
  border-color: rgba(244, 241, 232, .2);
}

.border-primary {
  border-color: hsl(var(--primary));
}

.border-red-400 {
  --tw-border-opacity: 1;
  border-color: rgba(248, 113, 113, var(--tw-border-opacity, 1));
}

.border-red-500\/30 {
  border-color: rgba(239, 68, 68, .3);
}

.border-sage {
  --tw-border-opacity: 1;
  border-color: rgba(139, 154, 140, var(--tw-border-opacity, 1));
}

.border-sage\/10 {
  border-color: rgba(139, 154, 140, .1);
}

.border-sage\/20 {
  border-color: rgba(139, 154, 140, .2);
}

.border-sage\/30 {
  border-color: rgba(139, 154, 140, .3);
}

.border-transparent {
  border-color: rgba(0, 0, 0, 0);
}

.border-white {
  --tw-border-opacity: 1;
  border-color: rgba(255, 255, 255, var(--tw-border-opacity, 1));
}

.border-white\/10 {
  border-color: rgba(255, 255, 255, .1);
}

.border-white\/20 {
  border-color: rgba(255, 255, 255, .2);
}

.border-yellow-400 {
  --tw-border-opacity: 1;
  border-color: rgba(250, 204, 21, var(--tw-border-opacity, 1));
}

.bg-\[var\(--aurora-bg\)\] {
  background-color: var(--aurora-bg);
}

.bg-\[var\(--header-footer-bg\)\] {
  background-color: var(--header-footer-bg);
}

.bg-\[var\(--master-action-primary\)\] {
  background-color: var(--master-action-primary);
}

.bg-\[var\(--master-action-secondary\)\] {
  background-color: var(--master-action-secondary);
}

.bg-\[var\(--master-brand-accent\)\] {
  background-color: var(--master-brand-accent);
}

.bg-\[var\(--master-input-bg-light\)\] {
  background-color: var(--master-input-bg-light);
}

.bg-background {
  background-color: hsl(var(--background));
}

.bg-black\/50 {
  background-color: rgba(0, 0, 0, .5);
}

.bg-blue-500\/90 {
  background-color: rgba(59, 130, 246, .9);
}

.bg-brand-gold {
  --tw-bg-opacity: 1;
  background-color: rgba(184, 149, 106, var(--tw-bg-opacity, 1));
}

.bg-brand-gold\/20 {
  background-color: rgba(184, 149, 106, .2);
}

.bg-card {
  background-color: hsl(var(--card));
}

.bg-charcoal\/50 {
  background-color: rgba(44, 49, 55, .5);
}

.bg-destructive {
  background-color: hsl(var(--destructive));
}

.bg-gold {
  --tw-bg-opacity: 1;
  background-color: rgba(184, 149, 106, var(--tw-bg-opacity, 1));
}

.bg-green-500\/90 {
  background-color: rgba(34, 197, 94, .9);
}

.bg-neutral-charcoal-dark\/40 {
  background-color: rgba(45, 42, 38, .4);
}

.bg-neutral-charcoal-dark\/60 {
  background-color: rgba(45, 42, 38, .6);
}

.bg-neutral-charcoal-dark\/80 {
  background-color: rgba(45, 42, 38, .8);
}

.bg-neutral-charcoal-dark\/95 {
  background-color: rgba(45, 42, 38, .95);
}

.bg-neutral-charcoal-light\/20 {
  background-color: rgba(51, 51, 51, .2);
}

.bg-neutral-charcoal-light\/90 {
  background-color: rgba(51, 51, 51, .9);
}

.bg-primary {
  background-color: hsl(var(--primary));
}

.bg-primary\/20 {
  background-color: hsl(var(--primary) / .2);
}

.bg-red-500\/20 {
  background-color: rgba(239, 68, 68, .2);
}

.bg-red-500\/90 {
  background-color: rgba(239, 68, 68, .9);
}

.bg-sage {
  --tw-bg-opacity: 1;
  background-color: rgba(139, 154, 140, var(--tw-bg-opacity, 1));
}

.bg-sage\/10 {
  background-color: rgba(139, 154, 140, .1);
}

.bg-sage\/20 {
  background-color: rgba(139, 154, 140, .2);
}

.bg-secondary {
  background-color: hsl(var(--secondary));
}

.bg-theme-primary\/10 {
  background-color: rgba(54, 64, 53, .1);
}

.bg-transparent {
  background-color: rgba(0, 0, 0, 0);
}

.bg-white\/10 {
  background-color: rgba(255, 255, 255, .1);
}

.bg-white\/15 {
  background-color: rgba(255, 255, 255, .15);
}

.bg-white\/5 {
  background-color: rgba(255, 255, 255, .05);
}

.bg-yellow-500\/90 {
  background-color: rgba(234, 179, 8, .9);
}

.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.from-primary\/20 {
  --tw-gradient-from: hsl(var(--primary) / .2) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.from-sage\/20 {
  --tw-gradient-from: rgba(139, 154, 140, .2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgba(139, 154, 140, 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.to-primary\/5 {
  --tw-gradient-to: hsl(var(--primary) / .05) var(--tw-gradient-to-position);
}

.to-sage\/10 {
  --tw-gradient-to: rgba(139, 154, 140, .1) var(--tw-gradient-to-position);
}

.bg-\[length\:250\%_100\%\,auto\] {
  background-size: 250% 100%, auto;
}

.bg-clip-text {
  -webkit-background-clip: text;
  background-clip: text;
}

.p-10 {
  padding: var(--space-10);
}

.p-12 {
  padding: var(--space-12);
}

.p-2 {
  padding: var(--space-2);
}

.p-4 {
  padding: var(--space-4);
}

.p-6 {
  padding: var(--space-6);
}

.p-8 {
  padding: var(--space-8);
}

.p-px {
  padding: 1px;
}

.px-10 {
  padding-left: var(--space-10);
  padding-right: var(--space-10);
}

.px-2 {
  padding-left: var(--space-2);
  padding-right: var(--space-2);
}

.px-2\.5 {
  padding-left: .625rem;
  padding-right: .625rem;
}

.px-3 {
  padding-left: var(--space-3);
  padding-right: var(--space-3);
}

.px-4 {
  padding-left: var(--space-4);
  padding-right: var(--space-4);
}

.px-6 {
  padding-left: var(--space-6);
  padding-right: var(--space-6);
}

.px-8 {
  padding-left: var(--space-8);
  padding-right: var(--space-8);
}

.py-0\.5 {
  padding-top: .125rem;
  padding-bottom: .125rem;
}

.py-1 {
  padding-top: var(--space-1);
  padding-bottom: var(--space-1);
}

.py-1\.5 {
  padding-top: .375rem;
  padding-bottom: .375rem;
}

.py-10 {
  padding-top: var(--space-10);
  padding-bottom: var(--space-10);
}

.py-12 {
  padding-top: var(--space-12);
  padding-bottom: var(--space-12);
}

.py-16 {
  padding-top: var(--space-16);
  padding-bottom: var(--space-16);
}

.py-2 {
  padding-top: var(--space-2);
  padding-bottom: var(--space-2);
}

.py-20 {
  padding-top: var(--space-20);
  padding-bottom: var(--space-20);
}

.py-3 {
  padding-top: var(--space-3);
  padding-bottom: var(--space-3);
}

.py-4 {
  padding-top: var(--space-4);
  padding-bottom: var(--space-4);
}

.py-5 {
  padding-top: var(--space-5);
  padding-bottom: var(--space-5);
}

.py-8 {
  padding-top: var(--space-8);
  padding-bottom: var(--space-8);
}

.pb-4 {
  padding-bottom: var(--space-4);
}

.pb-6 {
  padding-bottom: var(--space-6);
}

.pb-8 {
  padding-bottom: var(--space-8);
}

.pl-10 {
  padding-left: var(--space-10);
}

.pl-3 {
  padding-left: var(--space-3);
}

.pr-4 {
  padding-right: var(--space-4);
}

.pt-0 {
  padding-top: 0;
}

.pt-16 {
  padding-top: var(--space-16);
}

.pt-24 {
  padding-top: var(--space-24);
}

.pt-32 {
  padding-top: 8rem;
}

.pt-4 {
  padding-top: var(--space-4);
}

.pt-6 {
  padding-top: var(--space-6);
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.font-inter {
  font-family: var(--font-inter), Inter, system-ui, sans-serif;
}

.font-jost {
  font-family: var(--font-jost), Jost, system-ui, sans-serif;
}

.font-sans {
  font-family: var(--font-inter), Inter, system-ui, sans-serif;
}

.font-serif, .font-tai-heritage {
  font-family: var(--font-tai-heritage), Georgia, serif;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}

.text-4xl {
  font-size: 2.25rem;
  line-height: 2.5rem;
}

.text-5xl {
  font-size: 3rem;
  line-height: 1;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: .875rem;
  line-height: 1.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs {
  font-size: .75rem;
  line-height: 1rem;
}

.font-\[var\(--font-family-body\)\] {
  font-weight: var(--font-family-body);
}

.font-black {
  font-weight: 900;
}

.font-bold {
  font-weight: 700;
}

.font-light {
  font-weight: 300;
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.uppercase {
  text-transform: uppercase;
}

.leading-\[1\.1\] {
  line-height: 1.1;
}

.leading-\[5rem\] {
  line-height: 5rem;
}

.leading-none {
  line-height: 1;
}

.leading-normal {
  line-height: 1.5;
}

.leading-relaxed {
  line-height: 1.625;
}

.leading-tight {
  line-height: 1.25;
}

.tracking-\[-0\.02em\] {
  letter-spacing: -.02em;
}

.tracking-tight {
  letter-spacing: -.025em;
}

.tracking-wide {
  letter-spacing: .025em;
}

.text-\[\#2D2A26\] {
  --tw-text-opacity: 1;
  color: rgba(45, 42, 38, var(--tw-text-opacity, 1));
}

.text-\[var\(--master-action-tertiary\)\] {
  color: var(--master-action-tertiary);
}

.text-\[var\(--master-icon-success\)\] {
  color: var(--master-icon-success);
}

.text-\[var\(--master-label-color\)\] {
  color: var(--master-label-color);
}

.text-\[var\(--master-text-primary-dark\)\] {
  color: var(--master-text-primary-dark);
}

.text-\[var\(--master-text-primary-light\)\] {
  color: var(--master-text-primary-light);
}

.text-\[var\(--master-text-secondary-dark\)\] {
  color: var(--master-text-secondary-dark);
}

.text-brand-beige {
  --tw-text-opacity: 1;
  color: rgba(240, 230, 217, var(--tw-text-opacity, 1));
}

.text-brand-beige\/70 {
  color: rgba(240, 230, 217, .7);
}

.text-card-foreground {
  color: hsl(var(--card-foreground));
}

.text-current {
  color: currentColor;
}

.text-destructive-foreground {
  color: hsl(var(--destructive-foreground));
}

.text-error {
  --tw-text-opacity: 1;
  color: rgba(217, 119, 6, var(--tw-text-opacity, 1));
}

.text-foreground {
  color: hsl(var(--foreground));
}

.text-gray-300 {
  --tw-text-opacity: 1;
  color: rgba(209, 213, 219, var(--tw-text-opacity, 1));
}

.text-mantle-100 {
  --tw-text-opacity: 1;
  color: rgba(240, 230, 217, var(--tw-text-opacity, 1));
}

.text-mantle-300 {
  --tw-text-opacity: 1;
  color: rgba(208, 197, 183, var(--tw-text-opacity, 1));
}

.text-mantle-950 {
  --tw-text-opacity: 1;
  color: rgba(44, 49, 55, var(--tw-text-opacity, 1));
}

.text-muted-foreground {
  color: hsl(var(--muted-foreground));
}

.text-neutral-charcoal-dark {
  --tw-text-opacity: 1;
  color: rgba(45, 42, 38, var(--tw-text-opacity, 1));
}

.text-neutral-off-white {
  --tw-text-opacity: 1;
  color: rgba(244, 241, 232, var(--tw-text-opacity, 1));
}

.text-primary {
  color: hsl(var(--primary));
}

.text-primary-foreground {
  color: hsl(var(--primary-foreground));
}

.text-red-200 {
  --tw-text-opacity: 1;
  color: rgba(254, 202, 202, var(--tw-text-opacity, 1));
}

.text-sage {
  --tw-text-opacity: 1;
  color: rgba(139, 154, 140, var(--tw-text-opacity, 1));
}

.text-secondary-foreground {
  color: hsl(var(--secondary-foreground));
}

.text-slate-950 {
  --tw-text-opacity: 1;
  color: rgba(2, 6, 23, var(--tw-text-opacity, 1));
}

.text-theme-primary {
  --tw-text-opacity: 1;
  color: rgba(54, 64, 53, var(--tw-text-opacity, 1));
}

.text-transparent {
  color: rgba(0, 0, 0, 0);
}

.text-white {
  --tw-text-opacity: 1;
  color: rgba(255, 255, 255, var(--tw-text-opacity, 1));
}

.text-white\/70 {
  color: rgba(255, 255, 255, .7);
}

.underline {
  -webkit-text-decoration-line: underline;
  text-decoration-line: underline;
}

.underline-offset-4 {
  text-underline-offset: 4px;
}

.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.accent-brand-sage {
  accent-color: #8b9a8c;
}

.accent-primary {
  accent-color: hsl(var(--primary));
}

.accent-secondary {
  accent-color: hsl(var(--secondary));
}

.opacity-0 {
  opacity: 0;
}

.opacity-100 {
  opacity: 1;
}

.opacity-50 {
  opacity: .5;
}

.opacity-60 {
  opacity: .6;
}

.opacity-70 {
  opacity: .7;
}

.opacity-75 {
  opacity: .75;
}

.opacity-85 {
  opacity: .85;
}

.opacity-90 {
  opacity: .9;
}

.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgba(0, 0, 0, .25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
}

.shadow-\[0_1000px_0_0_var\(--mantle-50\)_inset\] {
  --tw-shadow: 0 1000px 0 0 var(--mantle-50) inset;
  --tw-shadow-colored: inset 0 1000px 0 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
}

.shadow-\[inset_0_-8px_10px_\#ffffff1f\] {
  --tw-shadow: inset 0 -8px 10px rgba(255, 255, 255, .12);
  --tw-shadow-colored: inset 0 -8px 10px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
}

.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, .1), 0 4px 6px -4px rgba(0, 0, 0, .1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
}

.shadow-sm {
  --tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, .05);
  --tw-shadow-colored: 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
}

.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgba(0, 0, 0, .1), 0 8px 10px -6px rgba(0, 0, 0, .1);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 8px 10px -6px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
}

.outline {
  outline-style: solid;
}

.ring {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 rgba(0, 0, 0, 0));
}

.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 rgba(0, 0, 0, 0));
}

.ring-4 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 rgba(0, 0, 0, 0));
}

.ring-offset-background {
  --tw-ring-offset-color: hsl(var(--background));
}

.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-3xl {
  --tw-blur: blur(64px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-\[2px\] {
  --tw-blur: blur(2px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-\[8px\] {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-\[var\(--blur\)\] {
  --tw-blur: blur(var(--blur));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.blur-sm {
  --tw-blur: blur(4px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.drop-shadow {
  --tw-drop-shadow: drop-shadow(0 1px 2px rgba(0, 0, 0, .1)) drop-shadow(0 1px 1px rgba(0, 0, 0, .06));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.drop-shadow-lg {
  --tw-drop-shadow: drop-shadow(0 10px 8px rgba(0, 0, 0, .04)) drop-shadow(0 4px 3px rgba(0, 0, 0, .1));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.drop-shadow-md {
  --tw-drop-shadow: drop-shadow(0 4px 3px rgba(0, 0, 0, .07)) drop-shadow(0 2px 2px rgba(0, 0, 0, .06));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.drop-shadow-sm {
  --tw-drop-shadow: drop-shadow(0 1px 1px rgba(0, 0, 0, .05));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.backdrop-blur-lg {
  --tw-backdrop-blur: blur(16px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-md {
  --tw-backdrop-blur: blur(12px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-blur-xl {
  --tw-backdrop-blur: blur(24px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.backdrop-filter {
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}

.transition {
  transition-property: color, background-color, border-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter, backdrop-filter;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.transition-all {
  transition-property: all;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.transition-colors {
  transition-property: color, background-color, border-color, -webkit-text-decoration-color, text-decoration-color, fill, stroke;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.transition-opacity {
  transition-property: opacity;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.transition-shadow {
  transition-property: box-shadow;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.transition-transform {
  transition-property: transform;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.delay-100 {
  transition-delay: .1s;
}

.duration-200 {
  transition-duration: .2s;
}

.duration-300 {
  transition-duration: .3s;
}

.duration-500 {
  transition-duration: .5s;
}

.ease-in-out {
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, .2, 1);
}

.will-change-transform {
  will-change: transform;
}

@keyframes enter {
  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}

@keyframes exit {
  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}

.animate-in {
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
  animation-name: enter;
  animation-duration: .15s;
}

.slide-in-from-right-full {
  --tw-enter-translate-x: 100%;
}

.duration-200 {
  animation-duration: .2s;
}

.duration-300 {
  animation-duration: .3s;
}

.duration-500 {
  animation-duration: .5s;
}

.delay-100 {
  animation-delay: .1s;
}

.ease-in-out {
  animation-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.ease-out {
  animation-timing-function: cubic-bezier(0, 0, .2, 1);
}

.running {
  animation-play-state: running;
}

.\[--base-color\:var\(--global-text-shimmer-base\)\] {
  --base-color: var(--global-text-shimmer-base);
}

.\[--base-gradient-color\:var\(--global-text-shimmer-highlight\)\] {
  --base-gradient-color: var(--global-text-shimmer-highlight);
}

.\[--bg\:linear-gradient\(90deg\,\#0000_calc\(50\%-var\(--spread\)\)\,var\(--base-gradient-color\)\,\#0000_calc\(50\%\+var\(--spread\)\)\)\] {
  --bg: linear-gradient(90deg, rgba(0, 0, 0, 0) calc(50% - var(--spread)), var(--base-gradient-color), rgba(0, 0, 0, 0) calc(50% + var(--spread)));
}

.\[aspect-ratio\:1\] {
  aspect-ratio: 1;
}

.\[background-repeat\:no-repeat\,padding-box\] {
  background-repeat: no-repeat, padding-box;
}

.\[background\:conic-gradient\(from_calc\(270deg-\(var\(--spread\)\*0\.5\)\)\,transparent_0\,var\(--shimmer-color\)_var\(--spread\)\,transparent_var\(--spread\)\)\] {
  background: conic-gradient(from calc(270deg - (var(--spread) * .5)), transparent 0, var(--shimmer-color) var(--spread), transparent var(--spread));
}

.\[background\:var\(--bg\)\] {
  background: var(--bg);
}

.\[border-radius\:0\] {
  border-radius: 0;
}

.\[border-radius\:var\(--radius\)\] {
  border-radius: var(--radius);
}

.\[container-type\:size\] {
  container-type: size;
}

.\[inset\:var\(--cut\)\] {
  inset: var(--cut);
}

.\[mask-image\:radial-gradient\(ellipse_at_100\%_0\%\,black_10\%\,transparent_70\%\)\] {
  -webkit-mask-image: radial-gradient(at 100% 0, #000 10%, rgba(0, 0, 0, 0) 70%);
  mask-image: radial-gradient(at 100% 0, #000 10%, rgba(0, 0, 0, 0) 70%);
}

.\[mask\:linear-gradient\(var\(--mantle-900\)\,_transparent_50\%\)\] {
  -webkit-mask: linear-gradient(var(--mantle-900), transparent 50%);
  mask: linear-gradient(var(--mantle-900), transparent 50%);
}

.\[mask\:none\] {
  -webkit-mask: none;
  mask: none;
}

.\[translate\:0_0\] {
  translate: 0;
}

.page-home .shiny-button {
  background-color: var(--home-shiny-button-bg);
  color: var(--home-shiny-button-text);
  --shimmer-color: var(--home-shiny-button-shimmer);
}

.page-home .shiny-button.hero-cta {
  background-color: var(--home-shiny-button-hero-bg);
  color: var(--home-shiny-button-hero-text);
  --shimmer-color: var(--home-shiny-button-hero-shimmer);
}

.page-home .shiny-button.section-cta {
  background-color: var(--home-shiny-button-cta-bg);
  color: var(--home-shiny-button-cta-text);
  --shimmer-color: var(--home-shiny-button-cta-shimmer);
}

.page-home .golden-glowing-card {
  background: var(--home-golden-card-bg);
  border-color: var(--home-golden-card-border);
  --glow-color: var(--home-golden-card-glow);
}

.page-home .text-shimmer {
  --base-color: var(--home-text-shimmer-base);
  --base-gradient-color: var(--home-text-shimmer-highlight);
}

.page-home .word-pull-up {
  color: var(--home-word-pullup-color);
}

.page-home .marquee-animation {
  color: var(--home-marquee-text);
  background: var(--home-marquee-bg);
}

.nav-link {
  color: var(--text-primary);
  text-shadow: 0 1px 2px rgba(0, 0, 0, .1);
  border-radius: .375rem;
  padding: .375rem .75rem;
  font-weight: 500;
  transition: all .3s;
}

.nav-link:hover {
  background: var(--master-header-nav-hover-bg);
  color: var(--text-primary);
}

.nav-link-active {
  background: var(--master-header-nav-active-bg);
  color: var(--text-primary);
  box-shadow: var(--master-header-nav-active-glow);
}

.theme-input {
  transition: all .3s;
  background-color: var(--input-bg) !important;
  border-color: var(--input-border) !important;
  color: var(--text-primary) !important;
}

.theme-input::placeholder {
  opacity: 1;
  color: var(--input-placeholder) !important;
}

.theme-input:focus {
  border-color: var(--icon-accent) !important;
  box-shadow: 0 0 0 2px var(--accent-hover-overlay) !important;
}

.limelight-nav {
  position: relative;
  overflow: hidden;
}

.limelight-nav:before {
  content: "";
  z-index: -1;
  opacity: 0;
  background: radial-gradient(circle, #d97706 0%, rgba(0, 0, 0, 0) 70%);
  border-radius: 50%;
  width: 0;
  height: 0;
  transition: all .3s;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.limelight-nav:hover:before, .limelight-nav.nav-link-active:before {
  opacity: .3;
  width: 120px;
  height: 120px;
}

.limelight-nav:hover, .limelight-nav.nav-link-active {
  color: #ca8a04;
  text-shadow: 0 0 8px rgba(202, 138, 4, .5);
}

@keyframes beam {
  0% {
    transform: translateY(-100%);
  }

  100% {
    transform: translateY(100%);
  }
}

.glassmorphism-light {
  -webkit-backdrop-filter: blur(16px);
  background: rgba(245, 250, 247, .1);
  border: 1px solid rgba(124, 154, 133, .2);
  box-shadow: 0 8px 32px rgba(44, 49, 55, .37);
}

.glassmorphism-medium {
  -webkit-backdrop-filter: blur(20px);
  background: rgba(44, 49, 55, .7);
  border: 1px solid rgba(124, 154, 133, .15);
  box-shadow: 0 8px 32px rgba(44, 49, 55, .5);
}

.glassmorphism-heavy {
  -webkit-backdrop-filter: blur(24px);
  background: rgba(44, 49, 55, .8);
  border: 1px solid rgba(124, 154, 133, .1);
  box-shadow: 0 12px 40px rgba(44, 49, 55, .6);
}

.glassmorphism-card {
  -webkit-backdrop-filter: blur(12px);
  background: rgba(51, 51, 51, .2);
  border: 1px solid rgba(124, 154, 133, .1);
  box-shadow: 0 4px 24px rgba(44, 49, 55, .3);
}

@supports not ((-webkit-backdrop-filter: blur(1px)) or (backdrop-filter: blur(1px))) {
  .glassmorphism-light, .glassmorphism-medium, .glassmorphism-heavy, .glassmorphism-card {
    background: rgba(44, 49, 55, .9);
  }
}

@media screen and (max-width: 767px) {
  :root {
    --font-size-h1: var(--font-size-h1-mobile);
    --font-size-h2: var(--font-size-h2-mobile);
    --font-size-h3: var(--font-size-h3-mobile);
    --font-size-h4: var(--font-size-h4-mobile);
    --font-size-h5: var(--font-size-h5-mobile);
    --font-size-body: var(--font-size-body-mobile);
    --font-size-small: var(--font-size-small-mobile);
    --header-height: 3.713rem;
    --header-logo-size: 2.23rem;
    --header-nav-font-size: .836rem;
    --header-button-height: 2.23rem;
    --header-padding: .747rem;
  }
}

.dark {
  --master-header-background: #2d2a26;
  --master-header-backdrop-blur: 50%;
  --master-header-border: rgba(169, 162, 153, .3);
  --master-header-logo-bg: linear-gradient(135deg, #b8956a, #a9a299);
  --master-header-logo-border: rgba(184, 149, 106, .3);
  --master-header-logo-icon: #2d2a26;
  --master-header-brand-text: #f4f1e8;
  --global-footer-bg: rgba(46, 51, 47, .9);
  --global-footer-border: rgba(95, 109, 97, .3);
  --global-footer-text: #e1e6e1;
  --global-footer-link: #f6f7f6;
  --global-footer-link-hover: #c3ccc3;
  --global-cookie-bg: #181b19;
  --global-cookie-border: #5f6d61;
  --global-cookie-text: #f6f7f6;
  --global-cookie-button-bg: #79887a;
  --header-footer-bg: rgba(45, 42, 38, .8);
  --header-footer-backdrop-blur: blur(16px);
  --header-footer-border: rgba(169, 162, 153, .3);
  --icon-primary: #f6f7f6;
  --icon-secondary: #e1e6e1;
  --icon-accent: #c3ccc3;
  --icon-on-accent: #181b19;
  --icon-muted: #8b9a8c;
  --icon-on-dark: #2e332f;
  --card-bg: rgba(24, 27, 25, .95);
  --shadow-card: 0 4px 6px -1px rgba(0, 0, 0, .3), 0 2px 4px -1px rgba(0, 0, 0, .2);
  --accent-hover-overlay: rgba(184, 149, 106, .08);
  --accent-bg: rgba(184, 149, 106, .2);
  --border-accent: rgba(184, 149, 106, .4);
  --input-bg: rgba(24, 27, 25, .8);
  --input-border: rgba(95, 109, 97, .3);
  --input-placeholder: rgba(246, 247, 246, .5);
  --text-primary: #f5faf7;
  --text-secondary: #f0e6d9;
  --text-headlines: #f5faf7;
  --text-links: #b8956a;
  --text-links-hover: #a6845c;
  --aurora-bg: #2d2a26;
  --aurora-bg-dark: #333;
  --aurora-stripe-light: rgba(244, 241, 232, .02);
  --aurora-stripe-dark: rgba(184, 149, 106, .15);
  --aurora-flow-1: rgba(184, 149, 106, .12);
  --aurora-flow-2: rgba(169, 162, 153, .08);
  --aurora-flow-3: rgba(75, 61, 107, .1);
  --aurora-flow-4: rgba(45, 42, 38, .18);
  --aurora-flow-5: rgba(184, 149, 106, .06);
  --tw-text-sage: #b8956a !important;
  --tw-bg-sage: #b8956a !important;
  --tw-border-sage: #b8956a !important;
  --tw-from-sage: #b8956a !important;
  --tw-to-sage: #a9a299 !important;
}

.dark .text-sage, .dark .text-sage-500, .dark .text-sage-400, .dark .text-sage-600 {
  color: #b8956a !important;
}

.dark .bg-sage, .dark .bg-sage-500, .dark .bg-sage-400, .dark .bg-sage-600, .dark .from-sage, .dark .to-sage {
  background-color: #b8956a !important;
}

.dark .border-sage, .dark .border-sage-500, .dark .border-sage-400, .dark .border-sage-600 {
  border-color: #b8956a !important;
}

.dark .from-sage\/20 {
  --tw-gradient-from: rgba(184, 149, 106, .2) !important;
}

.dark .to-sage\/10 {
  --tw-gradient-to: rgba(184, 149, 106, .1) !important;
}

.dark .border-sage\/30 {
  border-color: rgba(184, 149, 106, .3) !important;
}

.dark .bg-sage\/20 {
  background-color: rgba(184, 149, 106, .2) !important;
}

:root:not(.dark) .text-gold, :root:not(.dark) .text-muted-gold, :root:not(.dark) .text-brand-gold {
  color: #364035 !important;
}

:root:not(.dark) .bg-gold, :root:not(.dark) .bg-muted-gold, :root:not(.dark) .bg-brand-gold, :root:not(.dark) .from-gold, :root:not(.dark) .to-gold {
  background-color: #364035 !important;
}

:root:not(.dark) .border-gold, :root:not(.dark) .border-muted-gold, :root:not(.dark) .border-brand-gold {
  border-color: #364035 !important;
}

:root:not(.dark) .bg-gold\/20, :root:not(.dark) .bg-muted-gold\/20 {
  background-color: rgba(54, 64, 53, .2) !important;
}

:root:not(.dark) .border-gold\/30, :root:not(.dark) .border-muted-gold\/30 {
  border-color: rgba(54, 64, 53, .3) !important;
}

:root:not(.dark) .text-theme-primary {
  color: #364035 !important;
}

:root:not(.dark) .bg-theme-primary {
  background-color: #364035 !important;
}

:root:not(.dark) .bg-theme-primary\/10 {
  background-color: rgba(54, 64, 53, .1) !important;
}

:root:not(.dark) .bg-theme-primary\/5 {
  background-color: rgba(54, 64, 53, .05) !important;
}

.dark .text-theme-primary {
  color: #b8956a !important;
}

.dark .bg-theme-primary {
  background-color: #b8956a !important;
}

.dark .bg-theme-primary\/10 {
  background-color: rgba(184, 149, 106, .1) !important;
}

.dark .bg-theme-primary\/5 {
  background-color: rgba(184, 149, 106, .05) !important;
}

@media screen and (max-width: 767px) {
  body {
    -moz-text-size-adjust: 100%;
    text-size-adjust: 100%;
    overflow-x: hidden;
  }

  button, a, [role="button"] {
    min-width: 44px;
    min-height: 44px;
  }

  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .nav-link {
    padding: .75rem 1rem;
    font-size: 1.125rem;
  }

  .golden-glowing-card-container {
    margin: .75rem;
  }

  .hero-title {
    line-height: 1.1;
    font-size: 2.5rem !important;
  }

  .hero-subtitle {
    line-height: 1.5;
    font-size: 1.125rem !important;
  }
}

@media screen and (min-width: 768px) and (max-width: 1023px) {
  .container {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .grid-responsive {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media screen and (min-width: 1024px) {
  .container {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .grid-responsive {
    grid-template-columns: repeat(3, 1fr);
  }
}

.aurora-background, .backdrop-blur-lg, .backdrop-blur-md, .backdrop-blur-sm {
  will-change: transform;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  perspective: 1000px;
  transform: translateZ(0);
}

@media screen and (max-width: 768px) {
  .backdrop-blur-lg {
    -webkit-backdrop-filter: blur(8px);
  }

  .backdrop-blur-md {
    -webkit-backdrop-filter: blur(6px);
  }

  .backdrop-blur-sm {
    -webkit-backdrop-filter: blur(4px);
  }
}

* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

@media screen and (max-width: 768px) {
  .aurora-background {
    opacity: .7;
    filter: blur(8px);
  }

  .aurora-background:after {
    animation-duration: 120s;
  }
}

@media screen and (orientation: landscape) {
  html, body {
    min-height: 100lvh !important;
  }
}

@supports (padding: max(0px)) {
  html {
    padding-top: max(var(--safe-area-inset-top), 0px);
    padding-left: max(var(--safe-area-inset-left), 0px);
    padding-right: max(var(--safe-area-inset-right), 0px);
    padding-bottom: max(var(--safe-area-inset-bottom), 0px);
  }
}

@supports (-webkit-touch-callout: none) {
  html, body {
    height: -webkit-fill-available;
  }
}

.\*\:me-10 > :not(:-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  margin-right: var(--space-10);
}

.\*\:me-10 > :not(:-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  margin-right: var(--space-10);
}

.\*\:me-10 > :not(:is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi))) {
  margin-right: var(--space-10);
}

.\*\:me-10 > :-webkit-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  margin-left: var(--space-10);
}

.\*\:me-10 > :-moz-any(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  margin-left: var(--space-10);
}

.\*\:me-10 > :is(:lang(ae), :lang(ar), :lang(arc), :lang(bcc), :lang(bqi), :lang(ckb), :lang(dv), :lang(fa), :lang(glk), :lang(he), :lang(ku), :lang(mzn), :lang(nqo), :lang(pnb), :lang(ps), :lang(sd), :lang(ug), :lang(ur), :lang(yi)) {
  margin-left: var(--space-10);
}

.\*\:block > * {
  display: block;
}

.file\:border-0::-webkit-file-upload-button {
  border-width: 0;
}

.file\:border-0::file-selector-button {
  border-width: 0;
}

.file\:bg-transparent::-webkit-file-upload-button {
  background-color: rgba(0, 0, 0, 0);
}

.file\:bg-transparent::file-selector-button {
  background-color: rgba(0, 0, 0, 0);
}

.file\:text-sm::-webkit-file-upload-button {
  font-size: .875rem;
  line-height: 1.25rem;
}

.file\:text-sm::file-selector-button {
  font-size: .875rem;
  line-height: 1.25rem;
}

.file\:font-medium::-webkit-file-upload-button {
  font-weight: 500;
}

.file\:font-medium::file-selector-button {
  font-weight: 500;
}

.placeholder\:text-\[\#F5F5DC\]::placeholder {
  --tw-text-opacity: 1;
  color: rgba(245, 245, 220, var(--tw-text-opacity, 1));
}

.placeholder\:text-\[var\(--master-text-secondary-light\)\]::placeholder {
  color: var(--master-text-secondary-light);
}

.before\:absolute:before {
  content: var(--tw-content);
  position: absolute;
}

.before\:aspect-square:before {
  content: var(--tw-content);
  aspect-ratio: 1;
}

.before\:w-\[200\%\]:before {
  content: var(--tw-content);
  width: 200%;
}

.before\:rotate-\[-90deg\]:before {
  content: var(--tw-content);
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.before\:bg-\[conic-gradient\(from_0deg\,transparent_0_340deg\,\#D97706_360deg\)\]:before {
  content: var(--tw-content);
  background-image: conic-gradient(from 0deg, transparent 0 340deg, #d97706 360deg);
}

.before\:content-\[\'\'\]:before {
  --tw-content: "";
  content: var(--tw-content);
}

.before\:\[inset\:0_auto_auto_50\%\]:before {
  content: var(--tw-content);
  top: 0;
  bottom: auto;
  left: 50%;
  right: auto;
}

.before\:\[translate\:-50\%_-15\%\]:before {
  content: var(--tw-content);
  translate: -50% -15%;
}

.after\:absolute:after {
  content: var(--tw-content);
  position: absolute;
}

.after\:inset-0:after {
  content: var(--tw-content);
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.after\:inset-\[calc\(-1\*var\(--glowingeffect-border-width\)\)\]:after {
  content: var(--tw-content);
  inset: calc(-1 * var(--glowingeffect-border-width));
}

@keyframes aurora {
  0% {
    content: var(--tw-content);
    background-position: 50%, 50%;
  }

  25% {
    content: var(--tw-content);
    background-position: 200% 25%, 200% 75%;
  }

  50% {
    content: var(--tw-content);
    background-position: 350%, 350%;
  }

  75% {
    content: var(--tw-content);
    background-position: 100% 75%, 100% 25%;
  }

  100% {
    content: var(--tw-content);
    background-position: 50%, 50%;
  }
}

.after\:animate-aurora:after {
  content: var(--tw-content);
  animation: 25s ease-in-out infinite aurora;
}

.after\:rounded-\[inherit\]:after {
  content: var(--tw-content);
  border-radius: inherit;
}

.after\:opacity-\[var\(--active\)\]:after {
  content: var(--tw-content);
  opacity: var(--active);
}

.after\:mix-blend-difference:after {
  content: var(--tw-content);
  mix-blend-mode: difference;
}

.after\:transition-opacity:after {
  content: var(--tw-content);
  transition-property: opacity;
  transition-duration: .15s;
  transition-timing-function: cubic-bezier(.4, 0, .2, 1);
}

.after\:duration-300:after {
  content: var(--tw-content);
  transition-duration: .3s;
}

.after\:content-\[\"\"\]:after {
  --tw-content: "";
  content: var(--tw-content);
}

.after\:duration-300:after {
  content: var(--tw-content);
  animation-duration: .3s;
}

.after\:\[background-attachment\:fixed\]:after {
  content: var(--tw-content);
  background-attachment: fixed;
}

.after\:\[background-size\:250\%\,_150\%\]:after {
  content: var(--tw-content);
  background-size: 250%, 150%;
}

.after\:\[background\:var\(--gradient\)\]:after {
  content: var(--tw-content);
  background: var(--gradient);
}

.after\:\[border\:var\(--glowingeffect-border-width\)_solid_transparent\]:after {
  content: var(--tw-content);
  border: var(--glowingeffect-border-width) solid transparent;
}

.after\:\[mask-clip\:padding-box\,border-box\]:after {
  content: var(--tw-content);
  -webkit-mask-clip: padding-box, border-box;
  mask-clip: padding-box, border-box;
}

.after\:\[mask-composite\:intersect\]:after {
  content: var(--tw-content);
  -webkit-mask-composite: source-in;
  mask-composite: intersect;
}

.after\:\[mask-image\:linear-gradient\(\#0000\,\#0000\)\,conic-gradient\(from_calc\(\(var\(--start\)-var\(--spread\)\)\*1deg\)\,\#00000000_0deg\,\#fff\,\#00000000_calc\(var\(--spread\)\*2deg\)\)\]:after {
  content: var(--tw-content);
  -webkit-mask-image: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0)), conic-gradient(from calc((var(--start)  - var(--spread)) * 1deg), rgba(0, 0, 0, 0) 0deg, #fff, rgba(0, 0, 0, 0) calc(var(--spread) * 2deg));
  mask-image: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0)), conic-gradient(from calc((var(--start)  - var(--spread)) * 1deg), rgba(0, 0, 0, 0) 0deg, #fff, rgba(0, 0, 0, 0) calc(var(--spread) * 2deg));
}

.hover\:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-\[1\.02\]:hover {
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:border-gold:hover {
  --tw-border-opacity: 1;
  border-color: rgba(184, 149, 106, var(--tw-border-opacity, 1));
}

.hover\:border-sage\/50:hover {
  border-color: rgba(139, 154, 140, .5);
}

.hover\:bg-\[var\(--master-action-hover\)\]:hover {
  background-color: var(--master-action-hover);
}

.hover\:bg-accent:hover {
  background-color: hsl(var(--accent));
}

.hover\:bg-brand-sage\/10:hover {
  background-color: rgba(139, 154, 140, .1);
}

.hover\:bg-brand-sage\/20:hover {
  background-color: rgba(139, 154, 140, .2);
}

.hover\:bg-destructive\/80:hover {
  background-color: hsl(var(--destructive) / .8);
}

.hover\:bg-destructive\/90:hover {
  background-color: hsl(var(--destructive) / .9);
}

.hover\:bg-error\/20:hover {
  background-color: rgba(217, 119, 6, .2);
}

.hover\:bg-gold-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgba(166, 132, 92, var(--tw-bg-opacity, 1));
}

.hover\:bg-primary\/80:hover {
  background-color: hsl(var(--primary) / .8);
}

.hover\:bg-secondary\/80:hover {
  background-color: hsl(var(--secondary) / .8);
}

.hover\:bg-theme-primary\/5:hover {
  background-color: rgba(54, 64, 53, .05);
}

.hover\:bg-white\/10:hover {
  background-color: rgba(255, 255, 255, .1);
}

.hover\:text-accent-foreground:hover {
  color: hsl(var(--accent-foreground));
}

.hover\:text-brand-gold:hover {
  --tw-text-opacity: 1;
  color: rgba(184, 149, 106, var(--tw-text-opacity, 1));
}

.hover\:text-neutral-off-white:hover {
  --tw-text-opacity: 1;
  color: rgba(244, 241, 232, var(--tw-text-opacity, 1));
}

.hover\:text-theme-primary:hover {
  --tw-text-opacity: 1;
  color: rgba(54, 64, 53, var(--tw-text-opacity, 1));
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgba(255, 255, 255, var(--tw-text-opacity, 1));
}

.hover\:underline:hover {
  -webkit-text-decoration-line: underline;
  text-decoration-line: underline;
}

.hover\:opacity-100:hover {
  opacity: 1;
}

.hover\:opacity-80:hover {
  opacity: .8;
}

.hover\:shadow:hover {
  --tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, .1), 0 1px 2px -1px rgba(0, 0, 0, .1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
}

.hover\:shadow-2xl:hover {
  --tw-shadow: 0 25px 50px -12px rgba(0, 0, 0, .25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
}

.hover\:shadow-\[0_0_20px_rgba\(202\,138\,4\,0\.3\)\]:hover {
  --tw-shadow: 0 0 20px rgba(202, 138, 4, .3);
  --tw-shadow-colored: 0 0 20px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
}

.hover\:shadow-lg:hover {
  --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, .1), 0 4px 6px -4px rgba(0, 0, 0, .1);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -4px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
}

.focus\:outline-none:focus {
  outline-offset: 2px;
  outline: 2px solid rgba(0, 0, 0, 0);
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 rgba(0, 0, 0, 0));
}

.focus\:ring-error\/30:focus {
  --tw-ring-color: rgba(217, 119, 6, .3);
}

.focus\:ring-ring:focus {
  --tw-ring-color: hsl(var(--ring));
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.focus-visible\:border-\[var\(--master-input-border-focus\)\]:focus-visible {
  border-color: var(--master-input-border-focus);
}

.focus-visible\:outline-none:focus-visible {
  outline-offset: 2px;
  outline: 2px solid rgba(0, 0, 0, 0);
}

.focus-visible\:ring-2:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 rgba(0, 0, 0, 0));
}

.focus-visible\:ring-\[var\(--master-input-border-focus\)\]:focus-visible {
  --tw-ring-color: var(--master-input-border-focus);
}

.focus-visible\:ring-ring:focus-visible {
  --tw-ring-color: hsl(var(--ring));
}

.focus-visible\:ring-offset-2:focus-visible {
  --tw-ring-offset-width: 2px;
}

.active\:translate-y-px:active {
  --tw-translate-y: 1px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:opacity-50:disabled {
  opacity: .5;
}

.group:hover .group-hover\:left-\[0\%\] {
  left: 0%;
}

.group:hover .group-hover\:top-\[0\%\] {
  top: 0%;
}

.group:hover .group-hover\:h-full {
  height: 100%;
}

.group:hover .group-hover\:w-\[calc\(100\%-0\.5rem\)\] {
  width: calc(100% - .5rem);
}

.group:hover .group-hover\:w-full {
  width: 100%;
}

.group:hover .group-hover\:-translate-x-1 {
  --tw-translate-x: calc(var(--space-1) * -1);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:-translate-y-2 {
  --tw-translate-y: calc(var(--space-2) * -1);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:translate-x-1 {
  --tw-translate-x: var(--space-1);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:translate-x-12 {
  --tw-translate-x: var(--space-12);
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-\[1\.8\] {
  --tw-scale-x: 1.8;
  --tw-scale-y: 1.8;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:bg-gold {
  --tw-bg-opacity: 1;
  background-color: rgba(184, 149, 106, var(--tw-bg-opacity, 1));
}

.group\/card:hover .group-hover\/card\:opacity-100 {
  opacity: 1;
}

.group:hover .group-hover\:opacity-0 {
  opacity: 0;
}

.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

.group:hover .group-hover\:shadow-\[inset_0_-6px_10px_\#ffffff3f\] {
  --tw-shadow: inset 0 -6px 10px rgba(255, 255, 255, .247);
  --tw-shadow-colored: inset 0 -6px 10px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
}

.group:active .group-active\:scale-95 {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:active .group-active\:shadow-\[inset_0_-10px_10px_\#ffffff3f\] {
  --tw-shadow: inset 0 -10px 10px rgba(255, 255, 255, .247);
  --tw-shadow-colored: inset 0 -10px 10px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
}

.peer:disabled ~ .peer-disabled\:cursor-not-allowed {
  cursor: not-allowed;
}

.peer:disabled ~ .peer-disabled\:opacity-70 {
  opacity: .7;
}

.data-\[state\=checked\]\:bg-primary[data-state="checked"] {
  background-color: hsl(var(--primary));
}

.data-\[state\=open\]\:bg-secondary[data-state="open"] {
  background-color: hsl(var(--secondary));
}

.data-\[state\=checked\]\:text-primary-foreground[data-state="checked"] {
  color: hsl(var(--primary-foreground));
}

.data-\[state\=closed\]\:duration-300[data-state="closed"] {
  transition-duration: .3s;
}

.data-\[state\=open\]\:duration-500[data-state="open"] {
  transition-duration: .5s;
}

.data-\[state\=open\]\:animate-in[data-state="open"] {
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
  animation-name: enter;
  animation-duration: .15s;
}

.data-\[state\=closed\]\:animate-out[data-state="closed"] {
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
  animation-name: exit;
  animation-duration: .15s;
}

.data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
  --tw-exit-opacity: 0;
}

.data-\[state\=open\]\:fade-in-0[data-state="open"] {
  --tw-enter-opacity: 0;
}

.data-\[state\=closed\]\:slide-out-to-bottom[data-state="closed"] {
  --tw-exit-translate-y: 100%;
}

.data-\[state\=closed\]\:slide-out-to-left[data-state="closed"] {
  --tw-exit-translate-x: -100%;
}

.data-\[state\=closed\]\:slide-out-to-right[data-state="closed"] {
  --tw-exit-translate-x: 100%;
}

.data-\[state\=closed\]\:slide-out-to-top[data-state="closed"] {
  --tw-exit-translate-y: -100%;
}

.data-\[state\=open\]\:slide-in-from-bottom[data-state="open"] {
  --tw-enter-translate-y: 100%;
}

.data-\[state\=open\]\:slide-in-from-left[data-state="open"] {
  --tw-enter-translate-x: -100%;
}

.data-\[state\=open\]\:slide-in-from-right[data-state="open"] {
  --tw-enter-translate-x: 100%;
}

.data-\[state\=open\]\:slide-in-from-top[data-state="open"] {
  --tw-enter-translate-y: -100%;
}

.data-\[state\=closed\]\:duration-300[data-state="closed"] {
  animation-duration: .3s;
}

.data-\[state\=open\]\:duration-500[data-state="open"] {
  animation-duration: .5s;
}

@supports ((-webkit-backdrop-filter: var(--tw)) or (backdrop-filter: var(--tw))) {
  .supports-\[backdrop-filter\]\:bg-neutral-charcoal-dark\/40 {
    background-color: rgba(45, 42, 38, .4);
  }

  .supports-\[backdrop-filter\]\:bg-neutral-charcoal-dark\/70 {
    background-color: rgba(45, 42, 38, .7);
  }

  .supports-\[backdrop-filter\]\:bg-neutral-charcoal-light\/15 {
    background-color: rgba(51, 51, 51, .15);
  }
}

.dark\:bg-\[var\(--master-input-bg-dark\)\]:is(.dark *) {
  background-color: var(--master-input-bg-dark);
}

.dark\:bg-brand-gold\/10:is(.dark *) {
  background-color: rgba(184, 149, 106, .1);
}

.dark\:font-light:is(.dark *) {
  font-weight: 300;
}

.dark\:text-\[var\(--master-text-primary-dark\)\]:is(.dark *) {
  color: var(--master-text-primary-dark);
}

.dark\:text-black:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(0, 0, 0, var(--tw-text-opacity, 1));
}

.dark\:text-brand-gold:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(184, 149, 106, var(--tw-text-opacity, 1));
}

.dark\:\[--base-color\:var\(--global-text-shimmer-base\)\]:is(.dark *) {
  --base-color: var(--global-text-shimmer-base);
}

.dark\:\[--base-gradient-color\:var\(--global-text-shimmer-highlight\)\]:is(.dark *) {
  --base-gradient-color: var(--global-text-shimmer-highlight);
}

.dark\:placeholder\:text-\[var\(--master-text-secondary-dark\)\]:is(.dark *)::placeholder {
  color: var(--master-text-secondary-dark);
}

.dark\:hover\:bg-brand-gold\/5:hover:is(.dark *) {
  background-color: rgba(184, 149, 106, .05);
}

.dark\:hover\:text-brand-gold:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgba(184, 149, 106, var(--tw-text-opacity, 1));
}

.dark\:hover\:shadow-\[0_0_20px_hsl\(var\(--primary\)\/10\%\)\]:hover:is(.dark *) {
  --tw-shadow: 0 0 20px hsl(var(--primary) / 10%);
  --tw-shadow-colored: 0 0 20px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--tw-shadow);
}

@media (min-width: 640px) {
  .sm\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .sm\:mb-12 {
    margin-bottom: var(--space-12);
  }

  .sm\:mb-16 {
    margin-bottom: var(--space-16);
  }

  .sm\:mb-4 {
    margin-bottom: var(--space-4);
  }

  .sm\:mb-6 {
    margin-bottom: var(--space-6);
  }

  .sm\:mb-8 {
    margin-bottom: var(--space-8);
  }

  .sm\:mt-10 {
    margin-top: var(--space-10);
  }

  .sm\:block {
    display: block;
  }

  .sm\:h-10 {
    height: var(--space-10);
  }

  .sm\:h-16 {
    height: var(--space-16);
  }

  .sm\:h-20 {
    height: var(--space-20);
  }

  .sm\:h-8 {
    height: var(--space-8);
  }

  .sm\:w-10 {
    width: var(--space-10);
  }

  .sm\:w-16 {
    width: var(--space-16);
  }

  .sm\:w-20 {
    width: var(--space-20);
  }

  .sm\:w-8 {
    width: var(--space-8);
  }

  .sm\:w-\[350px\] {
    width: 350px;
  }

  .sm\:w-auto {
    width: auto;
  }

  .sm\:min-w-\[320px\] {
    min-width: 320px;
  }

  .sm\:max-w-sm {
    max-width: 24rem;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:justify-end {
    justify-content: flex-end;
  }

  .sm\:gap-6 {
    gap: var(--space-6);
  }

  .sm\:gap-8 {
    gap: var(--space-8);
  }

  .sm\:space-x-2 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 0;
    margin-right: calc(var(--space-2) * var(--tw-space-x-reverse));
    margin-left: calc(var(--space-2) * calc(1 - var(--tw-space-x-reverse)));
  }

  .sm\:p-6 {
    padding: var(--space-6);
  }

  .sm\:px-4 {
    padding-left: var(--space-4);
    padding-right: var(--space-4);
  }

  .sm\:px-6 {
    padding-left: var(--space-6);
    padding-right: var(--space-6);
  }

  .sm\:px-8 {
    padding-left: var(--space-8);
    padding-right: var(--space-8);
  }

  .sm\:py-12 {
    padding-top: var(--space-12);
    padding-bottom: var(--space-12);
  }

  .sm\:py-16 {
    padding-top: var(--space-16);
    padding-bottom: var(--space-16);
  }

  .sm\:py-4 {
    padding-top: var(--space-4);
    padding-bottom: var(--space-4);
  }

  .sm\:pt-28 {
    padding-top: 7rem;
  }

  .sm\:pt-8 {
    padding-top: var(--space-8);
  }

  .sm\:text-left {
    text-align: left;
  }

  .sm\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .sm\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .sm\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .sm\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .sm\:text-base {
    font-size: 1rem;
    line-height: 1.5rem;
  }

  .sm\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .sm\:text-sm {
    font-size: .875rem;
    line-height: 1.25rem;
  }

  .sm\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  .sm\:leading-8 {
    line-height: 2rem;
  }

  .sm\:leading-none {
    line-height: 1;
  }
}

@media (min-width: 768px) {
  .md\:flex {
    display: flex;
  }

  .md\:hidden {
    display: none;
  }

  .md\:h-5 {
    height: var(--space-5);
  }

  .md\:h-6 {
    height: var(--space-6);
  }

  .md\:h-7 {
    height: var(--space-7);
  }

  .md\:w-5 {
    width: var(--space-5);
  }

  .md\:w-6 {
    width: var(--space-6);
  }

  .md\:w-7 {
    width: var(--space-7);
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:items-center {
    align-items: center;
  }

  .md\:rounded-\[1\.5rem\] {
    border-radius: 1.5rem;
  }

  .md\:p-12 {
    padding: var(--space-12);
  }

  .md\:p-3 {
    padding: var(--space-3);
  }

  .md\:px-6 {
    padding-left: var(--space-6);
    padding-right: var(--space-6);
  }

  .md\:py-12 {
    padding-top: var(--space-12);
    padding-bottom: var(--space-12);
  }

  .md\:text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .md\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .md\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .md\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .md\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .md\:text-sm {
    font-size: .875rem;
    line-height: 1.25rem;
  }

  .md\:text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}

@media (min-width: 1024px) {
  .lg\:order-1 {
    order: 1;
  }

  .lg\:order-2 {
    order: 2;
  }

  .lg\:col-span-1 {
    grid-column: span 1 / span 1;
  }

  .lg\:col-start-1 {
    grid-column-start: 1;
  }

  .lg\:col-start-2 {
    grid-column-start: 2;
  }

  .lg\:col-start-3 {
    grid-column-start: 3;
  }

  .lg\:col-end-2 {
    grid-column-end: 2;
  }

  .lg\:col-end-3 {
    grid-column-end: 3;
  }

  .lg\:col-end-4 {
    grid-column-end: 4;
  }

  .lg\:row-start-1 {
    grid-row-start: 1;
  }

  .lg\:row-start-2 {
    grid-row-start: 2;
  }

  .lg\:row-start-3 {
    grid-row-start: 3;
  }

  .lg\:row-start-4 {
    grid-row-start: 4;
  }

  .lg\:row-end-2 {
    grid-row-end: 2;
  }

  .lg\:row-end-3 {
    grid-row-end: 3;
  }

  .lg\:row-end-4 {
    grid-row-end: 4;
  }

  .lg\:row-end-5 {
    grid-row-end: 5;
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-rows-3 {
    grid-template-rows: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-rows-4 {
    grid-template-rows: repeat(4, minmax(0, 1fr));
  }

  .lg\:gap-8 {
    gap: var(--space-8);
  }

  .lg\:p-8 {
    padding: var(--space-8);
  }

  .lg\:px-8 {
    padding-left: var(--space-8);
    padding-right: var(--space-8);
  }

  .lg\:py-16 {
    padding-top: var(--space-16);
    padding-bottom: var(--space-16);
  }

  .lg\:py-20 {
    padding-top: var(--space-20);
    padding-bottom: var(--space-20);
  }

  .lg\:py-24 {
    padding-top: var(--space-24);
    padding-bottom: var(--space-24);
  }

  .lg\:pt-32 {
    padding-top: 8rem;
  }

  .lg\:text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
  }

  .lg\:text-5xl {
    font-size: 3rem;
    line-height: 1;
  }

  .lg\:text-7xl {
    font-size: 4.5rem;
    line-height: 1;
  }
}

@media (min-width: 1280px) {
  .xl\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .xl\:text-8xl {
    font-size: 6rem;
    line-height: 1;
  }
}

.\[\&_svg\]\:pointer-events-none svg {
  pointer-events: none;
}

.\[\&_svg\]\:size-4 svg {
  width: var(--space-4);
  height: var(--space-4);
}

.\[\&_svg\]\:shrink-0 svg {
  flex-shrink: 0;
}

/*# sourceMappingURL=%5Broot-of-the-server%5D__8a280c37._.css.map*/