# 📚 Vierla Website Documentation

**Version:** 2.0  
**Last Updated:** January 16, 2025  
**Status:** Production Ready  

---

## 📋 **Table of Contents**

1. [Project Overview](#project-overview)
2. [Technical Architecture](#technical-architecture)
3. [Getting Started](#getting-started)
4. [Component Library](#component-library)
5. [SEO Implementation](#seo-implementation)
6. [Styling System](#styling-system)
7. [Deployment Guide](#deployment-guide)
8. [Maintenance & Updates](#maintenance--updates)
9. [Troubleshooting](#troubleshooting)

---

## 🎯 **Project Overview**

Vierla is a modern beauty services marketplace connecting clients with verified professionals across Canada. The website serves as both a customer-facing platform and a business management tool for beauty professionals.

### **Key Features**
- **Beauty Services Marketplace** - Connect clients with professionals
- **Business Management Platform** - Tools for providers to manage their business
- **Multi-step Application Process** - Professional onboarding system
- **Responsive Design** - Optimized for all devices
- **SEO Optimized** - Comprehensive structured data and metadata
- **Theme Support** - Light and dark mode with system preference detection

### **Target Audience**
- **Primary:** Beauty professionals (hair stylists, makeup artists, nail technicians, etc.)
- **Secondary:** Clients seeking beauty services
- **Tertiary:** Business owners looking for management tools

---

## 🏗️ **Technical Architecture**

### **Framework & Technologies**
- **Frontend:** Next.js 15.2.4 (React 19)
- **Styling:** Tailwind CSS with custom design system
- **Typography:** Playfair Display, Inter, Tai Heritage Pro, Farsan
- **Icons:** Lucide React
- **State Management:** React hooks and context
- **Build Tool:** Next.js built-in bundler (Turbopack)
- **Package Manager:** npm

### **Project Structure**
```
vierla-rebuild/
├── app/                    # Next.js App Router pages
│   ├── layout.tsx         # Root layout with metadata
│   ├── page.tsx           # Homepage
│   ├── features/          # Features page
│   ├── pricing/           # Pricing page
│   ├── about/             # About page
│   ├── contact/           # Contact page
│   ├── providers/         # For providers page
│   ├── apply/             # Application page
│   ├── privacy/           # Privacy policy
│   ├── terms/             # Terms of service
│   ├── globals.css        # Global styles and CSS variables
│   ├── sitemap.ts         # Dynamic sitemap generation
│   └── robots.txt         # Search engine directives
├── components/            # Reusable components
│   ├── marketing/         # Marketing-specific components
│   ├── ui/               # UI components
│   ├── seo/              # SEO components
│   └── providers/        # Theme and context providers
├── public/               # Static assets
│   ├── logo-transparent.png
│   └── robots.txt
└── docs/                 # Documentation files
```

### **Key Dependencies**
```json
{
  "next": "15.2.4",
  "react": "19.0.0",
  "tailwindcss": "^3.4.1",
  "lucide-react": "^0.469.0",
  "next-themes": "^0.4.4"
}
```

---

## 🚀 **Getting Started**

### **Prerequisites**
- Node.js 18.17 or later
- npm 9.0 or later

### **Installation**
```bash
# Clone the repository
git clone <repository-url>
cd vierla-rebuild

# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Start production server
npm start
```

### **Environment Setup**
Create a `.env.local` file in the root directory:
```env
# Add environment variables as needed
NEXT_PUBLIC_SITE_URL=https://vierla.com
```

### **Development Commands**
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript type checking
```

---

## 🧩 **Component Library**

### **Core UI Components**

#### **AuroraBackgroundLayer**
Animated gradient background component with theme support.
```tsx
import { AuroraBackgroundLayer } from "@/components/ui/aurora-background";

<AuroraBackgroundLayer variant="default" />
```

#### **GoldenGlowingCardContainer**
Card component with glassmorphism effects and golden glow.
```tsx
import { GoldenGlowingCardContainer } from "@/components/ui/golden-glowing-card-container";

<GoldenGlowingCardContainer>
  <h3>Card Title</h3>
  <p>Card content</p>
</GoldenGlowingCardContainer>
```

#### **ShinyButton**
Interactive button with shimmer effects and customizable colors.
```tsx
import ShinyButton from "@/components/ui/shiny-button";

<ShinyButton 
  size="lg"
  background="#B8956A"
  shimmerColor="#F4F1E8"
>
  Click Me
</ShinyButton>
```

#### **BentoGrid & BentoCard**
Grid layout system for feature displays.
```tsx
import { BentoGrid, BentoCard } from "@/components/ui/bento-grid";

<BentoGrid>
  <BentoCard
    name="Feature Name"
    description="Feature description"
    href="/link"
    cta="Learn More"
    Icon={IconComponent}
  />
</BentoGrid>
```

### **Marketing Components**

#### **Navbar**
Main navigation with theme switcher and responsive design.
```tsx
import { Navbar } from "@/components/marketing/navbar";

<Navbar />
```

#### **Footer**
Site footer with links and company information.
```tsx
import { Footer } from "@/components/marketing/footer";

<Footer />
```

#### **MultiStepForm**
Application form with progress tracking.
```tsx
import { MultiStepForm } from "@/components/marketing/multistep-form";

<MultiStepForm />
```

### **SEO Components**

#### **StructuredData**
JSON-LD structured data for search engines.
```tsx
import { StructuredData } from "@/components/seo/structured-data";

<StructuredData page="features" />
```

#### **BreadcrumbSchema**
Breadcrumb navigation for SEO.
```tsx
import { BreadcrumbSchema, getBreadcrumbsForPage } from "@/components/seo/breadcrumb-schema";

<BreadcrumbSchema items={getBreadcrumbsForPage('features')} />
```

---

## 🔍 **SEO Implementation**

### **Metadata Configuration**
Each page has optimized metadata for search engines and social sharing.

#### **Global Metadata** (layout.tsx)
```tsx
export const metadata: Metadata = {
  metadataBase: new URL('https://vierla.com'),
  title: {
    default: 'Vierla - Self-Care, Simplified',
    template: '%s | Vierla'
  },
  description: "The ultimate marketplace connecting you with top beauty professionals...",
  keywords: ['beauty services', 'self-care', 'beauty professionals'],
  openGraph: {
    type: 'website',
    locale: 'en_CA',
    url: 'https://vierla.com',
    siteName: 'Vierla',
    // ... additional OG tags
  },
  twitter: {
    card: 'summary_large_image',
    // ... Twitter card configuration
  }
}
```

#### **Page-Specific Metadata**
Each page includes unique metadata optimized for its content:
- **Features:** Business tools and platform features
- **Pricing:** Pricing plans and value propositions
- **About:** Company mission and story
- **Contact:** Support and inquiry information

### **Structured Data**
Comprehensive JSON-LD implementation includes:
- **Organization Schema:** Business information and contact details
- **WebSite Schema:** Search functionality
- **Service Schema:** Beauty services catalog
- **BreadcrumbList Schema:** Navigation structure
- **Page-specific schemas:** SoftwareApplication, Product, etc.

### **Technical SEO**
- **Sitemap:** Dynamic XML sitemap at `/sitemap.xml`
- **Robots.txt:** Search engine directives at `/robots.txt`
- **Canonical URLs:** Proper URL canonicalization
- **Meta Robots:** Appropriate indexing directives

---

## 🎨 **Styling System**

### **Design System**
Vierla uses a comprehensive design system with:
- **8-point grid system** for consistent spacing
- **Dual-theme support** (Light: Natural Wellness, Dark: Modern Luxury)
- **Consistent glassmorphism effects** across all components
- **Golden color scheme** with sage green accents

### **Color Palette**

#### **Light Mode (Natural Wellness)**
```css
--background: #F4F1E8;           /* Warm off-white */
--foreground: #364035;           /* Vierla Forest */
--primary: #364035;              /* Vierla Forest */
--secondary: #8B9A8C;            /* Vierla Sage */
--accent: #B8956A;               /* Vierla Gold */
```

#### **Dark Mode (Modern Luxury)**
```css
--background: #2D2A26;           /* Deep charcoal */
--foreground: #F4F1E8;           /* Light off-white */
--primary: #B8956A;              /* Vierla Gold */
--secondary: #A9A299;            /* Muted champagne */
--accent: #B8956A;               /* Vierla Gold */
```

### **Typography**
```css
--font-playfair: 'Playfair Display';  /* Headlines */
--font-inter: 'Inter';                /* Body text */
--font-tai-heritage: 'Tai Heritage Pro'; /* Section headers */
--font-farsan: 'Farsan';              /* Decorative */
```

### **Glassmorphism Classes**
```css
.glassmorphism-header    /* Headers/footers - 30% opacity */
.glassmorphism-light     /* Subtle effects - 15% opacity */
.glassmorphism-medium    /* Cards/modals - 25% opacity */
.glassmorphism-heavy     /* Overlays - 40% opacity */
.glassmorphism-card      /* Content cards - 20% opacity */
```

### **CSS Variables**
The system uses CSS variables for consistent theming:
```css
/* Aurora Background Controls */
--aurora-color-1: #79887A;
--aurora-color-2: #8B9A8C;
--aurora-color-3: #ABB7AB;
--aurora-color-4: #E1E6E1;

/* Component Controls */
--header-footer-bg: rgba(246, 247, 246, 0.3);
--header-footer-backdrop-blur: blur(20px);
--card-bg: rgba(245, 250, 247, 0.2);
```

---

## 🚀 **Deployment Guide**

### **Production Build**
```bash
# Build the application
npm run build

# Test the production build locally
npm start
```

### **Environment Variables**
Set the following environment variables in production:
```env
NEXT_PUBLIC_SITE_URL=https://vierla.com
NODE_ENV=production
```

### **Deployment Platforms**

#### **Vercel (Recommended)**
1. Connect your GitHub repository to Vercel
2. Configure environment variables
3. Deploy automatically on push to main branch

#### **Netlify**
1. Build command: `npm run build`
2. Publish directory: `.next`
3. Configure environment variables

#### **Custom Server**
1. Build the application: `npm run build`
2. Start the server: `npm start`
3. Ensure Node.js 18+ is available
4. Configure reverse proxy (nginx/Apache)

### **Performance Optimization**
- **Image Optimization:** Next.js automatic image optimization
- **Code Splitting:** Automatic with Next.js App Router
- **Caching:** Static assets cached with appropriate headers
- **Compression:** Enable gzip/brotli compression

---

## 🔧 **Maintenance & Updates**

### **Regular Maintenance Tasks**

#### **Monthly**
- [ ] Update dependencies: `npm update`
- [ ] Review and update content
- [ ] Check for broken links
- [ ] Monitor Core Web Vitals
- [ ] Review SEO performance

#### **Quarterly**
- [ ] Major dependency updates
- [ ] Security audit: `npm audit`
- [ ] Performance optimization review
- [ ] Content strategy review
- [ ] Analytics review

#### **Annually**
- [ ] Complete SEO audit
- [ ] Design system review
- [ ] Accessibility audit
- [ ] Security penetration testing

### **Content Updates**

#### **Adding New Pages**
1. Create page file in `app/` directory
2. Add metadata configuration
3. Include structured data components
4. Update sitemap.ts
5. Add navigation links if needed

#### **Updating Existing Content**
1. Modify page content
2. Update metadata if needed
3. Test in development
4. Deploy changes

### **Component Updates**

#### **Adding New Components**
1. Create component in appropriate directory
2. Follow existing naming conventions
3. Include TypeScript types
4. Add to component documentation
5. Test across different themes

#### **Modifying Existing Components**
1. Test changes thoroughly
2. Ensure backward compatibility
3. Update documentation
4. Test in both light and dark themes

---

## 🐛 **Troubleshooting**

### **Common Issues**

#### **Build Errors**
```bash
# Clear Next.js cache
rm -rf .next

# Reinstall dependencies
rm -rf node_modules package-lock.json
npm install

# Check for TypeScript errors
npm run type-check
```

#### **Styling Issues**
- Verify CSS variables are properly defined
- Check Tailwind class names for typos
- Ensure theme provider is properly configured
- Test in both light and dark modes

#### **SEO Issues**
- Validate structured data with Google's Rich Results Test
- Check sitemap accessibility at `/sitemap.xml`
- Verify robots.txt at `/robots.txt`
- Test metadata with social media debuggers

#### **Performance Issues**
- Use Next.js built-in performance monitoring
- Check for large bundle sizes
- Optimize images and assets
- Review Core Web Vitals

### **Debug Commands**
```bash
# Analyze bundle size
npm run build && npm run analyze

# Check for unused dependencies
npx depcheck

# Security audit
npm audit

# Type checking
npm run type-check
```

### **Getting Help**
- Check Next.js documentation: https://nextjs.org/docs
- Review Tailwind CSS documentation: https://tailwindcss.com/docs
- Consult component library documentation
- Review Git commit history for recent changes

---

**Documentation maintained by:** Augment Agent  
**Next review date:** February 16, 2025
