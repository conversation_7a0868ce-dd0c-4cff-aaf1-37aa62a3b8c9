{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AuroraBackground = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackground() from the server but AuroraBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx <module evaluation>\",\n    \"AuroraBackground\",\n);\nexport const AuroraBackgroundLayer = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackgroundLayer() from the server but AuroraBackgroundLayer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx <module evaluation>\",\n    \"AuroraBackgroundLayer\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,qEACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,qEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AuroraBackground = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackground() from the server but AuroraBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx\",\n    \"AuroraBackground\",\n);\nexport const AuroraBackgroundLayer = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackgroundLayer() from the server but AuroraBackgroundLayer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx\",\n    \"AuroraBackgroundLayer\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,iDACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,iDACA", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/golden-glowing-card-container.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const GoldenGlowingCardContainer = registerClientReference(\n    function() { throw new Error(\"Attempted to call GoldenGlowingCardContainer() from the server but GoldenGlowingCardContainer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glowing-card-container.tsx <module evaluation>\",\n    \"GoldenGlowingCardContainer\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ui/golden-glowing-card-container.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glowing-card-container.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,6BAA6B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5D;IAAa,MAAM,IAAI,MAAM;AAAoQ,GACjS,iFACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmT,GAChV,iFACA", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/golden-glowing-card-container.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const GoldenGlowingCardContainer = registerClientReference(\n    function() { throw new Error(\"Attempted to call GoldenGlowingCardContainer() from the server but GoldenGlowingCardContainer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glowing-card-container.tsx\",\n    \"GoldenGlowingCardContainer\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ui/golden-glowing-card-container.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glowing-card-container.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,6BAA6B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5D;IAAa,MAAM,IAAI,MAAM;AAAoQ,GACjS,6DACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "debugId": null}}, {"offset": {"line": 140, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/shiny-button.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ShinyButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call ShinyButton() from the server but ShinyButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/shiny-button.tsx <module evaluation>\",\n    \"ShinyButton\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ui/shiny-button.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/shiny-button.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,gEACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/shiny-button.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ShinyButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call ShinyButton() from the server but ShinyButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/shiny-button.tsx\",\n    \"ShinyButton\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ui/shiny-button.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/shiny-button.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,4CACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "debugId": null}}, {"offset": {"line": 180, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 188, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/seo/breadcrumb-schema.tsx"], "sourcesContent": ["import Script from 'next/script'\n\ninterface BreadcrumbItem {\n  name: string\n  url: string\n}\n\ninterface BreadcrumbSchemaProps {\n  items: BreadcrumbItem[]\n}\n\nexport function BreadcrumbSchema({ items }: BreadcrumbSchemaProps) {\n  const breadcrumbSchema = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"BreadcrumbList\",\n    \"itemListElement\": items.map((item, index) => ({\n      \"@type\": \"ListItem\",\n      \"position\": index + 1,\n      \"name\": item.name,\n      \"item\": item.url\n    }))\n  }\n\n  return (\n    <Script\n      id=\"breadcrumb-schema\"\n      type=\"application/ld+json\"\n      dangerouslySetInnerHTML={{\n        __html: JSON.stringify(breadcrumbSchema)\n      }}\n    />\n  )\n}\n\n// Helper function to generate breadcrumbs for different pages\nexport function getBreadcrumbsForPage(page: string): BreadcrumbItem[] {\n  const baseBreadcrumb = { name: \"Home\", url: \"https://vierla.com\" }\n  \n  switch (page) {\n    case 'features':\n      return [\n        baseBreadcrumb,\n        { name: \"Features\", url: \"https://vierla.com/features\" }\n      ]\n    case 'pricing':\n      return [\n        baseBreadcrumb,\n        { name: \"Pricing\", url: \"https://vierla.com/pricing\" }\n      ]\n    case 'about':\n      return [\n        baseBreadcrumb,\n        { name: \"About\", url: \"https://vierla.com/about\" }\n      ]\n    case 'contact':\n      return [\n        baseBreadcrumb,\n        { name: \"Contact\", url: \"https://vierla.com/contact\" }\n      ]\n    case 'providers':\n      return [\n        baseBreadcrumb,\n        { name: \"For Providers\", url: \"https://vierla.com/providers\" }\n      ]\n    case 'apply':\n      return [\n        baseBreadcrumb,\n        { name: \"For Providers\", url: \"https://vierla.com/providers\" },\n        { name: \"Apply\", url: \"https://vierla.com/apply\" }\n      ]\n    case 'privacy':\n      return [\n        baseBreadcrumb,\n        { name: \"Privacy Policy\", url: \"https://vierla.com/privacy\" }\n      ]\n    case 'terms':\n      return [\n        baseBreadcrumb,\n        { name: \"Terms of Service\", url: \"https://vierla.com/terms\" }\n      ]\n    default:\n      return [baseBreadcrumb]\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAWO,SAAS,iBAAiB,EAAE,KAAK,EAAyB;IAC/D,MAAM,mBAAmB;QACvB,YAAY;QACZ,SAAS;QACT,mBAAmB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC7C,SAAS;gBACT,YAAY,QAAQ;gBACpB,QAAQ,KAAK,IAAI;gBACjB,QAAQ,KAAK,GAAG;YAClB,CAAC;IACH;IAEA,qBACE,8OAAC,8HAAA,CAAA,UAAM;QACL,IAAG;QACH,MAAK;QACL,yBAAyB;YACvB,QAAQ,KAAK,SAAS,CAAC;QACzB;;;;;;AAGN;AAGO,SAAS,sBAAsB,IAAY;IAChD,MAAM,iBAAiB;QAAE,MAAM;QAAQ,KAAK;IAAqB;IAEjE,OAAQ;QACN,KAAK;YACH,OAAO;gBACL;gBACA;oBAAE,MAAM;oBAAY,KAAK;gBAA8B;aACxD;QACH,KAAK;YACH,OAAO;gBACL;gBACA;oBAAE,MAAM;oBAAW,KAAK;gBAA6B;aACtD;QACH,KAAK;YACH,OAAO;gBACL;gBACA;oBAAE,MAAM;oBAAS,KAAK;gBAA2B;aAClD;QACH,KAAK;YACH,OAAO;gBACL;gBACA;oBAAE,MAAM;oBAAW,KAAK;gBAA6B;aACtD;QACH,KAAK;YACH,OAAO;gBACL;gBACA;oBAAE,MAAM;oBAAiB,KAAK;gBAA+B;aAC9D;QACH,KAAK;YACH,OAAO;gBACL;gBACA;oBAAE,MAAM;oBAAiB,KAAK;gBAA+B;gBAC7D;oBAAE,MAAM;oBAAS,KAAK;gBAA2B;aAClD;QACH,KAAK;YACH,OAAO;gBACL;gBACA;oBAAE,MAAM;oBAAkB,KAAK;gBAA6B;aAC7D;QACH,KAAK;YACH,OAAO;gBACL;gBACA;oBAAE,MAAM;oBAAoB,KAAK;gBAA2B;aAC7D;QACH;YACE,OAAO;gBAAC;aAAe;IAC3B;AACF", "debugId": null}}, {"offset": {"line": 303, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/app/pricing/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\nimport { AuroraBackgroundLayer } from \"@/components/ui/aurora-background\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { GoldenGlowingCardContainer } from \"@/components/ui/golden-glowing-card-container\";\nimport ShinyButton from \"@/components/ui/shiny-button\";\nimport { StructuredData } from \"@/components/seo/structured-data\";\nimport { BreadcrumbSchema, getBreadcrumbsForPage } from \"@/components/seo/breadcrumb-schema\";\nimport { Check } from \"lucide-react\";\nimport Link from \"next/link\";\n\nexport const metadata: Metadata = {\n  title: 'Pricing - Affordable Plans for Every Business',\n  description: 'Choose the perfect Vierla plan for your beauty business. From individual professionals to large enterprises, we have flexible pricing options that grow with you.',\n  openGraph: {\n    title: 'Pricing - Affordable Plans for Every Business | Vierla',\n    description: 'Flexible pricing plans for beauty professionals and businesses. Start free and scale as you grow.',\n    url: 'https://vierla.com/pricing',\n    siteName: 'Vierla',\n    locale: 'en_CA',\n    type: 'website',\n  },\n  twitter: {\n    card: 'summary_large_image',\n    title: 'Pricing - Affordable Plans for Every Business | Vierla',\n    description: 'Flexible pricing plans for beauty professionals and businesses. Start free and scale as you grow.',\n  },\n};\n\nexport default function Pricing() {\n  return (\n    <>\n      <StructuredData page=\"pricing\" />\n      <BreadcrumbSchema items={getBreadcrumbsForPage('pricing')} />\n      <PricingContent />\n    </>\n  )\n}\n\nfunction PricingContent() {\n  const plans = [\n    {\n      name: \"Individual\",\n      description: \"Perfect for solo beauty professionals\",\n      price: \"Free\",\n      features: [\n        \"Service Listing\",\n        \"Website Builder\",\n        \"Bookings/month\",\n        \"Email Support\",\n        \"Templates\",\n        \"Payment Processing\"\n      ],\n      popular: false,\n      cta: \"Get Started Free\"\n    },\n    {\n      name: \"Medium Business\",\n      description: \"Most popular for growing beauty businesses\",\n      price: \"$29/month\",\n      features: [\n        \"Unlimited Service Listings\",\n        \"Full Website Builder\",\n        \"Unlimited Bookings\",\n        \"Integrated CRM\",\n        \"Advanced Analytics\",\n        \"Priority Support\",\n        \"Custom Domain\",\n        \"Premium Templates\",\n        \"Team Management (up to 5)\"\n      ],\n      popular: true,\n      cta: \"Start Pro Trial\"\n    },\n    {\n      name: \"Large Business\",\n      description: \"For established beauty enterprises\",\n      price: \"$79/month\",\n      features: [\n        \"Everything in Medium Business\",\n        \"Unlimited Team Members\",\n        \"White-label Options\",\n        \"API Access\",\n        \"Dedicated Account Manager\",\n        \"Custom Integrations\",\n        \"Advanced Automation\",\n        \"Multi-location Support\"\n      ],\n      popular: false,\n      cta: \"Contact Sales\"\n    }\n  ];\n\n  return (\n    <div className=\"page-pricing relative overflow-hidden\">\n      <AuroraBackgroundLayer />\n\n      {/* Hero Section */}\n      <section className=\"relative z-10 w-full px-4 py-20 pt-36\">\n        <div className=\"text-center max-w-6xl mx-auto\">\n          <h1 className=\"text-4xl md:text-6xl font-black mb-6 leading-none text-[#2D2A26] dark:text-[#F4F1E8] drop-shadow-lg font-sans\">\n            SIMPLE, TRANSPARENT PRICING\n          </h1>\n          <p className=\"text-xl md:text-2xl text-[#2D2A26] dark:text-[#A9A299] mb-8 leading-relaxed max-w-4xl mx-auto drop-shadow-sm font-inter\">\n            Choose the plan that fits your business needs. Start free and scale as you grow.\n          </p>\n        </div>\n      </section>\n\n      {/* Pricing Cards */}\n      <section className=\"relative z-10 py-20 border-t border-white/20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"grid gap-6 lg:grid-cols-3 max-w-6xl mx-auto\">\n            {plans.map((plan, index) => (\n              <div key={index} className=\"relative h-full\">\n                <GoldenGlowingCardContainer>\n                  <div className=\"relative h-full flex flex-col\">\n                    {plan.popular && (\n                      <div className=\"absolute -top-3 left-1/2 transform -translate-x-1/2 z-10\">\n                        <Badge className=\"bg-[var(--master-brand-accent)] text-[var(--master-text-primary-light)] font-inter\">Most Popular</Badge>\n                      </div>\n                    )}\n                    <div className=\"text-center pb-8\">\n                      <h3 className=\"text-2xl font-bold text-[#2D2A26] dark:text-[#F4F1E8] drop-shadow-lg mb-2 font-tai-heritage\">{plan.name}</h3>\n                      <p className=\"text-[var(--master-text-secondary-light)] dark:text-[var(--master-text-secondary-dark)] drop-shadow-sm mb-4 font-sans\">{plan.description}</p>\n                      <div className=\"mt-4\">\n                        <span className=\"text-4xl font-black text-[var(--master-brand-primary-light)] dark:text-[var(--master-brand-primary-dark)] drop-shadow-lg font-tai-heritage\">{plan.price}</span>\n                      </div>\n                    </div>\n                    <div className=\"flex-grow flex flex-col\">\n                      <ul className=\"space-y-3 mb-8 flex-grow\">\n                        {plan.features.map((feature, idx) => (\n                          <li key={idx} className=\"flex items-center text-[var(--master-text-secondary-light)] dark:text-[var(--master-text-secondary-dark)] drop-shadow-sm font-inter\">\n                            <Check className=\"h-4 w-4 text-[var(--master-brand-primary-light)] dark:text-[var(--master-brand-primary-dark)] mr-3 flex-shrink-0 drop-shadow-sm\" />\n                            <span className=\"text-sm text-[#2D2A26] dark:text-[#F4F1E8]\">{feature}</span>\n                          </li>\n                        ))}\n                      </ul>\n                      {/* Button area */}\n                      <div className=\"mt-auto flex justify-center\">\n                        <Link href={plan.name === \"Large Business\" ? \"/contact\" : \"/apply\"} className=\"w-full\">\n                          <ShinyButton\n                              size=\"sm\"\n                              variant=\"primary\"\n                              backgroundColor=\"var(--master-brand-primary-light)\"\n                              shimmerColor=\"var(--master-text-primary-light)\"\n                              className=\"w-full max-w-[180px] mx-auto drop-shadow-sm text-sm whitespace-nowrap font-medium dark:bg-[var(--master-brand-primary-dark)] text-white dark:text-[var(--master-text-primary-dark)]\"\n                            >\n                              {plan.cta}\n                            </ShinyButton>\n                          </Link>\n                        </div>\n                      </div>\n                    </div>\n                  </GoldenGlowingCardContainer>\n                </div>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Comparison Table Section */}\n      <section className=\"relative z-10 py-20 border-t border-white/20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-5xl font-black text-[#2D2A26] dark:text-[#F4F1E8] mb-6 drop-shadow-lg font-jost\">\n              WHY CHOOSE VIERLA OVER OTHERS?\n            </h2>\n            <p className=\"text-xl text-warm-beige max-w-4xl mx-auto drop-shadow-sm font-sans\">\n              See how Vierla stands out from traditional booking platforms and competitors\n            </p>\n          </div>\n\n          <div className=\"max-w-6xl mx-auto\">\n            <GoldenGlowingCardContainer>\n              <div className=\"overflow-x-auto\">\n                <table className=\"w-full text-left\">\n                  <thead>\n                    <tr className=\"border-b border-sage/20\">\n                      <th className=\"py-4 px-6 text-light-off-white font-bold text-lg font-tai-heritage\">Features</th>\n                      <th className=\"py-4 px-6 text-center\">\n                        <div className=\"flex flex-col items-center\">\n                          <span className=\"text-muted-gold font-bold text-lg mb-1 font-tai-heritage\">Vierla</span>\n                          <span className=\"text-xs bg-muted-gold/20 px-2 py-1 rounded-full text-muted-gold font-sans\">All-in-One</span>\n                        </div>\n                      </th>\n                      <th className=\"py-4 px-6 text-center text-warm-beige/70 font-medium font-sans\">Traditional Salons</th>\n                      <th className=\"py-4 px-6 text-center text-warm-beige/70 font-medium font-sans\">Other Platforms</th>\n                    </tr>\n                  </thead>\n                  <tbody className=\"text-warm-beige/80 font-sans\">\n                    <tr className=\"border-b border-sage/10\">\n                      <td className=\"py-4 px-6 font-medium\">Mobile Service Available</td>\n                      <td className=\"py-4 px-6 text-center\">\n                        <svg className=\"w-6 h-6 text-sage mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                      </td>\n                      <td className=\"py-4 px-6 text-center\">\n                        <svg className=\"w-6 h-6 text-error mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                        </svg>\n                      </td>\n                      <td className=\"py-4 px-6 text-center text-[var(--master-text-secondary-light)] dark:text-[var(--master-text-secondary-dark)]\">Limited</td>\n                    </tr>\n                    <tr className=\"border-b border-sage/10\">\n                      <td className=\"py-4 px-6 font-medium\">Verified Professionals</td>\n                      <td className=\"py-4 px-6 text-center\">\n                        <svg className=\"w-6 h-6 text-sage mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                      </td>\n                      <td className=\"py-4 px-6 text-center\">\n                        <svg className=\"w-6 h-6 text-sage mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                      </td>\n                      <td className=\"py-4 px-6 text-center text-[var(--master-text-secondary-light)] dark:text-[var(--master-text-secondary-dark)]\">Basic</td>\n                    </tr>\n                    <tr className=\"border-b border-sage/10\">\n                      <td className=\"py-4 px-6 font-medium\">Same-Day Booking</td>\n                      <td className=\"py-4 px-6 text-center\">\n                        <svg className=\"w-6 h-6 text-sage mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                      </td>\n                      <td className=\"py-4 px-6 text-center\">\n                        <svg className=\"w-6 h-6 text-error mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                        </svg>\n                      </td>\n                      <td className=\"py-4 px-6 text-center text-[var(--master-text-secondary-light)] dark:text-[var(--master-text-secondary-dark)]\">Rare</td>\n                    </tr>\n                    <tr className=\"border-b border-sage/10\">\n                      <td className=\"py-4 px-6 font-medium\">Transparent Pricing</td>\n                      <td className=\"py-4 px-6 text-center\">\n                        <svg className=\"w-6 h-6 text-sage mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                      </td>\n                      <td className=\"py-4 px-6 text-center text-[var(--master-text-secondary-light)] dark:text-[var(--master-text-secondary-dark)]\">Variable</td>\n                      <td className=\"py-4 px-6 text-center text-[var(--master-text-secondary-light)] dark:text-[var(--master-text-secondary-dark)]\">Hidden Fees</td>\n                    </tr>\n                    <tr className=\"border-b border-sage/10\">\n                      <td className=\"py-4 px-6 font-medium\">Money-Back Guarantee</td>\n                      <td className=\"py-4 px-6 text-center\">\n                        <svg className=\"w-6 h-6 text-sage mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                      </td>\n                      <td className=\"py-4 px-6 text-center\">\n                        <svg className=\"w-6 h-6 text-error mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                        </svg>\n                      </td>\n                      <td className=\"py-4 px-6 text-center\">\n                        <svg className=\"w-6 h-6 text-error mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                        </svg>\n                      </td>\n                    </tr>\n                    <tr>\n                      <td className=\"py-4 px-6 font-medium\">24/7 Customer Support</td>\n                      <td className=\"py-4 px-6 text-center\">\n                        <svg className=\"w-6 h-6 text-sage mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 13l4 4L19 7\" />\n                        </svg>\n                      </td>\n                      <td className=\"py-4 px-6 text-center text-[var(--master-text-secondary-light)] dark:text-[var(--master-text-secondary-dark)]\">Business Hours</td>\n                      <td className=\"py-4 px-6 text-center text-[var(--master-text-secondary-light)] dark:text-[var(--master-text-secondary-dark)]\">Limited</td>\n                    </tr>\n                  </tbody>\n                </table>\n              </div>\n            </GoldenGlowingCardContainer>\n\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"relative z-10 py-12 border-t border-white/20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <GoldenGlowingCardContainer interactive={false}>\n              <div className=\"text-center pt-8 pb-4 px-6\">\n                <h2 className=\"font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl text-[#2D2A26] dark:text-[#F4F1E8] drop-shadow-lg font-tai-heritage mb-6\">\n                  Ready to get started?\n                </h2>\n                <p className=\"max-w-[42rem] leading-normal text-[#2D2A26] dark:text-[#A9A299] sm:text-xl sm:leading-8 drop-shadow-sm font-sans mb-8 mx-auto\">\n                  Join thousands of entrepreneurs who trust Vierla to power their business.\n                </p>\n                <div className=\"flex justify-center\">\n                  <Link href=\"/apply\">\n                    <ShinyButton\n                      backgroundColor=\"var(--master-brand-primary-light)\"\n                      shimmerColor=\"var(--master-text-primary-light)\"\n                      className=\"dark:bg-[var(--master-brand-primary-dark)] text-white dark:text-[var(--master-text-primary-dark)]\"\n                    >\n                      Start Free Trial\n                    </ShinyButton>\n                  </Link>\n                </div>\n              </div>\n            </GoldenGlowingCardContainer>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,WAAW;QACT,OAAO;QACP,aAAa;QACb,KAAK;QACL,UAAU;QACV,QAAQ;QACR,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;IACf;AACF;AAEe,SAAS;IACtB,qBACE;;0BACE,8OAAC,wIAAA,CAAA,iBAAc;gBAAC,MAAK;;;;;;0BACrB,8OAAC,0IAAA,CAAA,mBAAgB;gBAAC,OAAO,CAAA,GAAA,0IAAA,CAAA,wBAAqB,AAAD,EAAE;;;;;;0BAC/C,8OAAC;;;;;;;AAGP;AAEA,SAAS;IACP,MAAM,QAAQ;QACZ;YACE,MAAM;YACN,aAAa;YACb,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,SAAS;YACT,KAAK;QACP;QACA;YACE,MAAM;YACN,aAAa;YACb,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,SAAS;YACT,KAAK;QACP;QACA;YACE,MAAM;YACN,aAAa;YACb,OAAO;YACP,UAAU;gBACR;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YACD,SAAS;YACT,KAAK;QACP;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,yIAAA,CAAA,wBAAqB;;;;;0BAGtB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAgH;;;;;;sCAG9H,8OAAC;4BAAE,WAAU;sCAA0H;;;;;;;;;;;;;;;;;0BAO3I,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;gCAAgB,WAAU;0CACzB,cAAA,8OAAC,2JAAA,CAAA,6BAA0B;8CACzB,cAAA,8OAAC;wCAAI,WAAU;;4CACZ,KAAK,OAAO,kBACX,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,0HAAA,CAAA,QAAK;oDAAC,WAAU;8DAAqF;;;;;;;;;;;0DAG1G,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAA+F,KAAK,IAAI;;;;;;kEACtH,8OAAC;wDAAE,WAAU;kEAAyH,KAAK,WAAW;;;;;;kEACtJ,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAK,WAAU;sEAA8I,KAAK,KAAK;;;;;;;;;;;;;;;;;0DAG5K,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEACX,KAAK,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,oBAC3B,8OAAC;gEAAa,WAAU;;kFACtB,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;kFACjB,8OAAC;wEAAK,WAAU;kFAA8C;;;;;;;+DAFvD;;;;;;;;;;kEAOb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4DAAC,MAAM,KAAK,IAAI,KAAK,mBAAmB,aAAa;4DAAU,WAAU;sEAC5E,cAAA,8OAAC,oIAAA,CAAA,UAAW;gEACR,MAAK;gEACL,SAAQ;gEACR,iBAAgB;gEAChB,cAAa;gEACb,WAAU;0EAET,KAAK,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BAlCf;;;;;;;;;;;;;;;;;;;;0BAgDlB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuF;;;;;;8CAGrG,8OAAC;oCAAE,WAAU;8CAAqE;;;;;;;;;;;;sCAKpF,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,2JAAA,CAAA,6BAA0B;0CACzB,cAAA,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;0DACC,cAAA,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAG,WAAU;sEAAqE;;;;;;sEACnF,8OAAC;4DAAG,WAAU;sEACZ,cAAA,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAA2D;;;;;;kFAC3E,8OAAC;wEAAK,WAAU;kFAA4E;;;;;;;;;;;;;;;;;sEAGhG,8OAAC;4DAAG,WAAU;sEAAiE;;;;;;sEAC/E,8OAAC;4DAAG,WAAU;sEAAiE;;;;;;;;;;;;;;;;;0DAGnF,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAG,WAAU;0EAAwB;;;;;;0EACtC,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;oEAAI,WAAU;oEAA4B,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EACnF,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;;;;;;0EAGzE,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;oEAAI,WAAU;oEAA6B,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EACpF,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;;;;;;0EAGzE,8OAAC;gEAAG,WAAU;0EAAgH;;;;;;;;;;;;kEAEhI,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAG,WAAU;0EAAwB;;;;;;0EACtC,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;oEAAI,WAAU;oEAA4B,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EACnF,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;;;;;;0EAGzE,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;oEAAI,WAAU;oEAA4B,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EACnF,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;;;;;;0EAGzE,8OAAC;gEAAG,WAAU;0EAAgH;;;;;;;;;;;;kEAEhI,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAG,WAAU;0EAAwB;;;;;;0EACtC,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;oEAAI,WAAU;oEAA4B,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EACnF,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;;;;;;0EAGzE,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;oEAAI,WAAU;oEAA6B,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EACpF,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;;;;;;0EAGzE,8OAAC;gEAAG,WAAU;0EAAgH;;;;;;;;;;;;kEAEhI,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAG,WAAU;0EAAwB;;;;;;0EACtC,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;oEAAI,WAAU;oEAA4B,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EACnF,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;;;;;;0EAGzE,8OAAC;gEAAG,WAAU;0EAAgH;;;;;;0EAC9H,8OAAC;gEAAG,WAAU;0EAAgH;;;;;;;;;;;;kEAEhI,8OAAC;wDAAG,WAAU;;0EACZ,8OAAC;gEAAG,WAAU;0EAAwB;;;;;;0EACtC,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;oEAAI,WAAU;oEAA4B,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EACnF,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;;;;;;0EAGzE,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;oEAAI,WAAU;oEAA6B,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EACpF,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;;;;;;0EAGzE,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;oEAAI,WAAU;oEAA6B,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EACpF,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;kEAI3E,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAwB;;;;;;0EACtC,8OAAC;gEAAG,WAAU;0EACZ,cAAA,8OAAC;oEAAI,WAAU;oEAA4B,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EACnF,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;;;;;;0EAGzE,8OAAC;gEAAG,WAAU;0EAAgH;;;;;;0EAC9H,8OAAC;gEAAG,WAAU;0EAAgH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAY9I,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,2JAAA,CAAA,6BAA0B;4BAAC,aAAa;sCACvC,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAuI;;;;;;kDAGrJ,8OAAC;wCAAE,WAAU;kDAAgI;;;;;;kDAG7I,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDACT,cAAA,8OAAC,oIAAA,CAAA,UAAW;gDACV,iBAAgB;gDAChB,cAAa;gDACb,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB", "debugId": null}}]}