{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,qMAAA,CAAA,aAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,aAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AuroraBackground = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackground() from the server but AuroraBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx <module evaluation>\",\n    \"AuroraBackground\",\n);\nexport const AuroraBackgroundLayer = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackgroundLayer() from the server but AuroraBackgroundLayer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx <module evaluation>\",\n    \"AuroraBackgroundLayer\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,qEACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,qEACA", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AuroraBackground = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackground() from the server but AuroraBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx\",\n    \"AuroraBackground\",\n);\nexport const AuroraBackgroundLayer = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackgroundLayer() from the server but AuroraBackgroundLayer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx\",\n    \"AuroraBackgroundLayer\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,iDACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,iDACA", "debugId": null}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/golden-glowing-card-container.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const GoldenGlowingCardContainer = registerClientReference(\n    function() { throw new Error(\"Attempted to call GoldenGlowingCardContainer() from the server but GoldenGlowingCardContainer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glowing-card-container.tsx <module evaluation>\",\n    \"GoldenGlowingCardContainer\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ui/golden-glowing-card-container.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glowing-card-container.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,6BAA6B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5D;IAAa,MAAM,IAAI,MAAM;AAAoQ,GACjS,iFACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmT,GAChV,iFACA", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/golden-glowing-card-container.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const GoldenGlowingCardContainer = registerClientReference(\n    function() { throw new Error(\"Attempted to call GoldenGlowingCardContainer() from the server but GoldenGlowingCardContainer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glowing-card-container.tsx\",\n    \"GoldenGlowingCardContainer\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ui/golden-glowing-card-container.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glowing-card-container.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,6BAA6B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5D;IAAa,MAAM,IAAI,MAAM;AAAoQ,GACjS,6DACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "debugId": null}}, {"offset": {"line": 183, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 191, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/shiny-button.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ShinyButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call ShinyButton() from the server but ShinyButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/shiny-button.tsx <module evaluation>\",\n    \"ShinyButton\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ui/shiny-button.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/shiny-button.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,gEACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkS,GAC/T,gEACA", "debugId": null}}, {"offset": {"line": 207, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/shiny-button.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ShinyButton = registerClientReference(\n    function() { throw new Error(\"Attempted to call ShinyButton() from the server but ShinyButton is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/shiny-button.tsx\",\n    \"ShinyButton\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ui/shiny-button.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/shiny-button.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,4CACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8Q,GAC3S,4CACA", "debugId": null}}, {"offset": {"line": 223, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 231, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/app/about/page.tsx"], "sourcesContent": ["import { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { AuroraBackgroundLayer } from \"@/components/ui/aurora-background\";\nimport { GoldenGlowingCardContainer } from \"@/components/ui/golden-glowing-card-container\";\nimport ShinyButton from \"@/components/ui/shiny-button\";\nimport { Users, Target, Lightbulb, Heart } from \"lucide-react\";\n\nexport default function About() {\n  return (\n    <div className=\"page-about min-h-screen relative overflow-hidden\">\n      <AuroraBackgroundLayer />\n\n      {/* Hero Section */}\n      <section className=\"relative z-10 w-full px-4 py-20 pt-32\">\n        <div className=\"text-center max-w-6xl mx-auto\">\n          <h1 className=\"text-4xl md:text-6xl font-black mb-6 leading-none drop-shadow-lg font-jost\"\n              style={{ color: 'var(--master-text-primary-dark)' }}>\n            WE'RE ON A MISSION TO EMPOWER BEAUTY & WELLNESS PROFESSIONALS\n          </h1>\n          <p className=\"text-xl md:text-2xl mb-8 leading-relaxed max-w-4xl mx-auto drop-shadow-sm font-inter\"\n             style={{ color: 'var(--master-text-secondary-dark)' }}>\n            Building the future of beauty and wellness business operations, one professional at a time.\n          </p>\n        </div>\n      </section>\n\n      {/* Our Mission Section */}\n      <section className=\"relative z-10 py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <div className=\"grid gap-8 lg:grid-cols-2 items-center\">\n              <div className=\"space-y-4\">\n                <GoldenGlowingCardContainer>\n                  <Card className=\"backdrop-blur-md shadow-xl rounded-2xl p-10 min-h-[400px] flex flex-col justify-center\"\n                        style={{\n                          backgroundColor: 'var(--card-bg)',\n                          borderColor: 'var(--border-subtle)',\n                          boxShadow: 'var(--shadow-card)'\n                        }}>\n                    <div className=\"flex items-center space-x-3 mb-8\">\n                      <Target className=\"h-10 w-10 drop-shadow-lg\" style={{ color: 'var(--icon-accent)' }} />\n                      <h2 className=\"text-4xl font-bold drop-shadow-lg font-tai-heritage\" style={{ color: 'var(--text-primary)' }}>Our Mission</h2>\n                    </div>\n                    <p className=\"text-xl leading-relaxed drop-shadow-sm mb-6 font-inter\" style={{ color: 'var(--master-text-secondary-dark)' }}>\n                      We started Vierla because we saw too many beauty and wellness professionals struggling to manage multiple software subscriptions for booking, payments, client management, and marketing. It's costly, complex, and takes time away from what matters most - serving clients. Our mission is to consolidate all the essential tools into a single, intelligent platform, giving beauty and wellness entrepreneurs their time back so they can focus on what they do best: perfecting their craft and growing their business.\n                    </p>\n                    <p className=\"text-xl leading-relaxed drop-shadow-sm font-inter\" style={{ color: 'var(--master-text-secondary-dark)' }}>\n                      Every feature we build is designed with the modern beauty and wellness professional in mind - from the solo esthetician just starting out to the growing salon scaling their operations. We believe that powerful business tools shouldn't require a technical degree or a massive budget to use effectively, especially in an industry focused on personal care and wellness.\n                    </p>\n                  </Card>\n                </GoldenGlowingCardContainer>\n              </div>\n              <div className=\"flex items-center justify-center\">\n                <div className=\"w-32 h-32 mx-auto rounded-full flex items-center justify-center border-2 shadow-lg\"\n                     style={{\n                       backgroundColor: 'var(--accent-bg)',\n                       borderColor: 'var(--border-accent)'\n                     }}>\n                  <Heart className=\"w-16 h-16 drop-shadow-lg\" style={{ color: 'var(--icon-accent)' }} />\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Our Vision Section */}\n      <section className=\"container space-y-6 py-8 md:py-12 lg:py-24\">\n        <div className=\"mx-auto max-w-4xl\">\n          <div className=\"grid gap-8 lg:grid-cols-2 items-center\">\n            <div className=\"flex items-center justify-center lg:order-1\">\n              <div className=\"w-32 h-32 mx-auto rounded-full flex items-center justify-center border-2 shadow-lg\"\n                   style={{\n                     backgroundColor: 'var(--accent-bg)',\n                     borderColor: 'var(--border-accent)'\n                   }}>\n                <Lightbulb className=\"w-16 h-16 drop-shadow-lg\" style={{ color: 'var(--icon-accent)' }} />\n              </div>\n            </div>\n            <div className=\"space-y-4 lg:order-2\">\n              <GoldenGlowingCardContainer>\n                <Card className=\"backdrop-blur-md shadow-xl rounded-2xl p-10 min-h-[400px] flex flex-col justify-center\"\n                      style={{\n                        backgroundColor: 'var(--card-bg)',\n                        borderColor: 'var(--border-subtle)',\n                        boxShadow: 'var(--shadow-card)'\n                      }}>\n                  <div className=\"flex items-center space-x-3 mb-8\">\n                    <Lightbulb className=\"h-10 w-10 drop-shadow-lg\" style={{ color: 'var(--icon-accent)' }} />\n                    <h2 className=\"text-4xl font-bold drop-shadow-lg font-tai-heritage\" style={{ color: 'var(--text-primary)' }}>Our Vision</h2>\n                  </div>\n                  <p className=\"text-xl leading-relaxed drop-shadow-sm mb-6 font-sans\" style={{ color: 'var(--text-secondary)' }}>\n                    We envision a future where starting and running a business is radically simpler. By leveraging the power of AI, we aim to automate administrative busywork and provide powerful insights that were once only available to large corporations. We're building the operating system for the next generation of business.\n                  </p>\n                  <p className=\"text-xl leading-relaxed drop-shadow-sm font-sans\" style={{ color: 'var(--text-secondary)' }}>\n                    Our vision extends beyond just software - we're creating an ecosystem where entrepreneurs can thrive, connect, and grow together. We believe that when we remove the barriers to business success, we unlock human potential and drive innovation across every industry.\n                  </p>\n                </Card>\n              </GoldenGlowingCardContainer>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      {/* Meet the Team Section */}\n      <section className=\"container space-y-6 py-8 md:py-12 lg:py-24 border-t border-white/20 mx-auto\">\n        <div className=\"mx-auto max-w-6xl\">\n          <div className=\"text-center mb-12\">\n            <div className=\"flex items-center justify-center space-x-3 mb-4\">\n              <Users className=\"h-10 w-10 drop-shadow-lg\" style={{ color: 'var(--icon-accent)' }} />\n              <h2 className=\"text-4xl font-bold drop-shadow-lg font-tai-heritage\" style={{ color: 'var(--text-primary)' }}>Meet the Team</h2>\n            </div>\n            <div className=\"w-full h-px mb-6\" style={{ background: 'linear-gradient(to right, transparent, var(--border-subtle), transparent)' }}></div>\n            <p className=\"text-xl max-w-2xl mx-auto drop-shadow-sm font-sans\" style={{ color: 'var(--text-secondary)' }}>\n              We're a passionate group of entrepreneurs, developers, and designers united by our mission to simplify business operations.\n            </p>\n          </div>\n\n          <div className=\"grid gap-8 md:grid-cols-2 lg:grid-cols-3 justify-center\">\n            {[1, 2, 3, 4, 5, 6].map((member) => (\n              <GoldenGlowingCardContainer key={member}>\n                <Card className=\"text-center backdrop-blur-md shadow-xl rounded-2xl min-h-[300px] flex flex-col justify-center p-8\"\n                      style={{\n                        backgroundColor: 'var(--card-bg)',\n                        borderColor: 'var(--border-subtle)',\n                        boxShadow: 'var(--shadow-card)'\n                      }}>\n                  <CardHeader className=\"text-center pb-6\">\n                    <div className=\"w-28 h-28 mx-auto rounded-full flex items-center justify-center mb-6 border-2 shadow-lg\"\n                         style={{\n                           backgroundColor: 'var(--accent-bg)',\n                           borderColor: 'var(--border-accent)'\n                         }}>\n                      <Users className=\"h-14 w-14 drop-shadow-lg\" style={{ color: 'var(--icon-accent)' }} />\n                    </div>\n                    <CardTitle className=\"drop-shadow-lg text-center font-tai-heritage text-2xl mb-2\"\n                               style={{ color: 'var(--text-primary)' }}>Team Member {member}</CardTitle>\n                    <CardDescription className=\"drop-shadow-sm text-center font-sans text-lg\"\n                                     style={{ color: 'var(--icon-accent)' }}>Position Title</CardDescription>\n                  </CardHeader>\n                  <CardContent className=\"text-center\">\n                    <p className=\"text-base drop-shadow-sm text-center font-sans leading-relaxed\"\n                       style={{ color: 'var(--text-secondary)' }}>\n                      Brief bio and background information will be added here.\n                    </p>\n                  </CardContent>\n                </Card>\n              </GoldenGlowingCardContainer>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"container space-y-6 py-8 md:py-12 lg:py-24 mx-auto\"\n               style={{ borderTop: '1px solid var(--border-subtle)' }}>\n        <div className=\"mx-auto flex max-w-[58rem] flex-col items-center space-y-6 text-center\">\n          <div className=\"w-full h-px mb-8\" style={{ background: 'linear-gradient(to right, transparent, var(--border-subtle), transparent)' }}></div>\n          <h2 className=\"font-heading text-3xl leading-[1.1] sm:text-4xl md:text-6xl drop-shadow-lg text-center font-tai-heritage\"\n              style={{ color: 'var(--text-primary)' }}>\n            Ready to join our mission?\n          </h2>\n          <p className=\"max-w-[42rem] leading-normal sm:text-xl sm:leading-8 drop-shadow-sm text-center font-sans\"\n             style={{ color: 'var(--text-secondary)' }}>\n            Be part of the future of business operations. Start your journey with Vierla today.\n          </p>\n          <div className=\"flex justify-center pt-4\">\n            <ShinyButton size=\"lg\" className=\"px-10 py-5 text-xl\">\n              <a href=\"/apply\">Get Started</a>\n            </ShinyButton>\n          </div>\n          <div className=\"w-full h-px mt-8\" style={{ background: 'linear-gradient(to right, transparent, var(--border-subtle), transparent)' }}></div>\n        </div>\n      </section>\n\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,yIAAA,CAAA,wBAAqB;;;;;0BAGtB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;4BACV,OAAO;gCAAE,OAAO;4BAAkC;sCAAG;;;;;;sCAGzD,8OAAC;4BAAE,WAAU;4BACV,OAAO;gCAAE,OAAO;4BAAoC;sCAAG;;;;;;;;;;;;;;;;;0BAO9D,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,2JAAA,CAAA,6BAA0B;kDACzB,cAAA,8OAAC,yHAAA,CAAA,OAAI;4CAAC,WAAU;4CACV,OAAO;gDACL,iBAAiB;gDACjB,aAAa;gDACb,WAAW;4CACb;;8DACJ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,sMAAA,CAAA,SAAM;4DAAC,WAAU;4DAA2B,OAAO;gEAAE,OAAO;4DAAqB;;;;;;sEAClF,8OAAC;4DAAG,WAAU;4DAAsD,OAAO;gEAAE,OAAO;4DAAsB;sEAAG;;;;;;;;;;;;8DAE/G,8OAAC;oDAAE,WAAU;oDAAyD,OAAO;wDAAE,OAAO;oDAAoC;8DAAG;;;;;;8DAG7H,8OAAC;oDAAE,WAAU;oDAAoD,OAAO;wDAAE,OAAO;oDAAoC;8DAAG;;;;;;;;;;;;;;;;;;;;;;8CAM9H,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCACV,OAAO;4CACL,iBAAiB;4CACjB,aAAa;wCACf;kDACH,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;4CAA2B,OAAO;gDAAE,OAAO;4CAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7F,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCACV,OAAO;wCACL,iBAAiB;wCACjB,aAAa;oCACf;8CACH,cAAA,8OAAC,4MAAA,CAAA,YAAS;wCAAC,WAAU;wCAA2B,OAAO;4CAAE,OAAO;wCAAqB;;;;;;;;;;;;;;;;0CAGzF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,2JAAA,CAAA,6BAA0B;8CACzB,cAAA,8OAAC,yHAAA,CAAA,OAAI;wCAAC,WAAU;wCACV,OAAO;4CACL,iBAAiB;4CACjB,aAAa;4CACb,WAAW;wCACb;;0DACJ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,4MAAA,CAAA,YAAS;wDAAC,WAAU;wDAA2B,OAAO;4DAAE,OAAO;wDAAqB;;;;;;kEACrF,8OAAC;wDAAG,WAAU;wDAAsD,OAAO;4DAAE,OAAO;wDAAsB;kEAAG;;;;;;;;;;;;0DAE/G,8OAAC;gDAAE,WAAU;gDAAwD,OAAO;oDAAE,OAAO;gDAAwB;0DAAG;;;;;;0DAGhH,8OAAC;gDAAE,WAAU;gDAAmD,OAAO;oDAAE,OAAO;gDAAwB;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWvH,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;4CAA2B,OAAO;gDAAE,OAAO;4CAAqB;;;;;;sDACjF,8OAAC;4CAAG,WAAU;4CAAsD,OAAO;gDAAE,OAAO;4CAAsB;sDAAG;;;;;;;;;;;;8CAE/G,8OAAC;oCAAI,WAAU;oCAAmB,OAAO;wCAAE,YAAY;oCAA4E;;;;;;8CACnI,8OAAC;oCAAE,WAAU;oCAAqD,OAAO;wCAAE,OAAO;oCAAwB;8CAAG;;;;;;;;;;;;sCAK/G,8OAAC;4BAAI,WAAU;sCACZ;gCAAC;gCAAG;gCAAG;gCAAG;gCAAG;gCAAG;6BAAE,CAAC,GAAG,CAAC,CAAC,uBACvB,8OAAC,2JAAA,CAAA,6BAA0B;8CACzB,cAAA,8OAAC,yHAAA,CAAA,OAAI;wCAAC,WAAU;wCACV,OAAO;4CACL,iBAAiB;4CACjB,aAAa;4CACb,WAAW;wCACb;;0DACJ,8OAAC,yHAAA,CAAA,aAAU;gDAAC,WAAU;;kEACpB,8OAAC;wDAAI,WAAU;wDACV,OAAO;4DACL,iBAAiB;4DACjB,aAAa;wDACf;kEACH,cAAA,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;4DAA2B,OAAO;gEAAE,OAAO;4DAAqB;;;;;;;;;;;kEAEnF,8OAAC,yHAAA,CAAA,YAAS;wDAAC,WAAU;wDACV,OAAO;4DAAE,OAAO;wDAAsB;;4DAAG;4DAAa;;;;;;;kEACjE,8OAAC,yHAAA,CAAA,kBAAe;wDAAC,WAAU;wDACV,OAAO;4DAAE,OAAO;wDAAqB;kEAAG;;;;;;;;;;;;0DAE3D,8OAAC,yHAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,8OAAC;oDAAE,WAAU;oDACV,OAAO;wDAAE,OAAO;oDAAwB;8DAAG;;;;;;;;;;;;;;;;;mCAtBnB;;;;;;;;;;;;;;;;;;;;;0BAkCzC,8OAAC;gBAAQ,WAAU;gBACV,OAAO;oBAAE,WAAW;gBAAiC;0BAC5D,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;4BAAmB,OAAO;gCAAE,YAAY;4BAA4E;;;;;;sCACnI,8OAAC;4BAAG,WAAU;4BACV,OAAO;gCAAE,OAAO;4BAAsB;sCAAG;;;;;;sCAG7C,8OAAC;4BAAE,WAAU;4BACV,OAAO;gCAAE,OAAO;4BAAwB;sCAAG;;;;;;sCAG9C,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,oIAAA,CAAA,UAAW;gCAAC,MAAK;gCAAK,WAAU;0CAC/B,cAAA,8OAAC;oCAAE,MAAK;8CAAS;;;;;;;;;;;;;;;;sCAGrB,8OAAC;4BAAI,WAAU;4BAAmB,OAAO;gCAAE,YAAY;4BAA4E;;;;;;;;;;;;;;;;;;;;;;;AAM7I", "debugId": null}}]}