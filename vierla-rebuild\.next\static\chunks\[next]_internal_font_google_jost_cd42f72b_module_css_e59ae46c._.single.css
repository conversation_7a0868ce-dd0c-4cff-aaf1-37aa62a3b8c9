/* [next]/internal/font/google/jost_cd42f72b.module.css [app-client] (css) */
@font-face {
  font-family: Jost;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/84530c28ff9c87e6-s.55edd5f8.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Jost;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/853c5792ac9a8261-s.506cdeeb.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Jost;
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../media/361ada22a6fc641e-s.p.ba172912.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Jost;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/84530c28ff9c87e6-s.55edd5f8.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Jost;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/853c5792ac9a8261-s.506cdeeb.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Jost;
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../media/361ada22a6fc641e-s.p.ba172912.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Jost;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/84530c28ff9c87e6-s.55edd5f8.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Jost;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/853c5792ac9a8261-s.506cdeeb.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Jost;
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../media/361ada22a6fc641e-s.p.ba172912.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Jost;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/84530c28ff9c87e6-s.55edd5f8.woff2") format("woff2");
  unicode-range: U+301, U+400-45F, U+490-491, U+4B0-4B1, U+2116;
}

@font-face {
  font-family: Jost;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/853c5792ac9a8261-s.506cdeeb.woff2") format("woff2");
  unicode-range: U+100-2BA, U+2BD-2C5, U+2C7-2CC, U+2CE-2D7, U+2DD-2FF, U+304, U+308, U+329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}

@font-face {
  font-family: Jost;
  font-style: normal;
  font-weight: 700;
  font-display: swap;
  src: url("../media/361ada22a6fc641e-s.p.ba172912.woff2") format("woff2");
  unicode-range: U+??, U+131, U+152-153, U+2BB-2BC, U+2C6, U+2DA, U+2DC, U+304, U+308, U+329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}

@font-face {
  font-family: Jost Fallback;
  src: local(Arial);
  ascent-override: 111.45%;
  descent-override: 39.06%;
  line-gap-override: 0.0%;
  size-adjust: 96.01%;
}

.jost_cd42f72b-module__Wo-Ogq__className {
  font-family: Jost, Jost Fallback;
  font-style: normal;
}

.jost_cd42f72b-module__Wo-Ogq__variable {
  --font-jost: "Jost", "Jost Fallback";
}

/*# sourceMappingURL=%5Bnext%5D_internal_font_google_jost_cd42f72b_module_css_e59ae46c._.single.css.map*/