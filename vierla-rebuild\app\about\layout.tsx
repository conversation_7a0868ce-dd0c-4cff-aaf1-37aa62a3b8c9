import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'About Us - Our Mission & Story',
  description: 'Learn about <PERSON><PERSON><PERSON>\'s mission to simplify self-care by connecting clients with top beauty professionals. Discover our story, values, and commitment to excellence.',
  openGraph: {
    title: 'About Us - Our Mission & Story | Vierla',
    description: 'Discover Vierla\'s mission to revolutionize the beauty industry by connecting clients with verified professionals.',
    url: 'https://vierla.com/about',
    siteName: 'Vierla',
    locale: 'en_CA',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'About Us - Our Mission & Story | Vierla',
    description: 'Discover Vierla\'s mission to revolutionize the beauty industry by connecting clients with verified professionals.',
  },
}

export default function AboutLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
