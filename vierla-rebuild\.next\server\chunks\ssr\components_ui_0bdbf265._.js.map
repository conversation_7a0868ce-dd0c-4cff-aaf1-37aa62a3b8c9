{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx"], "sourcesContent": ["\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport React, { memo, type ReactNode } from \"react\";\n\ninterface AuroraBackgroundProps extends React.HTMLProps<HTMLDivElement> {\n  children: ReactNode;\n  showRadialGradient?: boolean;\n}\n\n// Full page wrapper version\nexport const AuroraBackground = ({\n  className,\n  children,\n  showRadialGradient = true,\n  ...props\n}: AuroraBackgroundProps) => {\n  return (\n    <div\n      className={cn(\n        \"relative flex flex-col h-[100vh] items-center justify-center text-slate-950 transition-colors duration-300\",\n        className\n      )}\n      {...props}\n    >\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div\n          className={cn(\n            `\n            [--light-bg:#F4F1E8]\n            [--dark-bg:#2D2A26]\n            [--light-aurora:repeating-linear-gradient(100deg,#364035_10%,#8B9A8C_15%,#F0E6D9_20%,#7C9A85_25%,#364035_30%)]\n            [--dark-aurora:repeating-linear-gradient(100deg,#B8956A_10%,#4B3D6B_15%,#B8956A_20%,#4B3D6B_25%,#B8956A_30%)]\n            [--light-stripes:repeating-linear-gradient(100deg,var(--light-bg)_0%,var(--light-bg)_7%,transparent_10%,transparent_12%,var(--light-bg)_16%)]\n            [--dark-stripes:repeating-linear-gradient(100deg,var(--dark-bg)_0%,var(--dark-bg)_7%,transparent_10%,transparent_12%,var(--dark-bg)_16%)]\n            [background-image:var(--light-stripes),var(--light-aurora)]\n            dark:[background-image:var(--dark-stripes),var(--dark-aurora)]\n            [background-size:300%,_200%]\n            [background-position:50%_50%,50%_50%]\n            filter blur-[10px]\n            after:content-[\"\"] after:absolute after:inset-0 after:[background-image:var(--light-stripes),var(--light-aurora)]\n            after:dark:[background-image:var(--dark-stripes),var(--dark-aurora)]\n            after:[background-size:200%,_100%]\n            after:animate-aurora after:[background-attachment:fixed] after:mix-blend-difference\n            pointer-events-none\n            absolute -inset-[10px] opacity-50 will-change-transform`,\n            showRadialGradient && `[mask-image:radial-gradient(ellipse_at_100%_0%,black_10%,transparent_70%)]`\n          )}\n        ></div>\n      </div>\n      {children}\n    </div>\n  );\n};\n\n// Background-only version for existing layouts\nexport const AuroraBackgroundLayer = memo(\n  ({\n    className,\n    showRadialGradient = true,\n  }: {\n    className?: string;\n    showRadialGradient?: boolean;\n  }) => {\n    return (\n      <div className=\"fixed inset-0 -z-10 overflow-hidden\">\n        <div\n          className={cn(\n            `\n            [--light-bg:#F4F1E8]\n            [--dark-bg:#2D2A26]\n            [--light-aurora:repeating-linear-gradient(100deg,#7C9A85_10%,#8B9A8C_15%,#E8D5D5_20%,#7C9A85_25%,#8B9A8C_30%)]\n            [--dark-aurora:repeating-linear-gradient(100deg,#B8956A_10%,#4B3D6B_15%,#B8956A_20%,#4B3D6B_25%,#B8956A_30%)]\n            [--light-stripes:repeating-linear-gradient(100deg,var(--light-bg)_0%,var(--light-bg)_7%,transparent_10%,transparent_12%,var(--light-bg)_16%)]\n            [--dark-stripes:repeating-linear-gradient(100deg,var(--dark-bg)_0%,var(--dark-bg)_7%,transparent_10%,transparent_12%,var(--dark-bg)_16%)]\n            [background-image:var(--light-stripes),var(--light-aurora)]\n            dark:[background-image:var(--dark-stripes),var(--dark-aurora)]\n            [background-size:300%,_200%]\n            [background-position:50%_50%,50%_50%]\n            filter blur-[10px]\n            after:content-[\"\"] after:absolute after:inset-0 after:[background-image:var(--light-stripes),var(--light-aurora)]\n            after:dark:[background-image:var(--dark-stripes),var(--dark-aurora)]\n            after:[background-size:200%,_100%]\n            after:animate-aurora after:[background-attachment:fixed] after:mix-blend-difference\n            pointer-events-none\n            absolute -inset-[10px] opacity-50 will-change-transform`,\n            showRadialGradient && `[mask-image:radial-gradient(ellipse_at_100%_0%,black_10%,transparent_70%)]`,\n            className\n          )}\n        ></div>\n      </div>\n    );\n  }\n);\n\nAuroraBackgroundLayer.displayName = \"AuroraBackgroundLayer\";"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAWO,MAAM,mBAAmB,CAAC,EAC/B,SAAS,EACT,QAAQ,EACR,qBAAqB,IAAI,EACzB,GAAG,OACmB;IACtB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8GACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;;;;;;;;;;;;;;;mEAiBsD,CAAC,EACxD,sBAAsB,CAAC,0EAA0E,CAAC;;;;;;;;;;;YAIvG;;;;;;;AAGP;AAGO,MAAM,sCAAwB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EACtC,CAAC,EACC,SAAS,EACT,qBAAqB,IAAI,EAI1B;IACC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,CAAC;;;;;;;;;;;;;;;;;mEAiBsD,CAAC,EACxD,sBAAsB,CAAC,0EAA0E,CAAC,EAClG;;;;;;;;;;;AAKV;AAGF,sBAAsB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/glowing-effect.tsx"], "sourcesContent": ["\"use client\";\n\nimport { memo, useCallback, useEffect, useRef } from \"react\";\nimport { cn } from \"@/lib/utils\";\nimport { animate } from \"motion/react\";\n\ninterface GlowingEffectProps {\n  blur?: number;\n  inactiveZone?: number;\n  proximity?: number;\n  spread?: number;\n  variant?: \"default\" | \"white\" | \"sage\";\n  glow?: boolean;\n  className?: string;\n  disabled?: boolean;\n  movementDuration?: number;\n  borderWidth?: number;\n}\n\nconst GlowingEffect = memo(\n  ({\n    blur = 0,\n    inactiveZone = 0.7,\n    proximity = 0,\n    spread = 20,\n    variant = \"sage\",\n    glow = false,\n    className,\n    movementDuration = 2,\n    borderWidth = 1,\n    disabled = true,\n  }: GlowingEffectProps) => {\n    const containerRef = useRef<HTMLDivElement>(null);\n    const lastPosition = useRef({ x: 0, y: 0 });\n    const animationFrameRef = useRef<number>(0);\n\n    const handleMove = useCallback(\n      (e?: MouseEvent | { x: number; y: number }) => {\n        if (!containerRef.current) return;\n\n        if (animationFrameRef.current) {\n          cancelAnimationFrame(animationFrameRef.current);\n        }\n\n        animationFrameRef.current = requestAnimationFrame(() => {\n          const element = containerRef.current;\n          if (!element) return;\n\n          const { left, top, width, height } = element.getBoundingClientRect();\n          const mouseX = e?.x ?? lastPosition.current.x;\n          const mouseY = e?.y ?? lastPosition.current.y;\n\n          if (e) {\n            lastPosition.current = { x: mouseX, y: mouseY };\n          }\n\n          const center = [left + width * 0.5, top + height * 0.5];\n          const distanceFromCenter = Math.hypot(\n            mouseX - center[0],\n            mouseY - center[1]\n          );\n          const inactiveRadius = 0.5 * Math.min(width, height) * inactiveZone;\n\n          if (distanceFromCenter < inactiveRadius) {\n            element.style.setProperty(\"--active\", \"0\");\n            return;\n          }\n\n          const isActive =\n            mouseX > left - proximity &&\n            mouseX < left + width + proximity &&\n            mouseY > top - proximity &&\n            mouseY < top + height + proximity;\n\n          element.style.setProperty(\"--active\", isActive ? \"1\" : \"0\");\n\n          if (!isActive) return;\n\n          const currentAngle =\n            parseFloat(element.style.getPropertyValue(\"--start\")) || 0;\n          let targetAngle =\n            (180 * Math.atan2(mouseY - center[1], mouseX - center[0])) /\n              Math.PI +\n            90;\n\n          const angleDiff = ((targetAngle - currentAngle + 180) % 360) - 180;\n          const newAngle = currentAngle + angleDiff;\n\n          animate(currentAngle, newAngle, {\n            duration: movementDuration,\n            ease: [0.16, 1, 0.3, 1],\n            onUpdate: (value) => {\n              element.style.setProperty(\"--start\", String(value));\n            },\n          });\n        });\n      },\n      [inactiveZone, proximity, movementDuration]\n    );\n\n    useEffect(() => {\n      if (disabled) return;\n\n      const handleScroll = () => handleMove();\n      const handlePointerMove = (e: PointerEvent) => handleMove(e);\n\n      window.addEventListener(\"scroll\", handleScroll, { passive: true });\n      document.body.addEventListener(\"pointermove\", handlePointerMove, {\n        passive: true,\n      });\n\n      return () => {\n        if (animationFrameRef.current) {\n          cancelAnimationFrame(animationFrameRef.current);\n        }\n        window.removeEventListener(\"scroll\", handleScroll);\n        document.body.removeEventListener(\"pointermove\", handlePointerMove);\n      };\n    }, [handleMove, disabled]);\n\n    // Define gradient variants - THEME-AWARE\n    const getGradient = () => {\n      switch (variant) {\n        case \"white\":\n          return `repeating-conic-gradient(\n            from 236.84deg at 50% 50%,\n            var(--black),\n            var(--black) calc(25% / var(--repeating-conic-gradient-times))\n          )`;\n        case \"sage\":\n          // Theme-aware sage variant: Light Mode = Forest/Sage, Dark Mode = Gold\n          return `radial-gradient(circle, var(--theme-primary) 10%, transparent 20%),\n            radial-gradient(circle at 40% 40%, #F4F1E8 5%, transparent 15%),\n            radial-gradient(circle at 60% 60%, var(--theme-primary) 10%, transparent 20%),\n            radial-gradient(circle at 40% 60%, var(--theme-secondary) 10%, transparent 20%),\n            repeating-conic-gradient(\n              from 236.84deg at 50% 50%,\n              var(--theme-primary) 0%,\n              #F4F1E8 calc(25% / var(--repeating-conic-gradient-times)),\n              var(--theme-primary) calc(50% / var(--repeating-conic-gradient-times)),\n              var(--theme-secondary) calc(75% / var(--repeating-conic-gradient-times)),\n              var(--theme-primary) calc(100% / var(--repeating-conic-gradient-times))\n            )`;\n        default:\n          return `radial-gradient(circle, #dd7bbb 10%, #dd7bbb00 20%),\n            radial-gradient(circle at 40% 40%, #d79f1e 5%, #d79f1e00 15%),\n            radial-gradient(circle at 60% 60%, #5a922c 10%, #5a922c00 20%),\n            radial-gradient(circle at 40% 60%, #4c7894 10%, #4c789400 20%),\n            repeating-conic-gradient(\n              from 236.84deg at 50% 50%,\n              #dd7bbb 0%,\n              #d79f1e calc(25% / var(--repeating-conic-gradient-times)),\n              #5a922c calc(50% / var(--repeating-conic-gradient-times)),\n              #4c7894 calc(75% / var(--repeating-conic-gradient-times)),\n              #dd7bbb calc(100% / var(--repeating-conic-gradient-times))\n            )`;\n      }\n    };\n\n    return (\n      <>\n        <div\n          className={cn(\n            \"pointer-events-none absolute -inset-px hidden rounded-[inherit] border opacity-0 transition-opacity\",\n            glow && \"opacity-100\",\n            variant === \"white\" && \"border-white\",\n            disabled && \"!block\"\n          )}\n        />\n        <div\n          ref={containerRef}\n          style={\n            {\n              \"--blur\": `${blur}px`,\n              \"--spread\": spread,\n              \"--start\": \"0\",\n              \"--active\": \"0\",\n              \"--glowingeffect-border-width\": `${borderWidth}px`,\n              \"--repeating-conic-gradient-times\": \"5\",\n              \"--gradient\": getGradient(),\n            } as React.CSSProperties\n          }\n          className={cn(\n            \"pointer-events-none absolute inset-0 rounded-[inherit] opacity-100 transition-opacity\",\n            glow && \"opacity-100\",\n            blur > 0 && \"blur-[var(--blur)] \",\n            className,\n            disabled && \"!hidden\"\n          )}\n        >\n          <div\n            className={cn(\n              \"glow\",\n              \"rounded-[inherit]\",\n              'after:content-[\"\"] after:rounded-[inherit] after:absolute after:inset-[calc(-1*var(--glowingeffect-border-width))]',\n              \"after:[border:var(--glowingeffect-border-width)_solid_transparent]\",\n              \"after:[background:var(--gradient)] after:[background-attachment:fixed]\",\n              \"after:opacity-[var(--active)] after:transition-opacity after:duration-300\",\n              \"after:[mask-clip:padding-box,border-box]\",\n              \"after:[mask-composite:intersect]\",\n              \"after:[mask-image:linear-gradient(#0000,#0000),conic-gradient(from_calc((var(--start)-var(--spread))*1deg),#00000000_0deg,#fff,#00000000_calc(var(--spread)*2deg))]\"\n            )}\n          />\n        </div>\n      </>\n    );\n  }\n);\n\nGlowingEffect.displayName = \"GlowingEffect\";\n\nexport { GlowingEffect };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAmBA,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,OAAI,AAAD,EACvB,CAAC,EACC,OAAO,CAAC,EACR,eAAe,GAAG,EAClB,YAAY,CAAC,EACb,SAAS,EAAE,EACX,UAAU,MAAM,EAChB,OAAO,KAAK,EACZ,SAAS,EACT,mBAAmB,CAAC,EACpB,cAAc,CAAC,EACf,WAAW,IAAI,EACI;IACnB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC5C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IACzC,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAU;IAEzC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAC3B,CAAC;QACC,IAAI,CAAC,aAAa,OAAO,EAAE;QAE3B,IAAI,kBAAkB,OAAO,EAAE;YAC7B,qBAAqB,kBAAkB,OAAO;QAChD;QAEA,kBAAkB,OAAO,GAAG,sBAAsB;YAChD,MAAM,UAAU,aAAa,OAAO;YACpC,IAAI,CAAC,SAAS;YAEd,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,QAAQ,qBAAqB;YAClE,MAAM,SAAS,GAAG,KAAK,aAAa,OAAO,CAAC,CAAC;YAC7C,MAAM,SAAS,GAAG,KAAK,aAAa,OAAO,CAAC,CAAC;YAE7C,IAAI,GAAG;gBACL,aAAa,OAAO,GAAG;oBAAE,GAAG;oBAAQ,GAAG;gBAAO;YAChD;YAEA,MAAM,SAAS;gBAAC,OAAO,QAAQ;gBAAK,MAAM,SAAS;aAAI;YACvD,MAAM,qBAAqB,KAAK,KAAK,CACnC,SAAS,MAAM,CAAC,EAAE,EAClB,SAAS,MAAM,CAAC,EAAE;YAEpB,MAAM,iBAAiB,MAAM,KAAK,GAAG,CAAC,OAAO,UAAU;YAEvD,IAAI,qBAAqB,gBAAgB;gBACvC,QAAQ,KAAK,CAAC,WAAW,CAAC,YAAY;gBACtC;YACF;YAEA,MAAM,WACJ,SAAS,OAAO,aAChB,SAAS,OAAO,QAAQ,aACxB,SAAS,MAAM,aACf,SAAS,MAAM,SAAS;YAE1B,QAAQ,KAAK,CAAC,WAAW,CAAC,YAAY,WAAW,MAAM;YAEvD,IAAI,CAAC,UAAU;YAEf,MAAM,eACJ,WAAW,QAAQ,KAAK,CAAC,gBAAgB,CAAC,eAAe;YAC3D,IAAI,cACF,AAAC,MAAM,KAAK,KAAK,CAAC,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,MAAM,CAAC,EAAE,IACtD,KAAK,EAAE,GACT;YAEF,MAAM,YAAY,AAAC,CAAC,cAAc,eAAe,GAAG,IAAI,MAAO;YAC/D,MAAM,WAAW,eAAe;YAEhC,CAAA,GAAA,gLAAA,CAAA,UAAO,AAAD,EAAE,cAAc,UAAU;gBAC9B,UAAU;gBACV,MAAM;oBAAC;oBAAM;oBAAG;oBAAK;iBAAE;gBACvB,UAAU,CAAC;oBACT,QAAQ,KAAK,CAAC,WAAW,CAAC,WAAW,OAAO;gBAC9C;YACF;QACF;IACF,GACA;QAAC;QAAc;QAAW;KAAiB;IAG7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;QAEd,MAAM,eAAe,IAAM;QAC3B,MAAM,oBAAoB,CAAC,IAAoB,WAAW;QAE1D,OAAO,gBAAgB,CAAC,UAAU,cAAc;YAAE,SAAS;QAAK;QAChE,SAAS,IAAI,CAAC,gBAAgB,CAAC,eAAe,mBAAmB;YAC/D,SAAS;QACX;QAEA,OAAO;YACL,IAAI,kBAAkB,OAAO,EAAE;gBAC7B,qBAAqB,kBAAkB,OAAO;YAChD;YACA,OAAO,mBAAmB,CAAC,UAAU;YACrC,SAAS,IAAI,CAAC,mBAAmB,CAAC,eAAe;QACnD;IACF,GAAG;QAAC;QAAY;KAAS;IAEzB,yCAAyC;IACzC,MAAM,cAAc;QAClB,OAAQ;YACN,KAAK;gBACH,OAAO,CAAC;;;;WAIP,CAAC;YACJ,KAAK;gBACH,uEAAuE;gBACvE,OAAO,CAAC;;;;;;;;;;;aAWL,CAAC;YACN;gBACE,OAAO,CAAC;;;;;;;;;;;aAWL,CAAC;QACR;IACF;IAEA,qBACE;;0BACE,8OAAC;gBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,uGACA,QAAQ,eACR,YAAY,WAAW,gBACvB,YAAY;;;;;;0BAGhB,8OAAC;gBACC,KAAK;gBACL,OACE;oBACE,UAAU,GAAG,KAAK,EAAE,CAAC;oBACrB,YAAY;oBACZ,WAAW;oBACX,YAAY;oBACZ,gCAAgC,GAAG,YAAY,EAAE,CAAC;oBAClD,oCAAoC;oBACpC,cAAc;gBAChB;gBAEF,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,yFACA,QAAQ,eACR,OAAO,KAAK,uBACZ,WACA,YAAY;0BAGd,cAAA,8OAAC;oBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,QACA,qBACA,sHACA,sEACA,0EACA,6EACA,4CACA,oCACA;;;;;;;;;;;;;AAMZ;AAGF,cAAc,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/golden-glowing-card-container.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { cn } from \"@/lib/utils\";\nimport { GlowingEffect } from \"./glowing-effect\";\n\ninterface GoldenGlowingCardContainerProps {\n  children: React.ReactNode;\n  className?: string;\n  interactive?: boolean;\n}\n\nexport const GoldenGlowingCardContainer: React.FC<GoldenGlowingCardContainerProps> = React.memo(({\n  children,\n  className,\n  interactive = true,\n}) => {\n  return (\n    <div className={cn(\"relative h-full group\", className)}>\n      {/* Outer container with glowing effect */}\n      <div className=\"relative h-full rounded-[1.25rem] border-[0.75px] border-white/20 p-2 md:rounded-[1.5rem] md:p-3\">\n        <GlowingEffect\n          spread={40}\n          glow={interactive}\n          disabled={!interactive}\n          proximity={interactive ? 64 : 0}\n          inactiveZone={interactive ? 0.01 : 1}\n          borderWidth={3}\n          variant=\"sage\"\n        />\n\n        {/* Inner container with margin - Consistent glassmorphism */}\n        <div className=\"relative flex h-full flex-col justify-between gap-6 overflow-hidden rounded-xl p-6 glassmorphism-card\">\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n});\n\nexport default GoldenGlowingCardContainer;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAYO,MAAM,2CAAwE,qMAAA,CAAA,UAAK,CAAC,IAAI,CAAC,CAAC,EAC/F,QAAQ,EACR,SAAS,EACT,cAAc,IAAI,EACnB;IACC,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;kBAE1C,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,sIAAA,CAAA,gBAAa;oBACZ,QAAQ;oBACR,MAAM;oBACN,UAAU,CAAC;oBACX,WAAW,cAAc,KAAK;oBAC9B,cAAc,cAAc,OAAO;oBACnC,aAAa;oBACb,SAAQ;;;;;;8BAIV,8OAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 327, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/shiny-button.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { motion } from \"framer-motion\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface ShinyButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  children: React.ReactNode;\n  className?: string;\n  size?: \"sm\" | \"md\" | \"lg\";\n  variant?: \"primary\" | \"secondary\" | \"accent\";\n  shimmerColor?: string;\n  backgroundColor?: string;\n  textColor?: string;\n}\n\nconst animationProps = {\n  initial: { \"--x\": \"100%\", scale: 0.8 },\n  animate: { \"--x\": \"-100%\", scale: 1 },\n  whileTap: { scale: 0.95 },\n  transition: {\n    repeat: Infinity,\n    repeatType: \"loop\" as const,\n    repeatDelay: 1,\n    type: \"spring\" as const,\n    stiffness: 20,\n    damping: 15,\n    mass: 2,\n    scale: {\n      type: \"spring\" as const,\n      stiffness: 200,\n      damping: 5,\n      mass: 0.5,\n    },\n  },\n};\n\nexport const ShinyButton: React.FC<ShinyButtonProps> = ({\n  children,\n  className,\n  size = \"md\",\n  variant = \"primary\",\n  shimmerColor,\n  backgroundColor,\n  textColor,\n  ...props\n}) => {\n  // Size-based styling\n  const sizeClasses = {\n    sm: \"px-3 py-2 text-sm rounded-md\",\n    md: \"px-6 py-2 text-base rounded-lg\",\n    lg: \"px-8 py-3 text-lg rounded-lg\"\n  };\n\n  // Color variants with customization support - THEME-AWARE COLORS\n  const getColors = () => {\n    const baseColors = {\n      primary: {\n        // Light Mode: Vierla Forest, Dark Mode: Vierla Gold\n        bg: backgroundColor || \"var(--theme-primary, #364035)\",\n        shimmer: shimmerColor || \"#F4F1E8\",  /* Warm Cream - Shimmer effect */\n        text: textColor || \"#F4F1E8\"         /* Warm Cream - Text on primary */\n      },\n      secondary: {\n        // Light Mode: Vierla Sage, Dark Mode: Muted Gold/Champagne\n        bg: backgroundColor || \"var(--theme-secondary, #8B9A8C)\",\n        shimmer: shimmerColor || \"#F4F1E8\",  /* Warm Cream - Shimmer effect */\n        text: textColor || \"#F4F1E8\"         /* Warm Cream - Text on secondary */\n      },\n      accent: {\n        // Light Mode: Vierla Forest, Dark Mode: Vierla Gold\n        bg: backgroundColor || \"var(--theme-primary, #364035)\",\n        shimmer: shimmerColor || \"var(--theme-secondary, #8B9A8C)\",\n        text: textColor || \"#F4F1E8\"         /* Warm Cream - Text on accent */\n      }\n    };\n    return baseColors[variant];\n  };\n\n  const colors = getColors();\n\n  const {\n    onClick,\n    onMouseEnter,\n    onMouseLeave,\n    onFocus,\n    onBlur,\n    disabled,\n    type,\n    id,\n    name,\n    value\n  } = props;\n\n  return (\n    <motion.button\n      {...animationProps}\n      onClick={onClick}\n      onMouseEnter={onMouseEnter}\n      onMouseLeave={onMouseLeave}\n      onFocus={onFocus}\n      onBlur={onBlur}\n      disabled={disabled}\n      type={type}\n      id={id}\n      name={name}\n      value={value}\n      className={cn(\n        \"relative font-medium backdrop-blur-xl transition-shadow duration-300 ease-in-out hover:shadow\",\n        \"dark:hover:shadow-[0_0_20px_hsl(var(--primary)/10%)]\",\n        \"flex items-center justify-center\",\n        sizeClasses[size],\n        className\n      )}\n      style={{\n        backgroundColor: colors.bg,\n        \"--primary\": colors.shimmer,\n      } as React.CSSProperties}\n    >\n      <span\n        className=\"relative flex items-center justify-center size-full uppercase tracking-wide dark:font-light\"\n        style={{\n          ...(textColor && { color: colors.text }),\n          maskImage:\n            `linear-gradient(-75deg,${colors.shimmer} calc(var(--x) + 20%),transparent calc(var(--x) + 30%),${colors.shimmer} calc(var(--x) + 100%))`,\n        }}\n      >\n        {children}\n      </span>\n      <span\n        style={{\n          mask: \"linear-gradient(var(--mask-black), var(--mask-black)) content-box,linear-gradient(var(--mask-black), var(--mask-black))\",\n          maskComposite: \"exclude\",\n          background: `linear-gradient(-75deg,${colors.shimmer}10 calc(var(--x)+20%),${colors.shimmer}80 calc(var(--x)+25%),${colors.shimmer}10 calc(var(--x)+100%))`,\n        }}\n        className=\"absolute inset-0 z-10 block rounded-[inherit] p-px\"\n      ></span>\n    </motion.button>\n  );\n};\n\nexport default ShinyButton;\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAiBA,MAAM,iBAAiB;IACrB,SAAS;QAAE,OAAO;QAAQ,OAAO;IAAI;IACrC,SAAS;QAAE,OAAO;QAAS,OAAO;IAAE;IACpC,UAAU;QAAE,OAAO;IAAK;IACxB,YAAY;QACV,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,MAAM;QACN,WAAW;QACX,SAAS;QACT,MAAM;QACN,OAAO;YACL,MAAM;YACN,WAAW;YACX,SAAS;YACT,MAAM;QACR;IACF;AACF;AAEO,MAAM,cAA0C,CAAC,EACtD,QAAQ,EACR,SAAS,EACT,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,YAAY,EACZ,eAAe,EACf,SAAS,EACT,GAAG,OACJ;IACC,qBAAqB;IACrB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,iEAAiE;IACjE,MAAM,YAAY;QAChB,MAAM,aAAa;YACjB,SAAS;gBACP,oDAAoD;gBACpD,IAAI,mBAAmB;gBACvB,SAAS,gBAAgB;gBAAY,+BAA+B,GACpE,MAAM,aAAa,UAAkB,gCAAgC;YACvE;YACA,WAAW;gBACT,2DAA2D;gBAC3D,IAAI,mBAAmB;gBACvB,SAAS,gBAAgB;gBAAY,+BAA+B,GACpE,MAAM,aAAa,UAAkB,kCAAkC;YACzE;YACA,QAAQ;gBACN,oDAAoD;gBACpD,IAAI,mBAAmB;gBACvB,SAAS,gBAAgB;gBACzB,MAAM,aAAa,UAAkB,+BAA+B;YACtE;QACF;QACA,OAAO,UAAU,CAAC,QAAQ;IAC5B;IAEA,MAAM,SAAS;IAEf,MAAM,EACJ,OAAO,EACP,YAAY,EACZ,YAAY,EACZ,OAAO,EACP,MAAM,EACN,QAAQ,EACR,IAAI,EACJ,EAAE,EACF,IAAI,EACJ,KAAK,EACN,GAAG;IAEJ,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;QACX,GAAG,cAAc;QAClB,SAAS;QACT,cAAc;QACd,cAAc;QACd,SAAS;QACT,QAAQ;QACR,UAAU;QACV,MAAM;QACN,IAAI;QACJ,MAAM;QACN,OAAO;QACP,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,iGACA,wDACA,oCACA,WAAW,CAAC,KAAK,EACjB;QAEF,OAAO;YACL,iBAAiB,OAAO,EAAE;YAC1B,aAAa,OAAO,OAAO;QAC7B;;0BAEA,8OAAC;gBACC,WAAU;gBACV,OAAO;oBACL,GAAI,aAAa;wBAAE,OAAO,OAAO,IAAI;oBAAC,CAAC;oBACvC,WACE,CAAC,uBAAuB,EAAE,OAAO,OAAO,CAAC,uDAAuD,EAAE,OAAO,OAAO,CAAC,uBAAuB,CAAC;gBAC7I;0BAEC;;;;;;0BAEH,8OAAC;gBACC,OAAO;oBACL,MAAM;oBACN,eAAe;oBACf,YAAY,CAAC,uBAAuB,EAAE,OAAO,OAAO,CAAC,sBAAsB,EAAE,OAAO,OAAO,CAAC,sBAAsB,EAAE,OAAO,OAAO,CAAC,uBAAuB,CAAC;gBAC7J;gBACA,WAAU;;;;;;;;;;;;AAIlB;uCAEe", "debugId": null}}]}