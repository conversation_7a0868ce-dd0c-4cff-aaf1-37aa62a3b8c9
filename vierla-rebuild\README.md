# 💄 Vierla - Self-Care, Simplified

[![Next.js](https://img.shields.io/badge/Next.js-15.2.4-black)](https://nextjs.org/)
[![React](https://img.shields.io/badge/React-19.0.0-blue)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue)](https://www.typescriptlang.org/)
[![Tailwind CSS](https://img.shields.io/badge/Tailwind_CSS-3.4.1-38B2AC)](https://tailwindcss.com/)
[![License](https://img.shields.io/badge/License-MIT-green.svg)](LICENSE)

> The ultimate marketplace connecting you with top beauty professionals, and the all-in-one tool for providers to manage and grow their business.

## 🌟 **Features**

### **For Clients**
- 🔍 **Discover** verified beauty professionals in your area
- 📅 **Book** services with real-time scheduling
- 💳 **Secure Payment** with multiple payment options
- ⭐ **Reviews & Ratings** from real customers
- 📱 **Mobile & Studio** service options

### **For Beauty Professionals**
- 🏢 **Business Management** tools and analytics
- 🌐 **Website Builder** with professional templates
- 💰 **Smart Invoicing** and payment processing
- 👥 **CRM System** for client management
- 📊 **Analytics Dashboard** for business insights

### **Technical Features**
- 🎨 **Dual Theme Support** (Light: Natural Wellness, Dark: Modern Luxury)
- 📱 **Fully Responsive** design for all devices
- ⚡ **Performance Optimized** with Next.js 15
- 🔍 **SEO Optimized** with structured data and metadata
- ♿ **Accessible** design following WCAG guidelines
- 🔒 **Secure** with modern security practices

---

## 🚀 **Quick Start**

### **Prerequisites**
- Node.js 18.17 or later
- npm 9.0 or later

### **Installation**
```bash
# Clone the repository
git clone https://github.com/your-org/vierla-rebuild.git
cd vierla-rebuild

# Install dependencies
npm install

# Start development server
npm run dev
```

Open [http://localhost:3000](http://localhost:3000) to view the website.

### **Available Scripts**
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # Run TypeScript type checking
```

---

## 📁 **Project Structure**

```
vierla-rebuild/
├── app/                    # Next.js App Router pages
│   ├── layout.tsx         # Root layout with metadata
│   ├── page.tsx           # Homepage
│   ├── features/          # Features page
│   ├── pricing/           # Pricing page
│   ├── about/             # About page
│   ├── contact/           # Contact page
│   ├── providers/         # For providers page
│   ├── apply/             # Application page
│   ├── privacy/           # Privacy policy
│   ├── terms/             # Terms of service
│   ├── globals.css        # Global styles and CSS variables
│   ├── sitemap.ts         # Dynamic sitemap generation
│   └── robots.txt         # Search engine directives
├── components/            # Reusable components
│   ├── marketing/         # Marketing-specific components
│   ├── ui/               # UI components
│   ├── seo/              # SEO components
│   └── providers/        # Theme and context providers
├── public/               # Static assets
│   ├── logo-transparent.png
│   └── robots.txt
├── docs/                 # Documentation files
│   ├── DOCUMENTATION.md
│   ├── COMPONENT-REFERENCE.md
│   └── DEPLOYMENT-GUIDE.md
└── README.md
```

---

## 🎨 **Design System**

### **Color Palette**

#### **Light Mode (Natural Wellness)**
- **Background:** `#F4F1E8` (Warm off-white)
- **Primary:** `#364035` (Vierla Forest)
- **Secondary:** `#8B9A8C` (Vierla Sage)
- **Accent:** `#B8956A` (Vierla Gold)

#### **Dark Mode (Modern Luxury)**
- **Background:** `#2D2A26` (Deep charcoal)
- **Primary:** `#B8956A` (Vierla Gold)
- **Secondary:** `#A9A299` (Muted champagne)
- **Accent:** `#B8956A` (Vierla Gold)

### **Typography**
- **Headlines:** Playfair Display (Elegant serif)
- **Body Text:** Inter (Optimized for readability)
- **Section Headers:** Tai Heritage Pro (Cultural elegance)
- **Decorative:** Farsan (Playful accent)

### **Key Components**
- **AuroraBackgroundLayer** - Animated gradient backgrounds
- **GoldenGlowingCardContainer** - Glassmorphism cards
- **ShinyButton** - Interactive buttons with shimmer effects
- **BentoGrid** - Feature display grid system
- **MultiStepForm** - Professional application form

---

## 🔍 **SEO & Performance**

### **SEO Features**
- ✅ **Structured Data** (JSON-LD) for all pages
- ✅ **Dynamic Sitemap** generation
- ✅ **Optimized Metadata** for each page
- ✅ **Open Graph** and Twitter Card support
- ✅ **Breadcrumb Navigation** for better UX
- ✅ **Robots.txt** configuration

### **Performance Optimizations**
- ⚡ **Next.js 15** with App Router
- 🖼️ **Image Optimization** with Next.js Image component
- 📦 **Code Splitting** automatic with Next.js
- 🎯 **Core Web Vitals** optimized
- 💾 **Caching Strategy** for static assets

---

## 🛠️ **Development**

### **Tech Stack**
- **Framework:** Next.js 15.2.4
- **Language:** TypeScript 5.0
- **Styling:** Tailwind CSS 3.4.1
- **Icons:** Lucide React
- **Fonts:** Google Fonts (Playfair Display, Inter, etc.)
- **Theme:** next-themes for dark/light mode

### **Code Quality**
- **TypeScript** for type safety
- **ESLint** for code linting
- **Prettier** for code formatting (recommended)
- **Husky** for git hooks (optional)

### **Component Development**
```tsx
// Example component structure
import { ComponentProps } from './types'

interface ExampleComponentProps extends ComponentProps {
  title: string
  description?: string
}

export function ExampleComponent({
  title,
  description,
  className,
  ...props
}: ExampleComponentProps) {
  return (
    <div className={`component-base ${className}`} {...props}>
      <h3 className="font-playfair text-2xl">{title}</h3>
      {description && (
        <p className="font-inter text-base">{description}</p>
      )}
    </div>
  )
}
```

---

## 📚 **Documentation**

- **[Complete Documentation](DOCUMENTATION.md)** - Comprehensive guide
- **[Component Reference](COMPONENT-REFERENCE.md)** - Component usage guide
- **[Deployment Guide](DEPLOYMENT-GUIDE.md)** - Deployment and maintenance
- **[SEO Audit Report](SEO-AUDIT-REPORT.md)** - SEO analysis and recommendations

---

## 🚀 **Deployment**

### **Vercel (Recommended)**
```bash
# Install Vercel CLI
npm i -g vercel

# Deploy to production
vercel --prod
```

### **Other Platforms**
- **Netlify:** Configure build settings in dashboard
- **Custom Server:** Use Docker or traditional hosting
- **CDN:** Configure for static asset delivery

See [Deployment Guide](DEPLOYMENT-GUIDE.md) for detailed instructions.

---

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### **Development Guidelines**
- Follow TypeScript best practices
- Use the established component patterns
- Maintain theme compatibility
- Include proper documentation
- Test in both light and dark modes

---

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

## 📞 **Support**

- **Documentation:** Check the docs folder for detailed guides
- **Issues:** Report bugs via GitHub Issues
- **Contact:** <EMAIL>

---

## 🙏 **Acknowledgments**

- **Next.js Team** for the amazing framework
- **Tailwind CSS** for the utility-first CSS framework
- **Lucide** for the beautiful icon library
- **Vercel** for hosting and deployment platform

---

**Built with ❤️ by the Vierla Team**
