import { Metadata } from 'next'
import { AuroraBackgroundLayer } from "@/components/ui/aurora-background";
import { Badge } from "@/components/ui/badge";
import { GoldenGlowingCardContainer } from "@/components/ui/golden-glowing-card-container";
import ShinyButton from "@/components/ui/shiny-button";
import { StructuredData } from "@/components/seo/structured-data";
import { BreadcrumbSchema, getBreadcrumbsForPage } from "@/components/seo/breadcrumb-schema";
import { Check } from "lucide-react";
import Link from "next/link";

export const metadata: Metadata = {
  title: 'Pricing - Affordable Plans for Every Business',
  description: 'Choose the perfect Vierla plan for your beauty business. From individual professionals to large enterprises, we have flexible pricing options that grow with you.',
  openGraph: {
    title: 'Pricing - Affordable Plans for Every Business | Vierla',
    description: 'Flexible pricing plans for beauty professionals and businesses. Start free and scale as you grow.',
    url: 'https://vierla.com/pricing',
    siteName: 'Vierla',
    locale: 'en_CA',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Pricing - Affordable Plans for Every Business | Vierla',
    description: 'Flexible pricing plans for beauty professionals and businesses. Start free and scale as you grow.',
  },
};

export default function Pricing() {
  return (
    <>
      <StructuredData page="pricing" />
      <BreadcrumbSchema items={getBreadcrumbsForPage('pricing')} />
      <PricingContent />
    </>
  )
}

function PricingContent() {
  const plans = [
    {
      name: "Individual",
      description: "Perfect for solo beauty professionals",
      price: "Free",
      features: [
        "Service Listing",
        "Website Builder",
        "Bookings/month",
        "Email Support",
        "Templates",
        "Payment Processing"
      ],
      popular: false,
      cta: "Get Started Free"
    },
    {
      name: "Medium Business",
      description: "Most popular for growing beauty businesses",
      price: "$29/month",
      features: [
        "Unlimited Service Listings",
        "Full Website Builder",
        "Unlimited Bookings",
        "Integrated CRM",
        "Advanced Analytics",
        "Priority Support",
        "Custom Domain",
        "Premium Templates",
        "Team Management (up to 5)"
      ],
      popular: true,
      cta: "Start Pro Trial"
    },
    {
      name: "Large Business",
      description: "For established beauty enterprises",
      price: "$79/month",
      features: [
        "Everything in Medium Business",
        "Unlimited Team Members",
        "White-label Options",
        "API Access",
        "Dedicated Account Manager",
        "Custom Integrations",
        "Advanced Automation",
        "Multi-location Support"
      ],
      popular: false,
      cta: "Contact Sales"
    }
  ];

  return (
    <div className="page-pricing relative overflow-hidden">
      <AuroraBackgroundLayer />

      {/* Hero Section */}
      <section className="relative z-10 w-full px-4 py-20 pt-36">
        <div className="text-center max-w-6xl mx-auto">
          <h1 className="text-4xl md:text-6xl font-black mb-6 leading-none text-[#2D2A26] dark:text-[#F4F1E8] drop-shadow-lg font-sans">
            SIMPLE, TRANSPARENT PRICING
          </h1>
          <p className="text-xl md:text-2xl text-[#2D2A26] dark:text-[#A9A299] mb-8 leading-relaxed max-w-4xl mx-auto drop-shadow-sm font-inter">
            Choose the plan that fits your business needs. Start free and scale as you grow.
          </p>
        </div>
      </section>

      {/* Pricing Cards */}
      <section className="relative z-10 py-20 border-t border-white/20">
        <div className="container mx-auto px-4">
          <div className="grid gap-6 lg:grid-cols-3 max-w-6xl mx-auto">
            {plans.map((plan, index) => (
              <div key={index} className="relative h-full">
                <GoldenGlowingCardContainer>
                  <div className="relative h-full flex flex-col">
                    {plan.popular && (
                      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2 z-10">
                        <Badge className="bg-[var(--master-brand-accent)] text-[var(--master-text-primary-light)] font-inter">Most Popular</Badge>
                      </div>
                    )}
                    <div className="text-center pb-8">
                      <h3 className="text-2xl font-bold text-[#2D2A26] dark:text-[#F4F1E8] drop-shadow-lg mb-2 font-tai-heritage">{plan.name}</h3>
                      <p className="text-[var(--master-text-secondary-light)] dark:text-[var(--master-text-secondary-dark)] drop-shadow-sm mb-4 font-sans">{plan.description}</p>
                      <div className="mt-4">
                        <span className="text-4xl font-black text-[var(--master-brand-primary-light)] dark:text-[var(--master-brand-primary-dark)] drop-shadow-lg font-tai-heritage">{plan.price}</span>
                      </div>
                    </div>
                    <div className="flex-grow flex flex-col">
                      <ul className="space-y-3 mb-8 flex-grow">
                        {plan.features.map((feature, idx) => (
                          <li key={idx} className="flex items-center text-[var(--master-text-secondary-light)] dark:text-[var(--master-text-secondary-dark)] drop-shadow-sm font-inter">
                            <Check className="h-4 w-4 text-[var(--master-brand-primary-light)] dark:text-[var(--master-brand-primary-dark)] mr-3 flex-shrink-0 drop-shadow-sm" />
                            <span className="text-sm text-[#2D2A26] dark:text-[#F4F1E8]">{feature}</span>
                          </li>
                        ))}
                      </ul>
                      {/* Button area */}
                      <div className="mt-auto flex justify-center">
                        <Link href={plan.name === "Large Business" ? "/contact" : "/apply"} className="w-full">
                          <ShinyButton
                              size="sm"
                              variant="primary"
                              backgroundColor="var(--master-brand-primary-light)"
                              shimmerColor="var(--master-text-primary-light)"
                              className="w-full max-w-[180px] mx-auto drop-shadow-sm text-sm whitespace-nowrap font-medium dark:bg-[var(--master-brand-primary-dark)] text-white dark:text-[var(--master-text-primary-dark)]"
                            >
                              {plan.cta}
                            </ShinyButton>
                          </Link>
                        </div>
                      </div>
                    </div>
                  </GoldenGlowingCardContainer>
                </div>
            ))}
          </div>
        </div>
      </section>

      {/* Comparison Table Section */}
      <section className="relative z-10 py-20 border-t border-white/20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-5xl font-black text-[#2D2A26] dark:text-[#F4F1E8] mb-6 drop-shadow-lg font-jost">
              WHY CHOOSE VIERLA OVER OTHERS?
            </h2>
            <p className="text-xl text-warm-beige max-w-4xl mx-auto drop-shadow-sm font-sans">
              See how Vierla stands out from traditional booking platforms and competitors
            </p>
          </div>

          <div className="max-w-6xl mx-auto">
            <GoldenGlowingCardContainer>
              <div className="overflow-x-auto">
                <table className="w-full text-left">
                  <thead>
                    <tr className="border-b border-sage/20">
                      <th className="py-4 px-6 text-light-off-white font-bold text-lg font-tai-heritage">Features</th>
                      <th className="py-4 px-6 text-center">
                        <div className="flex flex-col items-center">
                          <span className="text-muted-gold font-bold text-lg mb-1 font-tai-heritage">Vierla</span>
                          <span className="text-xs bg-muted-gold/20 px-2 py-1 rounded-full text-muted-gold font-sans">All-in-One</span>
                        </div>
                      </th>
                      <th className="py-4 px-6 text-center text-warm-beige/70 font-medium font-sans">Traditional Salons</th>
                      <th className="py-4 px-6 text-center text-warm-beige/70 font-medium font-sans">Other Platforms</th>
                    </tr>
                  </thead>
                  <tbody className="text-warm-beige/80 font-sans">
                    <tr className="border-b border-sage/10">
                      <td className="py-4 px-6 font-medium">Mobile Service Available</td>
                      <td className="py-4 px-6 text-center">
                        <svg className="w-6 h-6 text-sage mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </td>
                      <td className="py-4 px-6 text-center">
                        <svg className="w-6 h-6 text-error mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </td>
                      <td className="py-4 px-6 text-center text-[var(--master-text-secondary-light)] dark:text-[var(--master-text-secondary-dark)]">Limited</td>
                    </tr>
                    <tr className="border-b border-sage/10">
                      <td className="py-4 px-6 font-medium">Verified Professionals</td>
                      <td className="py-4 px-6 text-center">
                        <svg className="w-6 h-6 text-sage mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </td>
                      <td className="py-4 px-6 text-center">
                        <svg className="w-6 h-6 text-sage mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </td>
                      <td className="py-4 px-6 text-center text-[var(--master-text-secondary-light)] dark:text-[var(--master-text-secondary-dark)]">Basic</td>
                    </tr>
                    <tr className="border-b border-sage/10">
                      <td className="py-4 px-6 font-medium">Same-Day Booking</td>
                      <td className="py-4 px-6 text-center">
                        <svg className="w-6 h-6 text-sage mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </td>
                      <td className="py-4 px-6 text-center">
                        <svg className="w-6 h-6 text-error mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </td>
                      <td className="py-4 px-6 text-center text-[var(--master-text-secondary-light)] dark:text-[var(--master-text-secondary-dark)]">Rare</td>
                    </tr>
                    <tr className="border-b border-sage/10">
                      <td className="py-4 px-6 font-medium">Transparent Pricing</td>
                      <td className="py-4 px-6 text-center">
                        <svg className="w-6 h-6 text-sage mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </td>
                      <td className="py-4 px-6 text-center text-[var(--master-text-secondary-light)] dark:text-[var(--master-text-secondary-dark)]">Variable</td>
                      <td className="py-4 px-6 text-center text-[var(--master-text-secondary-light)] dark:text-[var(--master-text-secondary-dark)]">Hidden Fees</td>
                    </tr>
                    <tr className="border-b border-sage/10">
                      <td className="py-4 px-6 font-medium">Money-Back Guarantee</td>
                      <td className="py-4 px-6 text-center">
                        <svg className="w-6 h-6 text-sage mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </td>
                      <td className="py-4 px-6 text-center">
                        <svg className="w-6 h-6 text-error mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </td>
                      <td className="py-4 px-6 text-center">
                        <svg className="w-6 h-6 text-error mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </td>
                    </tr>
                    <tr>
                      <td className="py-4 px-6 font-medium">24/7 Customer Support</td>
                      <td className="py-4 px-6 text-center">
                        <svg className="w-6 h-6 text-sage mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </td>
                      <td className="py-4 px-6 text-center text-[var(--master-text-secondary-light)] dark:text-[var(--master-text-secondary-dark)]">Business Hours</td>
                      <td className="py-4 px-6 text-center text-[var(--master-text-secondary-light)] dark:text-[var(--master-text-secondary-dark)]">Limited</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </GoldenGlowingCardContainer>

          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative z-10 py-12 border-t border-white/20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <GoldenGlowingCardContainer interactive={false}>
              <div className="text-center pt-8 pb-4 px-6">
                <h2 className="font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl text-[#2D2A26] dark:text-[#F4F1E8] drop-shadow-lg font-tai-heritage mb-6">
                  Ready to get started?
                </h2>
                <p className="max-w-[42rem] leading-normal text-[#2D2A26] dark:text-[#A9A299] sm:text-xl sm:leading-8 drop-shadow-sm font-sans mb-8 mx-auto">
                  Join thousands of entrepreneurs who trust Vierla to power their business.
                </p>
                <div className="flex justify-center">
                  <Link href="/apply">
                    <ShinyButton
                      backgroundColor="var(--master-brand-primary-light)"
                      shimmerColor="var(--master-text-primary-light)"
                      className="dark:bg-[var(--master-brand-primary-dark)] text-white dark:text-[var(--master-text-primary-dark)]"
                    >
                      Start Free Trial
                    </ShinyButton>
                  </Link>
                </div>
              </div>
            </GoldenGlowingCardContainer>
          </div>
        </div>
      </section>
    </div>
  );
}
