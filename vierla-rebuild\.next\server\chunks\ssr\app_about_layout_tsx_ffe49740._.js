module.exports = {

"[project]/app/about/layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>AboutLayout,
    "metadata": ()=>metadata
});
const metadata = {
    title: 'About Us - Our Mission & Story',
    description: 'Learn about <PERSON><PERSON><PERSON>\'s mission to simplify self-care by connecting clients with top beauty professionals. Discover our story, values, and commitment to excellence.',
    openGraph: {
        title: 'About Us - Our Mission & Story | Vierla',
        description: 'Discover Vierla\'s mission to revolutionize the beauty industry by connecting clients with verified professionals.',
        url: 'https://vierla.com/about',
        siteName: 'Vierla',
        locale: 'en_CA',
        type: 'website'
    },
    twitter: {
        card: 'summary_large_image',
        title: 'About Us - Our Mission & Story | Vierla',
        description: 'Discover Vierla\'s mission to revolutionize the beauty industry by connecting clients with verified professionals.'
    }
};
function AboutLayout({ children }) {
    return children;
}
}),

};

//# sourceMappingURL=app_about_layout_tsx_ffe49740._.js.map