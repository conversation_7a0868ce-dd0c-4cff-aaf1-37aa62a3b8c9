import type { <PERSON><PERSON><PERSON> } from "next";
import { Inter, Playfair_Display, Dancing_Script } from "next/font/google";
import "./globals.css";
import { Navbar } from "@/components/marketing/navbar";
import { Footer } from "@/components/marketing/footer";
import { ToastProvider } from "@/components/ui/toast";
import { CookieConsent } from "@/components/ui/cookie-consent";
import { ThemeProvider } from "@/components/providers/theme-provider";
import { StructuredData } from "@/components/seo/structured-data";

// Playfair Display for headlines (H1, H2) - Elegant serif for impact and luxury
const playfairDisplay = Playfair_Display({
  variable: "--font-playfair",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700", "800", "900"],
  display: "swap",
});

// Inter for body text and UI elements - Optimized for screen readability
const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  display: "swap",
});

// Playfair Display for section titles (H3) - Consistent with headline font
const taiHeritage = Playfair_Display({
  variable: "--font-tai-heritage",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  display: "swap",
});

// Dancing Script for optional accent/signature text (script-like)
const farsan = Dancing_Script({
  variable: "--font-farsan",
  subsets: ["latin"],
  weight: ["400", "500", "600", "700"],
  display: "swap",
});

export const metadata: Metadata = {
  metadataBase: new URL('https://vierla.com'),
  title: {
    default: 'Vierla - Self-Care, Simplified',
    template: '%s | Vierla'
  },
  description: "The ultimate marketplace connecting you with top beauty professionals, and the all-in-one tool for providers to manage and grow their business.",
  keywords: ['beauty services', 'self-care', 'beauty professionals', 'hair styling', 'makeup', 'nail services', 'skincare', 'beauty marketplace', 'mobile beauty', 'beauty booking'],
  authors: [{ name: 'Vierla Team' }],
  creator: 'Vierla',
  publisher: 'Vierla',
  openGraph: {
    type: 'website',
    locale: 'en_CA',
    url: 'https://vierla.com',
    siteName: 'Vierla',
    title: 'Vierla - Self-Care, Simplified',
    description: 'Connect with top beauty professionals for hair, makeup, nails, and skincare services. Mobile and studio options available.',
    images: [
      {
        url: '/logo-transparent.png',
        width: 1200,
        height: 630,
        alt: 'Vierla - Self-Care, Simplified',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Vierla - Self-Care, Simplified',
    description: 'Connect with top beauty professionals for premium beauty services.',
    images: ['/logo-transparent.png'],
    creator: '@vierla',
    site: '@vierla',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  icons: {
    icon: '/logo-transparent.png',
    shortcut: '/logo-transparent.png',
    apple: '/logo-transparent.png',
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${playfairDisplay.variable} ${inter.variable} ${taiHeritage.variable} ${farsan.variable} antialiased
                   text-light-off-white transition-colors duration-300`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange={false}
        >
          <ToastProvider>
            <StructuredData page="home" />
            <Navbar />
            <main className="pt-4">
              {children}
            </main>
            <Footer />
            <CookieConsent />
          </ToastProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}