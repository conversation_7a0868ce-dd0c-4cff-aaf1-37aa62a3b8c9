import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Contact Us - Get in Touch',
  description: 'Contact Vierla for support, partnerships, or general inquiries. We\'re here to help you with your beauty service needs and business questions.',
  openGraph: {
    title: 'Contact Us - Get in Touch | Vierla',
    description: 'Get in touch with <PERSON><PERSON><PERSON> for support, partnerships, or general inquiries. We\'re here to help.',
    url: 'https://vierla.com/contact',
    siteName: 'Vierla',
    locale: 'en_CA',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Contact Us - Get in Touch | Vierla',
    description: 'Get in touch with <PERSON><PERSON><PERSON> for support, partnerships, or general inquiries. We\'re here to help.',
  },
}

export default function ContactLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
