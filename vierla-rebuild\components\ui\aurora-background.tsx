"use client";

import { cn } from "@/lib/utils";
import React, { memo, type ReactNode } from "react";

interface AuroraBackgroundProps extends React.HTMLProps<HTMLDivElement> {
  children: ReactNode;
  showRadialGradient?: boolean;
}

// Full page wrapper version
export const AuroraBackground = ({
  className,
  children,
  showRadialGradient = true,
  ...props
}: AuroraBackgroundProps) => {
  return (
    <div
      className={cn(
        "relative flex flex-col h-[100vh] items-center justify-center text-slate-950 transition-colors duration-300",
        className
      )}
      {...props}
    >
      <div className="absolute inset-0 overflow-hidden">
        <div
          className={cn(
            `
            [--light-bg:#F4F1E8]
            [--dark-bg:#2D2A26]
            [--light-aurora:repeating-linear-gradient(100deg,#364035_10%,#8B9A8C_15%,#F0E6D9_20%,#7C9A85_25%,#364035_30%)]
            [--dark-aurora:repeating-linear-gradient(100deg,#B8956A_10%,#4B3D6B_15%,#B8956A_20%,#4B3D6B_25%,#B8956A_30%)]
            [--light-stripes:repeating-linear-gradient(100deg,var(--light-bg)_0%,var(--light-bg)_7%,transparent_10%,transparent_12%,var(--light-bg)_16%)]
            [--dark-stripes:repeating-linear-gradient(100deg,var(--dark-bg)_0%,var(--dark-bg)_7%,transparent_10%,transparent_12%,var(--dark-bg)_16%)]
            [background-image:var(--light-stripes),var(--light-aurora)]
            dark:[background-image:var(--dark-stripes),var(--dark-aurora)]
            [background-size:300%,_200%]
            [background-position:50%_50%,50%_50%]
            filter blur-[10px]
            after:content-[""] after:absolute after:inset-0 after:[background-image:var(--light-stripes),var(--light-aurora)]
            after:dark:[background-image:var(--dark-stripes),var(--dark-aurora)]
            after:[background-size:200%,_100%]
            after:animate-aurora after:[background-attachment:fixed] after:mix-blend-difference
            pointer-events-none
            absolute -inset-[10px] opacity-50 will-change-transform`,
            showRadialGradient && `[mask-image:radial-gradient(ellipse_at_100%_0%,black_10%,transparent_70%)]`
          )}
        ></div>
      </div>
      {children}
    </div>
  );
};

// Background-only version for existing layouts
export const AuroraBackgroundLayer = memo(
  ({
    className,
    showRadialGradient = true,
  }: {
    className?: string;
    showRadialGradient?: boolean;
  }) => {
    return (
      <div className="fixed inset-0 -z-10 overflow-hidden">
        <div
          className={cn(
            `
            [--light-bg:#F4F1E8]
            [--dark-bg:#2D2A26]
            [--light-aurora:repeating-linear-gradient(100deg,#7C9A85_10%,#8B9A8C_15%,#E8D5D5_20%,#7C9A85_25%,#8B9A8C_30%)]
            [--dark-aurora:repeating-linear-gradient(100deg,#B8956A_10%,#4B3D6B_15%,#B8956A_20%,#4B3D6B_25%,#B8956A_30%)]
            [--light-stripes:repeating-linear-gradient(100deg,var(--light-bg)_0%,var(--light-bg)_7%,transparent_10%,transparent_12%,var(--light-bg)_16%)]
            [--dark-stripes:repeating-linear-gradient(100deg,var(--dark-bg)_0%,var(--dark-bg)_7%,transparent_10%,transparent_12%,var(--dark-bg)_16%)]
            [background-image:var(--light-stripes),var(--light-aurora)]
            dark:[background-image:var(--dark-stripes),var(--dark-aurora)]
            [background-size:300%,_200%]
            [background-position:50%_50%,50%_50%]
            filter blur-[10px]
            after:content-[""] after:absolute after:inset-0 after:[background-image:var(--light-stripes),var(--light-aurora)]
            after:dark:[background-image:var(--dark-stripes),var(--dark-aurora)]
            after:[background-size:200%,_100%]
            after:animate-aurora after:[background-attachment:fixed] after:mix-blend-difference
            pointer-events-none
            absolute -inset-[10px] opacity-50 will-change-transform`,
            showRadialGradient && `[mask-image:radial-gradient(ellipse_at_100%_0%,black_10%,transparent_70%)]`,
            className
          )}
        ></div>
      </div>
    );
  }
);

AuroraBackgroundLayer.displayName = "AuroraBackgroundLayer";