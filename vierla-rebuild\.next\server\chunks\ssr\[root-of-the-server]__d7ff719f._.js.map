{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AuroraBackground = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackground() from the server but AuroraBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx <module evaluation>\",\n    \"AuroraBackground\",\n);\nexport const AuroraBackgroundLayer = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackgroundLayer() from the server but AuroraBackgroundLayer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx <module evaluation>\",\n    \"AuroraBackgroundLayer\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,qEACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,qEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AuroraBackground = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackground() from the server but AuroraBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx\",\n    \"AuroraBackground\",\n);\nexport const AuroraBackgroundLayer = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackgroundLayer() from the server but AuroraBackgroundLayer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx\",\n    \"AuroraBackgroundLayer\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,iDACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,iDACA", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/golden-glowing-card-container.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const GoldenGlowingCardContainer = registerClientReference(\n    function() { throw new Error(\"Attempted to call GoldenGlowingCardContainer() from the server but GoldenGlowingCardContainer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glowing-card-container.tsx <module evaluation>\",\n    \"GoldenGlowingCardContainer\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ui/golden-glowing-card-container.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glowing-card-container.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,6BAA6B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5D;IAAa,MAAM,IAAI,MAAM;AAAoQ,GACjS,iFACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmT,GAChV,iFACA", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/golden-glowing-card-container.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const GoldenGlowingCardContainer = registerClientReference(\n    function() { throw new Error(\"Attempted to call GoldenGlowingCardContainer() from the server but GoldenGlowingCardContainer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glowing-card-container.tsx\",\n    \"GoldenGlowingCardContainer\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ui/golden-glowing-card-container.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glowing-card-container.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,6BAA6B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5D;IAAa,MAAM,IAAI,MAAM;AAAoQ,GACjS,6DACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 108, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/bento-grid.tsx"], "sourcesContent": ["import { ReactNode } from \"react\";\nimport { ArrowRightIcon } from \"@radix-ui/react-icons\";\nimport Link from \"next/link\";\n\nimport { cn } from \"@/lib/utils\";\nimport { Button } from \"@/components/ui/button\";\n\nconst BentoGrid = ({\n  children,\n  className,\n}: {\n  children: ReactNode;\n  className?: string;\n}) => {\n  return (\n    <div\n      className={cn(\n        \"grid w-full auto-rows-[20rem] grid-cols-3 gap-4\",\n        // 16px gap as specified in design system\n        \"gap-4\", // 16px\n        className,\n      )}\n    >\n      {children}\n    </div>\n  );\n};\n\nconst BentoCard = ({\n  name,\n  className,\n  background,\n  Icon,\n  description,\n  services,\n  href,\n  cta,\n}: {\n  name: string;\n  className: string;\n  background?: ReactNode;\n  Icon: any;\n  description: string;\n  services?: string[];\n  href: string;\n  cta: string;\n}) => (\n  <Link href={href} className={cn(\n      \"group relative col-span-3 flex flex-col justify-between overflow-hidden rounded-xl\",\n      // Theme-aware background and borders\n      \"border shadow-xl transition-all duration-500 ease-out transform-gpu\",\n      // Smooth hover effects for desktop\n      \"hover:scale-[1.02] hover:shadow-2xl\",\n      // Mobile touch behavior - show content on first tap, navigate on second\n      \"mobile-bento-card\",\n      className,\n    )}\n    style={{\n      backgroundColor: 'var(--card-bg)',\n      borderColor: 'var(--border-subtle)',\n      boxShadow: 'var(--shadow-card)'\n    }}\n  >\n    {background && <div>{background}</div>}\n    <div className=\"pointer-events-none z-10 flex transform-gpu flex-col gap-2 p-4 transition-all duration-500\">\n      <Icon className=\"h-10 w-10 origin-left transform-gpu transition-all duration-500 ease-out group-hover:scale-110 mobile-bento-icon\"\n            style={{ color: 'var(--icon-accent)' }} />\n      <h3 className=\"text-lg font-semibold font-tai-heritage leading-tight transition-all duration-500 group-hover:opacity-0 group-hover:transform group-hover:-translate-y-2 mobile-bento-title\"\n          style={{ color: 'var(--text-primary)' }}>\n        {name}\n      </h3>\n      <p className=\"max-w-lg font-sans text-sm leading-relaxed opacity-0 transform translate-y-2 group-hover:opacity-100 group-hover:translate-y-0 transition-all duration-500 delay-100 mobile-bento-description\"\n         style={{ color: 'var(--text-secondary)' }}>{description}</p>\n    </div>\n\n    <div className=\"pointer-events-none absolute inset-0 transform-gpu transition-all duration-500 opacity-0 group-hover:opacity-100 mobile-bento-overlay\"\n         style={{ backgroundColor: 'var(--accent-hover-overlay)' }} />\n  </Link>\n);\n\nexport { BentoCard, BentoGrid };\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;;AAGA,MAAM,YAAY,CAAC,EACjB,QAAQ,EACR,SAAS,EAIV;IACC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mDACA,yCAAyC;QACzC,SACA;kBAGD;;;;;;AAGP;AAEA,MAAM,YAAY,CAAC,EACjB,IAAI,EACJ,SAAS,EACT,UAAU,EACV,IAAI,EACJ,WAAW,EACX,QAAQ,EACR,IAAI,EACJ,GAAG,EAUJ,iBACC,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAM;QAAM,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAC1B,sFACA,qCAAqC;QACrC,uEACA,mCAAmC;QACnC,uCACA,wEAAwE;QACxE,qBACA;QAEF,OAAO;YACL,iBAAiB;YACjB,aAAa;YACb,WAAW;QACb;;YAEC,4BAAc,8OAAC;0BAAK;;;;;;0BACrB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;wBACV,OAAO;4BAAE,OAAO;wBAAqB;;;;;;kCAC3C,8OAAC;wBAAG,WAAU;wBACV,OAAO;4BAAE,OAAO;wBAAsB;kCACvC;;;;;;kCAEH,8OAAC;wBAAE,WAAU;wBACV,OAAO;4BAAE,OAAO;wBAAwB;kCAAI;;;;;;;;;;;;0BAGjD,8OAAC;gBAAI,WAAU;gBACV,OAAO;oBAAE,iBAAiB;gBAA8B", "debugId": null}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/shimmer-button.tsx"], "sourcesContent": ["import React, { CSSProperties } from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface ShimmerButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  shimmerColor?: string;\n  shimmerSize?: string;\n  borderRadius?: string;\n  shimmerDuration?: string;\n  background?: string;\n  className?: string;\n  children?: React.ReactNode;\n  size?: \"sm\" | \"md\" | \"lg\";\n  variant?: \"primary\" | \"secondary\" | \"accent\";\n}\n\nconst ShimmerButton = React.forwardRef<HTMLButtonElement, ShimmerButtonProps>(\n  (\n    {\n      shimmerColor,\n      shimmerSize = \"0.05em\",\n      shimmerDuration = \"3s\",\n      borderRadius = \"100px\",\n      background,\n      className,\n      children,\n      size = \"md\",\n      variant = \"primary\",\n      ...props\n    },\n    ref,\n  ) => {\n    // Size-based styling\n    const sizeClasses = {\n      sm: \"px-4 py-2 text-sm\",\n      md: \"px-6 py-3 text-base\",\n      lg: \"px-8 py-4 text-lg\"\n    };\n\n    // Color variants with customization support - THEME-AWARE COLORS\n    const getColors = () => {\n      const baseColors = {\n        primary: {\n          // Light Mode: Vierla Forest, Dark Mode: Vierla Gold\n          bg: background || \"var(--theme-primary, #364035)\",\n          shimmer: shimmerColor || \"#F4F1E8\",  /* Warm Cream - Shimmer effect */\n        },\n        secondary: {\n          // Light Mode: Vierla Sage, Dark Mode: Muted Gold/Champagne\n          bg: background || \"var(--theme-secondary, #8B9A8C)\",\n          shimmer: shimmerColor || \"#F4F1E8\",  /* Warm Cream - Shimmer effect */\n        },\n        accent: {\n          // Light Mode: Vierla Forest, Dark Mode: Vierla Gold\n          bg: background || \"var(--theme-primary, #364035)\",\n          shimmer: shimmerColor || \"var(--theme-secondary, #8B9A8C)\",\n        }\n      };\n      return baseColors[variant];\n    };\n\n    const colors = getColors();\n\n    return (\n      <button\n        style={\n          {\n            \"--spread\": \"90deg\",\n            \"--shimmer-color\": colors.shimmer,\n            \"--radius\": borderRadius,\n            \"--speed\": shimmerDuration,\n            \"--cut\": shimmerSize,\n            \"--bg\": colors.bg,\n          } as CSSProperties\n        }\n        className={cn(\n          \"group relative z-0 flex cursor-pointer items-center justify-center overflow-hidden whitespace-nowrap border border-white/10 text-white [background:var(--bg)] [border-radius:var(--radius)] dark:text-black\",\n          \"transform-gpu transition-transform duration-300 ease-in-out active:translate-y-px\",\n          sizeClasses[size],\n          className,\n        )}\n        ref={ref}\n        {...props}\n      >\n        {/* spark container */}\n        <div\n          className={cn(\n            \"-z-30 blur-[2px]\",\n            \"absolute inset-0 overflow-visible [container-type:size]\",\n          )}\n        >\n          {/* spark */}\n          <div className=\"absolute inset-0 h-[100cqh] animate-shimmer-slide [aspect-ratio:1] [border-radius:0] [mask:none]\">\n            {/* spark before */}\n            <div className=\"animate-spin-around absolute -inset-full w-auto rotate-0 [background:conic-gradient(from_calc(270deg-(var(--spread)*0.5)),transparent_0,var(--shimmer-color)_var(--spread),transparent_var(--spread))] [translate:0_0]\" />\n          </div>\n        </div>\n        {children}\n\n        {/* Highlight */}\n        <div\n          className={cn(\n            \"insert-0 absolute size-full\",\n            \"rounded-2xl px-4 py-1.5 text-sm font-medium shadow-[inset_0_-8px_10px_#ffffff1f]\",\n            // transition\n            \"transform-gpu transition-all duration-300 ease-in-out\",\n            // on hover\n            \"group-hover:shadow-[inset_0_-6px_10px_#ffffff3f]\",\n            // on click\n            \"group-active:shadow-[inset_0_-10px_10px_#ffffff3f]\",\n          )}\n        />\n\n        {/* backdrop */}\n        <div\n          className={cn(\n            \"absolute -z-20 [background:var(--bg)] [border-radius:var(--radius)] [inset:var(--cut)]\",\n          )}\n        />\n      </button>\n    );\n  },\n);\n\nShimmerButton.displayName = \"ShimmerButton\";\n\nexport { ShimmerButton };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAeA,MAAM,8BAAgB,qMAAA,CAAA,UAAK,CAAC,UAAU,CACpC,CACE,EACE,YAAY,EACZ,cAAc,QAAQ,EACtB,kBAAkB,IAAI,EACtB,eAAe,OAAO,EACtB,UAAU,EACV,SAAS,EACT,QAAQ,EACR,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,GAAG,OACJ,EACD;IAEA,qBAAqB;IACrB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,iEAAiE;IACjE,MAAM,YAAY;QAChB,MAAM,aAAa;YACjB,SAAS;gBACP,oDAAoD;gBACpD,IAAI,cAAc;gBAClB,SAAS,gBAAgB;YAC3B;YACA,WAAW;gBACT,2DAA2D;gBAC3D,IAAI,cAAc;gBAClB,SAAS,gBAAgB;YAC3B;YACA,QAAQ;gBACN,oDAAoD;gBACpD,IAAI,cAAc;gBAClB,SAAS,gBAAgB;YAC3B;QACF;QACA,OAAO,UAAU,CAAC,QAAQ;IAC5B;IAEA,MAAM,SAAS;IAEf,qBACE,8OAAC;QACC,OACE;YACE,YAAY;YACZ,mBAAmB,OAAO,OAAO;YACjC,YAAY;YACZ,WAAW;YACX,SAAS;YACT,QAAQ,OAAO,EAAE;QACnB;QAEF,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+MACA,qFACA,WAAW,CAAC,KAAK,EACjB;QAEF,KAAK;QACJ,GAAG,KAAK;;0BAGT,8OAAC;gBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,oBACA;0BAIF,cAAA,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;YAGlB;0BAGD,8OAAC;gBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+BACA,oFACA,aAAa;gBACb,yDACA,WAAW;gBACX,oDACA,WAAW;gBACX;;;;;;0BAKJ,8OAAC;gBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;;AAKV;AAGF,cAAc,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/app/providers/page.tsx"], "sourcesContent": ["import { <PERSON>ada<PERSON> } from 'next'\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { AuroraBackgroundLayer } from \"@/components/ui/aurora-background\";\nimport { GoldenGlowingCardContainer } from \"@/components/ui/golden-glowing-card-container\";\nimport { BentoGrid, BentoCard } from \"@/components/ui/bento-grid\";\nimport { ShimmerButton } from \"@/components/ui/shimmer-button\";\nimport { LayoutTemplate, FileText, Users, BarChart2, Calendar, CreditCard, Globe, Palette, Zap, Shield } from \"lucide-react\";\nimport Link from \"next/link\";\n\nexport const metadata: Metadata = {\n  title: 'For Beauty Professionals - Grow Your Business',\n  description: 'Join <PERSON> as a beauty professional. Access powerful business tools, connect with clients, and grow your beauty business with our all-in-one platform.',\n  openGraph: {\n    title: 'For Beauty Professionals - Grow Your Business | Vierla',\n    description: 'Join <PERSON> as a beauty professional and access powerful tools to grow your business.',\n    url: 'https://vierla.com/providers',\n    siteName: 'Vierla',\n    locale: 'en_CA',\n    type: 'website',\n  },\n  twitter: {\n    card: 'summary_large_image',\n    title: 'For Beauty Professionals - Grow Your Business | Vierla',\n    description: 'Join Vierla as a beauty professional and access powerful tools to grow your business.',\n  },\n};\n\nexport default function ProviderApp() {\n  return (\n    <div className=\"page-provider relative overflow-hidden\">\n      <AuroraBackgroundLayer variant=\"feature\" />\n      \n      {/* Hero Section */}\n      <section className=\"relative z-10 w-full px-4 py-20 pt-32\">\n        <div className=\"text-center max-w-6xl mx-auto\">\n          <h1 className=\"text-4xl md:text-6xl font-black mb-6 leading-none text-[#2D2A26] dark:text-[#F4F1E8] drop-shadow-lg font-sans\">\n            FOR BEAUTY PROFESSIONALS - EVERYTHING YOU NEED TO RUN YOUR BUSINESS\n          </h1>\n          <p className=\"text-xl md:text-2xl text-[#8B9A8C] dark:text-[#A9A299] mb-8 leading-relaxed max-w-4xl mx-auto drop-shadow-sm font-inter\">\n            Join our curated network of top-tier beauty professionals. Build your business with powerful tools designed for your success.\n          </p>\n          \n          <Link href=\"/apply\" className=\"mb-8 inline-block\">\n            <ShimmerButton\n              size=\"lg\"\n              background=\"#B8956A\"\n              shimmerColor=\"#F4F1E8\"\n              className=\"px-8 py-4 text-lg font-medium text-[var(--master-text-primary-light)]\"\n            >\n              <span className=\"flex items-center\">\n                Apply to Join Vierla\n                <FileText className=\"ml-2 w-5 h-5\" />\n              </span>\n            </ShimmerButton>\n          </Link>\n        </div>\n      </section>\n\n      {/* Business Tools Section */}\n      <section className=\"relative z-10 py-20\">\n        <div className=\"container mx-auto px-4\">\n          <BentoGrid className=\"lg:grid-rows-2 max-w-6xl mx-auto min-h-[800px] mb-16\">\n            {[\n              {\n                Icon: Globe,\n                name: \"Digital Store & Website Builder\",\n                description: \"Create stunning, professional websites and branded online presence with our AI-powered builder. Drag & drop interface, AI content generation, portfolio gallery, and custom domains.\",\n                href: \"/contact\",\n                cta: \"Learn More\",\n                className: \"lg:row-start-1 lg:row-end-2 lg:col-start-1 lg:col-end-3\",\n              },\n              {\n                Icon: FileText,\n                name: \"Smart Invoicing\",\n                description: \"Streamline your billing process with intelligent invoicing features. Automated reminders, multiple currencies, payment tracking, and professional templates.\",\n                href: \"/contact\",\n                cta: \"Learn More\",\n                className: \"lg:row-start-1 lg:row-end-2 lg:col-start-3 lg:col-end-4\",\n              },\n              {\n                Icon: Users,\n                name: \"Integrated CRM\",\n                description: \"Manage customer relationships and grow your business effectively. Customer database, sales pipeline, task automation, and journey tracking.\",\n                href: \"/contact\",\n                cta: \"Learn More\",\n                className: \"lg:row-start-2 lg:row-end-3 lg:col-start-1 lg:col-end-2\",\n              },\n              {\n                Icon: BarChart2,\n                name: \"Business Analytics\",\n                description: \"Track your performance with detailed insights on bookings, revenue, customer behavior, and growth trends. Revenue tracking, customer insights, booking analytics, and growth metrics.\",\n                href: \"/contact\",\n                cta: \"Learn More\",\n                className: \"lg:row-start-2 lg:row-end-3 lg:col-start-2 lg:col-end-4\",\n              }\n            ].map((tool) => (\n              <BentoCard key={tool.name} {...tool} />\n            ))}\n          </BentoGrid>\n\n\n\n          <BentoGrid className=\"lg:grid-rows-1 max-w-6xl mx-auto min-h-[400px]\">\n            {[\n              {\n                Icon: Calendar,\n                name: \"Booking Management\",\n                description: \"Centralized appointment scheduling with calendar integration, automated reminders, and easy rescheduling. Calendar sync, auto reminders, easy rescheduling, and availability control.\",\n                href: \"/contact\",\n                cta: \"Learn More\",\n                className: \"lg:row-start-1 lg:row-end-2 lg:col-start-1 lg:col-end-2\",\n              },\n              {\n                Icon: CreditCard,\n                name: \"Payment Processing\",\n                description: \"Secure payment handling with multiple payment methods and direct deposits to your preferred account. Multiple payment methods, secure transactions, direct deposits, and transaction history.\",\n                href: \"/contact\",\n                cta: \"Learn More\",\n                className: \"lg:row-start-1 lg:row-end-2 lg:col-start-2 lg:col-end-4\",\n              }\n            ].map((feature) => (\n              <BentoCard key={feature.name} {...feature} />\n            ))}\n          </BentoGrid>\n\n          {/* Coming Soon Section */}\n          <div className=\"text-center mt-16\">\n            <GoldenGlowingCardContainer>\n              <div className=\"text-center py-4\">\n                <h3 className=\"text-3xl md:text-4xl font-black text-light-off-white mb-4 drop-shadow-lg font-tai-heritage\">COMING SOON</h3>\n                <p className=\"text-lg md:text-xl text-warm-beige mb-6 max-w-2xl mx-auto drop-shadow-sm font-sans\">\n                  We're building something special for beauty professionals. Join our waitlist to be the first to know when we launch.\n                </p>\n                \n                <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n                  <div className=\"flex items-center px-6 py-3 bg-charcoal/50 rounded-xl border border-neutral-off-white/20 cursor-not-allowed opacity-75\">\n                    <svg className=\"w-8 h-8 mr-3 text-neutral-off-white\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                      <path d=\"M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z\"/>\n                    </svg>\n                    <div className=\"text-left\">\n                      <div className=\"text-xs text-brand-beige/70\">Coming Soon to</div>\n                      <div className=\"text-lg font-semibold text-neutral-off-white\">Apple App Store</div>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center px-6 py-3 bg-charcoal/50 rounded-xl border border-neutral-off-white/20 cursor-not-allowed opacity-75\">\n                    <svg className=\"w-8 h-8 mr-3 text-neutral-off-white\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                      <path d=\"M3,20.5V3.5C3,2.91 3.34,2.39 3.84,2.15L13.69,12L3.84,21.85C3.34,21.61 3,21.09 3,20.5M16.81,15.12L6.05,21.34L14.54,12.85L16.81,15.12M20.16,10.81C20.5,11.08 20.75,11.5 20.75,12C20.75,12.5 20.53,12.9 20.18,13.18L17.89,14.5L15.39,12L17.89,9.5L20.16,10.81M6.05,2.66L16.81,8.88L14.54,11.15L6.05,2.66Z\"/>\n                    </svg>\n                    <div className=\"text-left\">\n                      <div className=\"text-xs text-brand-beige/70\">Coming Soon to</div>\n                      <div className=\"text-lg font-semibold text-neutral-off-white\">Google Play Store</div>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"flex justify-center mt-8\">\n                  <Link href=\"/apply\">\n                    <ShimmerButton\n                      size=\"lg\"\n                      background=\"#B8956A\"\n                      shimmerColor=\"#E5D4A1\"\n                      className=\"px-8 py-4 text-lg font-medium text-[#2D2A26]\"\n                    >\n                      <span className=\"flex items-center\">\n                        Apply to Join Vierla\n                        <FileText className=\"ml-2 w-5 h-5\" />\n                      </span>\n                    </ShimmerButton>\n                  </Link>\n                </div>\n              </div>\n            </GoldenGlowingCardContainer>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,WAAW;QACT,OAAO;QACP,aAAa;QACb,KAAK;QACL,UAAU;QACV,QAAQ;QACR,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;IACf;AACF;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,yIAAA,CAAA,wBAAqB;gBAAC,SAAQ;;;;;;0BAG/B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAgH;;;;;;sCAG9H,8OAAC;4BAAE,WAAU;sCAA0H;;;;;;sCAIvI,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAS,WAAU;sCAC5B,cAAA,8OAAC,sIAAA,CAAA,gBAAa;gCACZ,MAAK;gCACL,YAAW;gCACX,cAAa;gCACb,WAAU;0CAEV,cAAA,8OAAC;oCAAK,WAAU;;wCAAoB;sDAElC,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ9B,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,kIAAA,CAAA,YAAS;4BAAC,WAAU;sCAClB;gCACC;oCACE,MAAM,oMAAA,CAAA,QAAK;oCACX,MAAM;oCACN,aAAa;oCACb,MAAM;oCACN,KAAK;oCACL,WAAW;gCACb;gCACA;oCACE,MAAM,8MAAA,CAAA,WAAQ;oCACd,MAAM;oCACN,aAAa;oCACb,MAAM;oCACN,KAAK;oCACL,WAAW;gCACb;gCACA;oCACE,MAAM,oMAAA,CAAA,QAAK;oCACX,MAAM;oCACN,aAAa;oCACb,MAAM;oCACN,KAAK;oCACL,WAAW;gCACb;gCACA;oCACE,MAAM,gOAAA,CAAA,YAAS;oCACf,MAAM;oCACN,aAAa;oCACb,MAAM;oCACN,KAAK;oCACL,WAAW;gCACb;6BACD,CAAC,GAAG,CAAC,CAAC,qBACL,8OAAC,kIAAA,CAAA,YAAS;oCAAkB,GAAG,IAAI;mCAAnB,KAAK,IAAI;;;;;;;;;;sCAM7B,8OAAC,kIAAA,CAAA,YAAS;4BAAC,WAAU;sCAClB;gCACC;oCACE,MAAM,0MAAA,CAAA,WAAQ;oCACd,MAAM;oCACN,aAAa;oCACb,MAAM;oCACN,KAAK;oCACL,WAAW;gCACb;gCACA;oCACE,MAAM,kNAAA,CAAA,aAAU;oCAChB,MAAM;oCACN,aAAa;oCACb,MAAM;oCACN,KAAK;oCACL,WAAW;gCACb;6BACD,CAAC,GAAG,CAAC,CAAC,wBACL,8OAAC,kIAAA,CAAA,YAAS;oCAAqB,GAAG,OAAO;mCAAzB,QAAQ,IAAI;;;;;;;;;;sCAKhC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,2JAAA,CAAA,6BAA0B;0CACzB,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA6F;;;;;;sDAC3G,8OAAC;4CAAE,WAAU;sDAAqF;;;;;;sDAIlG,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;4DAAsC,SAAQ;4DAAY,MAAK;sEAC5E,cAAA,8OAAC;gEAAK,GAAE;;;;;;;;;;;sEAEV,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAA8B;;;;;;8EAC7C,8OAAC;oEAAI,WAAU;8EAA+C;;;;;;;;;;;;;;;;;;8DAGlE,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;4DAAsC,SAAQ;4DAAY,MAAK;sEAC5E,cAAA,8OAAC;gEAAK,GAAE;;;;;;;;;;;sEAEV,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EAA8B;;;;;;8EAC7C,8OAAC;oEAAI,WAAU;8EAA+C;;;;;;;;;;;;;;;;;;;;;;;;sDAKpE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;0DACT,cAAA,8OAAC,sIAAA,CAAA,gBAAa;oDACZ,MAAK;oDACL,YAAW;oDACX,cAAa;oDACb,WAAU;8DAEV,cAAA,8OAAC;wDAAK,WAAU;;4DAAoB;0EAElC,8OAAC,8MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY5C", "debugId": null}}]}