# 🧩 Vierla Component Reference Guide

**Version:** 2.0  
**Last Updated:** January 16, 2025  

---

## 📋 **Table of Contents**

1. [UI Components](#ui-components)
2. [Marketing Components](#marketing-components)
3. [SEO Components](#seo-components)
4. [Provider Components](#provider-components)
5. [Component Props Reference](#component-props-reference)
6. [Usage Examples](#usage-examples)
7. [Styling Guidelines](#styling-guidelines)

---

## 🎨 **UI Components**

### **AuroraBackgroundLayer**
Animated gradient background with theme-aware colors.

**Location:** `components/ui/aurora-background.tsx`

**Props:**
```tsx
interface AuroraBackgroundProps {
  variant?: 'default' | 'feature' | 'pricing'
  className?: string
}
```

**Usage:**
```tsx
import { AuroraBackgroundLayer } from "@/components/ui/aurora-background";

// Default variant
<AuroraBackgroundLayer />

// Feature page variant
<AuroraBackgroundLayer variant="feature" />

// With custom className
<AuroraBackgroundLayer className="custom-class" />
```

**Features:**
- Theme-aware color transitions
- Smooth animations
- Multiple variants for different pages
- Responsive design

---

### **GoldenGlowingCardContainer**
Card component with glassmorphism effects and golden glow.

**Location:** `components/ui/golden-glowing-card-container.tsx`

**Props:**
```tsx
interface GoldenGlowingCardContainerProps {
  children: React.ReactNode
  className?: string
}
```

**Usage:**
```tsx
import { GoldenGlowingCardContainer } from "@/components/ui/golden-glowing-card-container";

<GoldenGlowingCardContainer>
  <h3>Card Title</h3>
  <p>Card content goes here</p>
</GoldenGlowingCardContainer>
```

**Features:**
- Consistent glassmorphism styling
- Golden glow effects
- Theme-aware colors
- Responsive design

---

### **ShinyButton**
Interactive button with shimmer effects and customizable colors.

**Location:** `components/ui/shiny-button.tsx`

**Props:**
```tsx
interface ShinyButtonProps {
  children: React.ReactNode
  size?: 'sm' | 'md' | 'lg'
  background?: string
  shimmerColor?: string
  className?: string
  onClick?: () => void
  disabled?: boolean
}
```

**Usage:**
```tsx
import ShinyButton from "@/components/ui/shiny-button";

// Basic usage
<ShinyButton>Click Me</ShinyButton>

// With custom colors and size
<ShinyButton 
  size="lg"
  background="#B8956A"
  shimmerColor="#F4F1E8"
>
  Get Started
</ShinyButton>

// With click handler
<ShinyButton onClick={() => console.log('Clicked!')}>
  Interactive Button
</ShinyButton>
```

**Features:**
- Shimmer animation effects
- Customizable colors per instance
- Multiple size options
- Click event handling
- Disabled state support

---

### **BentoGrid & BentoCard**
Grid layout system for displaying features and content cards.

**Location:** `components/ui/bento-grid.tsx`

**Props:**
```tsx
interface BentoGridProps {
  children: React.ReactNode
  className?: string
}

interface BentoCardProps {
  name: string
  description: string
  href: string
  cta: string
  Icon: React.ComponentType<any>
  className?: string
}
```

**Usage:**
```tsx
import { BentoGrid, BentoCard } from "@/components/ui/bento-grid";
import { LayoutTemplate } from "lucide-react";

<BentoGrid className="lg:grid-rows-3">
  <BentoCard
    name="Website Builder"
    description="Create stunning websites with our drag-and-drop builder"
    href="/features"
    cta="Learn More"
    Icon={LayoutTemplate}
    className="lg:row-span-2"
  />
  {/* More cards... */}
</BentoGrid>
```

**Features:**
- Responsive grid layout
- Interactive hover effects
- Icon integration
- Customizable card sizes
- Click tracking and navigation

---

### **ShimmerButton**
Button with edge shimmer effects.

**Location:** `components/ui/shimmer-button.tsx`

**Props:**
```tsx
interface ShimmerButtonProps {
  children: React.ReactNode
  size?: 'sm' | 'md' | 'lg'
  background?: string
  shimmerColor?: string
  className?: string
}
```

**Usage:**
```tsx
import { ShimmerButton } from "@/components/ui/shimmer-button";

<ShimmerButton 
  size="lg"
  background="#B8956A"
  shimmerColor="#F4F1E8"
>
  Apply Now
</ShimmerButton>
```

**Features:**
- Edge-only shimmer effects
- Color customization
- Size variants
- Theme-aware styling

---

### **CookieConsent**
GDPR-compliant cookie consent popup.

**Location:** `components/ui/cookie-consent.tsx`

**Usage:**
```tsx
import { CookieConsent } from "@/components/ui/cookie-consent";

// Automatically included in layout.tsx
<CookieConsent />
```

**Features:**
- GDPR compliance
- Preference management
- Local storage integration
- Glassmorphism styling
- Responsive design

---

## 📢 **Marketing Components**

### **Navbar**
Main navigation component with theme switcher.

**Location:** `components/marketing/navbar.tsx`

**Usage:**
```tsx
import { Navbar } from "@/components/marketing/navbar";

<Navbar />
```

**Features:**
- Responsive navigation
- Theme switcher integration
- Mobile menu support
- Glassmorphism styling
- Active link highlighting

---

### **Footer**
Site footer with links and company information.

**Location:** `components/marketing/footer.tsx`

**Usage:**
```tsx
import { Footer } from "@/components/marketing/footer";

<Footer />
```

**Features:**
- Organized link sections
- Company information
- Social media links (when available)
- Responsive layout
- Consistent styling

---

### **MultiStepForm**
Professional application form with progress tracking.

**Location:** `components/marketing/multistep-form.tsx`

**Usage:**
```tsx
import { MultiStepForm } from "@/components/marketing/multistep-form";

<MultiStepForm />
```

**Features:**
- Multi-step progression
- Form validation
- Progress indicators
- Data persistence
- Responsive design

---

## 🔍 **SEO Components**

### **StructuredData**
JSON-LD structured data for search engines.

**Location:** `components/seo/structured-data.tsx`

**Props:**
```tsx
interface StructuredDataProps {
  page?: 'home' | 'features' | 'pricing' | 'about' | 'contact' | 'providers' | 'apply'
}
```

**Usage:**
```tsx
import { StructuredData } from "@/components/seo/structured-data";

// Homepage schemas
<StructuredData page="home" />

// Features page with SoftwareApplication schema
<StructuredData page="features" />

// Pricing page with Product schema
<StructuredData page="pricing" />
```

**Features:**
- Organization schema
- Website schema
- Service schema
- Page-specific schemas
- Breadcrumb support

---

### **BreadcrumbSchema**
Breadcrumb navigation for SEO.

**Location:** `components/seo/breadcrumb-schema.tsx`

**Props:**
```tsx
interface BreadcrumbSchemaProps {
  items: BreadcrumbItem[]
}

interface BreadcrumbItem {
  name: string
  url: string
}
```

**Usage:**
```tsx
import { BreadcrumbSchema, getBreadcrumbsForPage } from "@/components/seo/breadcrumb-schema";

// Automatic breadcrumb generation
<BreadcrumbSchema items={getBreadcrumbsForPage('features')} />

// Manual breadcrumb definition
<BreadcrumbSchema items={[
  { name: "Home", url: "https://vierla.com" },
  { name: "Features", url: "https://vierla.com/features" }
]} />
```

**Features:**
- Automatic breadcrumb generation
- Manual breadcrumb support
- SEO-optimized structure
- JSON-LD format

---

## 🔧 **Provider Components**

### **ThemeProvider**
Theme management with system preference detection.

**Location:** `components/providers/theme-provider.tsx`

**Usage:**
```tsx
import { ThemeProvider } from "@/components/providers/theme-provider";

<ThemeProvider
  attribute="class"
  defaultTheme="system"
  enableSystem
  disableTransitionOnChange={false}
>
  {children}
</ThemeProvider>
```

**Features:**
- Light/dark theme switching
- System preference detection
- Smooth transitions
- Persistent theme selection

---

### **ToastProvider**
Toast notification system.

**Location:** `components/ui/toast.tsx`

**Usage:**
```tsx
import { ToastProvider } from "@/components/ui/toast";

<ToastProvider>
  {children}
</ToastProvider>
```

**Features:**
- Success/error notifications
- Auto-dismiss functionality
- Customizable styling
- Accessible design

---

## 📝 **Component Props Reference**

### **Common Props**
Most components accept these common props:

```tsx
interface CommonProps {
  children?: React.ReactNode
  className?: string
  id?: string
}
```

### **Size Variants**
Components with size options typically use:

```tsx
type Size = 'sm' | 'md' | 'lg'
```

### **Color Props**
Components with color customization use:

```tsx
interface ColorProps {
  background?: string      // Hex color for background
  shimmerColor?: string   // Hex color for shimmer effects
  borderColor?: string    // Hex color for borders
}
```

### **Event Handlers**
Interactive components support:

```tsx
interface EventProps {
  onClick?: () => void
  onHover?: () => void
  onFocus?: () => void
  onBlur?: () => void
}
```

---

## 💡 **Usage Examples**

### **Creating a Feature Page**
```tsx
import { AuroraBackgroundLayer } from "@/components/ui/aurora-background";
import { GoldenGlowingCardContainer } from "@/components/ui/golden-glowing-card-container";
import { StructuredData } from "@/components/seo/structured-data";
import { BreadcrumbSchema, getBreadcrumbsForPage } from "@/components/seo/breadcrumb-schema";

export default function FeaturePage() {
  return (
    <div className="page-features relative overflow-hidden">
      <StructuredData page="features" />
      <BreadcrumbSchema items={getBreadcrumbsForPage('features')} />
      <AuroraBackgroundLayer variant="feature" />
      
      <section className="relative z-10 py-20">
        <div className="container mx-auto px-4">
          <h1 className="text-4xl font-bold mb-8">Features</h1>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            <GoldenGlowingCardContainer>
              <h3 className="text-xl font-semibold mb-4">Feature 1</h3>
              <p>Feature description...</p>
            </GoldenGlowingCardContainer>
            {/* More cards... */}
          </div>
        </div>
      </section>
    </div>
  )
}
```

### **Custom Button Implementation**
```tsx
import ShinyButton from "@/components/ui/shiny-button";

function CustomCTA() {
  const handleClick = () => {
    // Analytics tracking
    console.log('CTA clicked');
    // Navigation or action
  };

  return (
    <ShinyButton
      size="lg"
      background="#B8956A"
      shimmerColor="#F4F1E8"
      onClick={handleClick}
      className="w-full md:w-auto"
    >
      Get Started Today
    </ShinyButton>
  );
}
```

### **Form with Validation**
```tsx
import { useState } from 'react';
import { GoldenGlowingCardContainer } from "@/components/ui/golden-glowing-card-container";
import ShinyButton from "@/components/ui/shiny-button";

function ContactForm() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Form submission logic
  };

  return (
    <GoldenGlowingCardContainer>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">Name</label>
          <input
            type="text"
            value={formData.name}
            onChange={(e) => setFormData({...formData, name: e.target.value})}
            className="w-full p-3 rounded-lg border border-gray-300"
            required
          />
        </div>
        {/* More fields... */}
        
        <ShinyButton type="submit" size="lg" className="w-full">
          Send Message
        </ShinyButton>
      </form>
    </GoldenGlowingCardContainer>
  );
}
```

---

## 🎨 **Styling Guidelines**

### **Component Styling Best Practices**

1. **Use CSS Variables:** Leverage the design system variables for consistency
2. **Theme Awareness:** Ensure components work in both light and dark themes
3. **Responsive Design:** Use Tailwind's responsive prefixes
4. **Glassmorphism:** Apply consistent glassmorphism classes
5. **Accessibility:** Include proper ARIA labels and keyboard navigation

### **Color Usage**
```tsx
// Use design system colors
className="bg-[var(--background)] text-[var(--foreground)]"

// Theme-aware text colors
className="text-[#2D2A26] dark:text-[#F4F1E8]"

// Accent colors
className="text-[var(--accent)] border-[var(--accent)]"
```

### **Spacing and Layout**
```tsx
// Use 8-point grid system
className="p-4 m-8 gap-6"  // 16px, 32px, 24px

// Responsive spacing
className="p-4 md:p-6 lg:p-8"

// Container patterns
className="container mx-auto px-4 max-w-6xl"
```

### **Typography**
```tsx
// Headlines
className="font-playfair text-4xl md:text-6xl font-black"

// Body text
className="font-inter text-base leading-relaxed"

// Section headers
className="font-tai-heritage text-2xl font-semibold"
```

---

**Component Reference maintained by:** Augment Agent  
**Next review date:** February 16, 2025
