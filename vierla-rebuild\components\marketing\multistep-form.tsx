"use client";
import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ShimmerButton } from "@/components/ui/shimmer-button";
import ShinyButton from "@/components/ui/shiny-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { DropdownSelect, type DropdownOption } from "@/components/ui/dropdown-select";

interface ApplicationData {
  // Personal Information
  firstName: string;
  lastName: string;
  email: string;
  phone: string;

  // Professional Information
  businessName: string;
  services: string[];
  experience: string;
  certifications: string[];

  // Location & Availability
  serviceAreas: string[];
  availability: string[];
  travelRadius: string;

  // Business Details
  insurance: boolean;
  license: string;
  portfolio: string;
  rates: string;

  // Additional Information
  motivation: string;
  references: string;
}

const serviceOptions = [
  'Hair Styling', 'Hair Cutting', 'Hair Coloring', 'Blowouts',
  'Makeup Application', 'Bridal Makeup', 'Special Event Makeup',
  'Manicures', 'Pedicures', 'Nail Art', 'Gel Polish',
  'Eyebrow Shaping', 'Eyebrow Threading', 'Eyebrow Tinting',
  'Eyelash Extensions', 'Lash Lifts', 'Lash Tinting',
  'Braiding', 'Box Braids', 'Cornrows', 'Protective Styles',
  'Loc Maintenance', 'Loc Styling', 'Retwisting',
  'Beard Trimming', 'Hot Towel Shaves', 'Men\'s Grooming'
];

const fadeInUp = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: { duration: 0.3 } },
};

const contentVariants = {
  hidden: { opacity: 0, x: 50 },
  visible: { opacity: 1, x: 0, transition: { duration: 0.3 } },
  exit: { opacity: 0, x: -50, transition: { duration: 0.2 } },
};

const steps = [
  { id: 1, name: "Personal Information", stepWord: "Personal", fields: ["firstName", "lastName", "email", "phone"] },
  { id: 2, name: "Professional Details", stepWord: "Professional", fields: ["businessName", "services", "experience", "certifications"] },
  { id: 3, name: "Service Areas", stepWord: "Location", fields: ["serviceAreas", "availability", "travelRadius"] },
  { id: 4, name: "Business Information", stepWord: "Business", fields: ["insurance", "license", "portfolio", "rates"] },
  { id: 5, name: "Additional Details", stepWord: "Details", fields: ["motivation", "references"] },
  { id: 6, name: "Review & Submit", stepWord: "Review", fields: [] },
];

export function MultiStepForm() {
  const [currentStep, setCurrentStep] = useState(0);

  // Experience options for dropdown
  const experienceOptions: DropdownOption[] = [
    { value: '0-1', label: 'Less than 1 year' },
    { value: '1-3', label: '1-3 years' },
    { value: '3-5', label: '3-5 years' },
    { value: '5-10', label: '5-10 years' },
    { value: '10+', label: '10+ years' }
  ];

  const [formData, setFormData] = useState<ApplicationData>({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    businessName: "",
    services: [],
    experience: "",
    certifications: [],
    serviceAreas: [],
    availability: [],
    travelRadius: "",
    insurance: false,
    license: "",
    portfolio: "",
    rates: "",
    motivation: "",
    references: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const next = () => setCurrentStep((prev) => (prev < steps.length - 1 ? prev + 1 : prev));
  const prev = () => setCurrentStep((prev) => (prev > 0 ? prev - 1 : prev));

  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleArrayToggle = (field: keyof ApplicationData, value: string) => {
    const currentArray = formData[field] as string[];
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value];
    updateFormData(field, newArray);
  };

  // Check if step is valid for next button
  const isStepValid = () => {
    switch (currentStep) {
      case 0:
        return formData.firstName.trim() !== "" && formData.lastName.trim() !== "" && formData.email.trim() !== "";
      case 1:
        return formData.businessName.trim() !== "" && formData.services.length > 0;
      case 2:
        return formData.serviceAreas.length > 0;
      case 3:
        return formData.portfolio.trim() !== "";
      case 4:
        return formData.motivation.trim() !== "";
      default:
        return true;
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      // Submit to the exact same API endpoint as SOURCE
      const response = await fetch('/api/professional-application', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          timestamp: new Date().toISOString()
        }),
      });

      if (response.ok) {
        setSubmitSuccess(true);
      } else {
        throw new Error('Submission failed');
      }
    } catch (error) {
      // Fallback to localStorage (same as SOURCE)
      const existingApplications = JSON.parse(localStorage.getItem('vierla-applications') || '[]');
      existingApplications.push({
        ...formData,
        timestamp: new Date().toISOString(),
        status: 'pending'
      });
      localStorage.setItem('vierla-applications', JSON.stringify(existingApplications));
      setSubmitSuccess(true);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (submitSuccess) {
    return (
      <Card className="w-full max-w-2xl mx-auto bg-white/10 backdrop-blur-md border border-white/20">
        <CardContent className="p-8 text-center">
          <h2 className="text-2xl font-bold mb-4 text-primary drop-shadow-lg">Application Submitted!</h2>
          <p className="text-vierla-text/80 mb-6 drop-shadow-sm">
            Thank you for your application. We'll review it and get back to you within 2-3 business days.
          </p>
          <Button asChild>
            <a href="/">Return to Homepage</a>
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="w-full max-w-4xl mx-auto">

      {/* Progress Indicator */}
      <motion.div
        className="mb-8"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex justify-between mb-2">
          {steps.map((step, index) => (
            <motion.div
              key={index}
              className="flex flex-col items-center"
              whileHover={{ scale: 1.1 }}
            >
              <motion.div
                className={cn(
                  "w-5 h-5 rounded-full cursor-pointer transition-all duration-300 border-2",
                  index < currentStep
                    ? "ring-2"
                    : index === currentStep
                      ? "ring-4"
                      : "opacity-50",
                )}
                style={{
                  backgroundColor: index <= currentStep ? 'var(--icon-accent)' : 'transparent',
                  borderColor: 'var(--icon-accent)',
                  ringColor: index <= currentStep ? 'var(--accent-hover-overlay)' : 'transparent'
                }}
                onClick={() => {
                  // Only allow going back or to completed steps
                  if (index <= currentStep) {
                    setCurrentStep(index);
                  }
                }}
                whileTap={{ scale: 0.95 }}
              />
              <motion.span
                className={cn(
                  "text-xs mt-1.5 hidden sm:block drop-shadow-sm font-sans transition-colors duration-300",
                  index === currentStep ? "font-medium" : "opacity-60",
                )}
                style={{ color: 'var(--text-primary)' }}
              >
                {step.stepWord}
              </motion.span>
            </motion.div>
          ))}
        </div>
        <div className="w-full h-1.5 rounded-full overflow-hidden mt-2"
             style={{ backgroundColor: 'var(--border-subtle)' }}>
          <motion.div
            className="h-full"
            style={{ backgroundColor: 'var(--icon-accent)' }}
            initial={{ width: 0 }}
            animate={{ width: `${(currentStep / (steps.length - 1)) * 100}%` }}
            transition={{ duration: 0.3 }}
          />
        </div>
      </motion.div>

      <Card className="w-full shadow-2xl rounded-3xl"
            style={{
              backgroundColor: 'var(--card-bg)',
              borderColor: 'var(--border-subtle)',
              boxShadow: 'var(--shadow-card)'
            }}>
        <CardHeader>
          {/* Step Indicator - Top Right */}
          <div className="flex justify-end mb-4">
            <p className="text-sm drop-shadow-sm font-sans px-4 py-2 rounded-lg"
               style={{
                 color: 'var(--text-primary)',
                 backgroundColor: 'var(--card-bg)',
                 border: '1px solid var(--border-subtle)'
               }}>
              Step {currentStep + 1} of {steps.length}: {steps[currentStep].name}
            </p>
          </div>
          <CardTitle className="drop-shadow-lg font-tai-heritage" style={{ color: 'var(--text-primary)' }}>
            {steps[currentStep].name}
          </CardTitle>
          <CardDescription className="drop-shadow-sm font-sans" style={{ color: 'var(--text-secondary)' }}>
            {currentStep === 0 && "Let's start with your basic information"}
            {currentStep === 1 && "Tell us about your professional background"}
            {currentStep === 2 && "Where do you provide services?"}
            {currentStep === 3 && "Business credentials and portfolio"}
            {currentStep === 4 && "Help us understand your motivation"}
            {currentStep === 5 && "Review your information before submitting"}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentStep}
              initial={{ x: 300, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: -300, opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              {/* Step 1: Personal Information */}
              {currentStep === 0 && (
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="firstName" className="drop-shadow-sm" style={{ color: 'var(--text-primary)' }}>First Name</Label>
                      <Input
                        id="firstName"
                        value={formData.firstName}
                        onChange={(e) => updateFormData('firstName', e.target.value)}
                        placeholder="Enter your first name"
                        className="theme-input"
                        required
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="lastName" className="drop-shadow-sm" style={{ color: 'var(--text-primary)' }}>Last Name</Label>
                      <Input
                        id="lastName"
                        value={formData.lastName}
                        onChange={(e) => updateFormData('lastName', e.target.value)}
                        placeholder="Enter your last name"
                        className="theme-input"
                        required
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="email" className="drop-shadow-sm" style={{ color: 'var(--text-primary)' }}>Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => updateFormData('email', e.target.value)}
                      placeholder="Enter your email address"
                      className="theme-input"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="phone" className="drop-shadow-sm" style={{ color: 'var(--text-primary)' }}>Phone Number</Label>
                    <Input
                      id="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => updateFormData('phone', e.target.value)}
                      placeholder="Enter your phone number"
                      className="theme-input"
                      required
                    />
                  </div>
                </div>
              )}

              {/* Step 2: Professional Details */}
              {currentStep === 1 && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="businessName" className="text-vierla-text drop-shadow-sm">Business Name</Label>
                    <Input
                      id="businessName"
                      value={formData.businessName}
                      onChange={(e) => updateFormData('businessName', e.target.value)}
                      placeholder="Enter your business name"
                      className="text-vierla-text placeholder:text-[#F5F5DC] bg-white/10 border-white/20"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label className="text-vierla-text drop-shadow-sm">Services Offered</Label>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 max-h-48 overflow-y-auto">
                      {serviceOptions.map((service) => (
                        <div key={service} className="flex items-center space-x-2 p-2 rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition-colors">
                          <Checkbox
                            id={service}
                            checked={formData.services.includes(service)}
                            onCheckedChange={() => handleArrayToggle('services', service)}
                          />
                          <Label htmlFor={service} className="text-sm text-vierla-text drop-shadow-sm cursor-pointer">{service}</Label>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <DropdownSelect
                      label="Years of Experience"
                      options={experienceOptions}
                      value={formData.experience}
                      onChange={(value) => updateFormData('experience', value)}
                      placeholder="Select your experience level"
                      required
                    />
                  </div>
                </div>
              )}

              {/* Step 3: Service Areas */}
              {currentStep === 2 && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label className="text-vierla-text drop-shadow-sm">Service Areas</Label>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                      {['Toronto Downtown', 'North York', 'Scarborough', 'Etobicoke', 'Mississauga', 'Brampton', 'Ottawa Downtown', 'Kanata', 'Orleans', 'Nepean'].map((area) => (
                        <div key={area} className="flex items-center space-x-2 p-2 rounded-lg bg-white/5 border border-white/10 hover:bg-white/10 transition-colors">
                          <Checkbox
                            id={area}
                            checked={formData.serviceAreas.includes(area)}
                            onCheckedChange={() => handleArrayToggle('serviceAreas', area)}
                          />
                          <Label htmlFor={area} className="text-sm text-vierla-text drop-shadow-sm cursor-pointer">{area}</Label>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="travelRadius" className="text-vierla-text drop-shadow-sm">Travel Radius (km)</Label>
                    <Input
                      id="travelRadius"
                      value={formData.travelRadius}
                      onChange={(e) => updateFormData('travelRadius', e.target.value)}
                      placeholder="e.g., 25"
                      className="text-vierla-text placeholder:text-[#F5F5DC] bg-white/10 border-white/20"
                    />
                  </div>
                </div>
              )}

              {/* Step 4: Business Information */}
              {currentStep === 3 && (
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="insurance"
                      checked={formData.insurance}
                      onCheckedChange={(checked) => updateFormData('insurance', checked)}
                    />
                    <Label htmlFor="insurance" className="text-vierla-text drop-shadow-sm">I have professional liability insurance</Label>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="license" className="text-vierla-text drop-shadow-sm">License/Certification Numbers</Label>
                    <Input
                      id="license"
                      value={formData.license}
                      onChange={(e) => updateFormData('license', e.target.value)}
                      placeholder="Enter relevant license numbers"
                      className="text-vierla-text placeholder:text-[#F5F5DC] bg-white/10 border-white/20"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="portfolio" className="text-vierla-text drop-shadow-sm">Portfolio/Website URL</Label>
                    <Input
                      id="portfolio"
                      value={formData.portfolio}
                      onChange={(e) => updateFormData('portfolio', e.target.value)}
                      placeholder="https://your-portfolio.com"
                      className="text-vierla-text placeholder:text-[#F5F5DC] bg-white/10 border-white/20"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="rates" className="text-vierla-text drop-shadow-sm">Starting Rates</Label>
                    <Textarea
                      id="rates"
                      value={formData.rates}
                      onChange={(e) => updateFormData('rates', e.target.value)}
                      placeholder="Describe your pricing structure"
                      className="text-vierla-text placeholder:text-[#F5F5DC] bg-white/10 border-white/20"
                      rows={3}
                    />
                  </div>
                </div>
              )}

              {/* Step 5: Additional Details */}
              {currentStep === 4 && (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="motivation" className="text-vierla-text drop-shadow-sm">Why do you want to join Vierla?</Label>
                    <Textarea
                      id="motivation"
                      value={formData.motivation}
                      onChange={(e) => updateFormData('motivation', e.target.value)}
                      placeholder="Tell us about your motivation and goals"
                      className="text-vierla-text placeholder:text-[#F5F5DC] bg-white/10 border-white/20"
                      rows={4}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="references" className="text-vierla-text drop-shadow-sm">References (Optional)</Label>
                    <Textarea
                      id="references"
                      value={formData.references}
                      onChange={(e) => updateFormData('references', e.target.value)}
                      placeholder="Professional references or client testimonials"
                      className="text-vierla-text placeholder:text-[#F5F5DC] bg-white/10 border-white/20"
                      rows={3}
                    />
                  </div>
                </div>
              )}

              {/* Step 6: Review & Submit */}
              {currentStep === 5 && (
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-vierla-text drop-shadow-lg">Review Your Application</h3>
                  <div className="space-y-2 text-sm text-vierla-text/90 drop-shadow-sm">
                    <p><strong>Name:</strong> {formData.firstName} {formData.lastName}</p>
                    <p><strong>Email:</strong> {formData.email}</p>
                    <p><strong>Phone:</strong> {formData.phone}</p>
                    <p><strong>Business:</strong> {formData.businessName || 'Not specified'}</p>
                    <p><strong>Services:</strong> {formData.services.join(', ') || 'None selected'}</p>
                    <p><strong>Experience:</strong> {formData.experience || 'Not specified'}</p>
                    <p><strong>Service Areas:</strong> {formData.serviceAreas.join(', ') || 'None selected'}</p>
                    <p><strong>Insurance:</strong> {formData.insurance ? 'Yes' : 'No'}</p>
                  </div>
                  <div className="bg-white/10 p-4 rounded-lg border border-white/20">
                    <p className="text-sm text-vierla-text/70 drop-shadow-sm">
                      By submitting this application, you agree to our Terms of Service and Privacy Policy.
                      We'll review your application and contact you within 2-3 business days.
                    </p>
                  </div>
                </div>
              )}
            </motion.div>
          </AnimatePresence>

          <div className="flex justify-between pt-6 pb-4">
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <button
                type="button"
                onClick={prev}
                disabled={currentStep === 0}
                className="px-8 py-3 text-lg font-medium rounded-2xl bg-transparent border-2 border-[var(--master-brand-primary-light)] dark:border-[var(--master-brand-primary-dark)] text-[var(--master-brand-primary-light)] dark:text-[var(--master-brand-primary-dark)] hover:bg-[var(--master-brand-primary-light)] hover:dark:bg-[var(--master-brand-primary-dark)] hover:text-[var(--master-text-primary-light)] hover:dark:text-[var(--master-text-primary-dark)] transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Back
              </button>
            </motion.div>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {currentStep === steps.length - 1 ? (
                <ShimmerButton
                  type="button"
                  size="lg"
                  background="#B8956A"
                  shimmerColor="#E5D4A1"
                  className="px-8 py-3 text-lg font-medium text-[#2D2A26] rounded-2xl"
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                >
                  {isSubmitting ? "Submitting..." : "Submit Application"}
                </ShimmerButton>
              ) : (
                <ShinyButton
                  type="button"
                  onClick={next}
                  disabled={!isStepValid()}
                  size="lg"
                  className="px-8 py-3 text-lg font-medium rounded-2xl"
                >
                  Next
                </ShinyButton>
              )}
            </motion.div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
