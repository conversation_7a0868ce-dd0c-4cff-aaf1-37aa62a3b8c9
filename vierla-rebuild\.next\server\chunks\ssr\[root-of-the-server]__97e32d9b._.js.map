{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/jost_cd42f72b.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"jost_cd42f72b-module__Wo-Ogq__className\",\n  \"variable\": \"jost_cd42f72b-module__Wo-Ogq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/jost_cd42f72b.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Jost%22,%22arguments%22:[{%22variable%22:%22--font-jost%22,%22subsets%22:[%22latin%22],%22weight%22:[%22400%22,%22500%22,%22600%22,%22700%22],%22display%22:%22swap%22}],%22variableName%22:%22jost%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Jost', 'Jost Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,oJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,oJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,oJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_3c883839.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_3c883839-module__u_8pEG__className\",\n  \"variable\": \"inter_3c883839-module__u_8pEG__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_3c883839.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22variable%22:%22--font-inter%22,%22subsets%22:[%22latin%22],%22weight%22:[%22400%22,%22500%22,%22600%22,%22700%22],%22display%22:%22swap%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/playfair_display_70aae4f1.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"playfair_display_70aae4f1-module__fh3YGq__className\",\n  \"variable\": \"playfair_display_70aae4f1-module__fh3YGq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/playfair_display_70aae4f1.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Playfair_Display%22,%22arguments%22:[{%22variable%22:%22--font-tai-heritage%22,%22subsets%22:[%22latin%22],%22weight%22:[%22400%22,%22500%22,%22600%22,%22700%22],%22display%22:%22swap%22}],%22variableName%22:%22taiHeritage%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Playfair Display', 'Playfair Display Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,gKAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,gKAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,gKAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/dancing_script_13037ae7.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"dancing_script_13037ae7-module__EJGhma__className\",\n  \"variable\": \"dancing_script_13037ae7-module__EJGhma__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/dancing_script_13037ae7.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Dancing_Script%22,%22arguments%22:[{%22variable%22:%22--font-farsan%22,%22subsets%22:[%22latin%22],%22weight%22:[%22400%22,%22500%22,%22600%22,%22700%22],%22display%22:%22swap%22}],%22variableName%22:%22farsan%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Dancing Script', 'Dancing Script Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,8JAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,8JAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,8JAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/marketing/navbar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Navbar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Navbar() from the server but Navbar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/marketing/navbar.tsx <module evaluation>\",\n    \"Navbar\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,iEACA", "debugId": null}}, {"offset": {"line": 125, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/marketing/navbar.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Navbar = registerClientReference(\n    function() { throw new Error(\"Attempted to call Navbar() from the server but Navbar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/marketing/navbar.tsx\",\n    \"Navbar\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,6CACA", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/marketing/footer.tsx"], "sourcesContent": ["import React from \"react\";\nimport Link from \"next/link\";\n\nexport const Footer = React.memo(function Footer() {\n  return (\n    <footer className=\"bg-[var(--header-footer-bg)] border-[var(--header-footer-border)] border-t py-8 sm:py-12\" style={{\n      backdropFilter: 'var(--header-footer-backdrop-blur)',\n      WebkitBackdropFilter: 'var(--header-footer-backdrop-blur)'\n    }}>\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Mobile: Single column, Tablet: 2 columns, Desktop: 4 columns */}\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-8\">\n          {/* Brand Section - Full width on mobile */}\n          <div className=\"sm:col-span-2 lg:col-span-1\">\n            <h2 className=\"text-xl sm:text-2xl font-bold mb-3 sm:mb-4 drop-shadow-lg font-serif\" style={{\n              color: 'var(--text-primary)',\n              fontFamily: 'Playfair Display, serif'\n            }}>Vierla</h2>\n            <p className=\"text-sm sm:text-base drop-shadow-sm font-sans max-w-xs\" style={{ color: 'var(--text-secondary)' }}>\n              Ontario's Premier Beauty Marketplace connecting you with verified professionals.\n            </p>\n          </div>\n\n          {/* Product Links */}\n          <div>\n            <h3 className=\"font-semibold mb-3 sm:mb-4 drop-shadow-lg font-tai-heritage text-base sm:text-lg\" style={{ color: 'var(--text-primary)' }}>Product</h3>\n            <ul className=\"space-y-2 text-sm sm:text-base\">\n              <li><Link href=\"/features\" className=\"drop-shadow-sm font-sans transition-colors hover:opacity-80\" style={{ color: 'var(--text-secondary)' }}>Features</Link></li>\n              <li><Link href=\"/pricing\" className=\"drop-shadow-sm font-sans transition-colors hover:opacity-80\" style={{ color: 'var(--text-secondary)' }}>Pricing</Link></li>\n              <li><Link href=\"/providers\" className=\"drop-shadow-sm font-sans transition-colors hover:opacity-80\" style={{ color: 'var(--text-secondary)' }}>For Providers</Link></li>\n              <li><Link href=\"/apply\" className=\"drop-shadow-sm font-sans transition-colors hover:opacity-80\" style={{ color: 'var(--text-secondary)' }}>Apply</Link></li>\n            </ul>\n          </div>\n\n          {/* Company Links */}\n          <div>\n            <h3 className=\"font-semibold mb-3 sm:mb-4 drop-shadow-lg font-tai-heritage text-base sm:text-lg\" style={{ color: 'var(--text-primary)' }}>Company</h3>\n            <ul className=\"space-y-2 text-sm sm:text-base\">\n              <li><Link href=\"/about\" className=\"drop-shadow-sm font-sans transition-colors hover:opacity-80\" style={{ color: 'var(--text-secondary)' }}>About Us</Link></li>\n              <li><Link href=\"/contact\" className=\"drop-shadow-sm font-sans transition-colors hover:opacity-80\" style={{ color: 'var(--text-secondary)' }}>Contact Us</Link></li>\n            </ul>\n          </div>\n\n          {/* Legal Links */}\n          <div>\n            <h3 className=\"font-semibold mb-3 sm:mb-4 drop-shadow-lg font-tai-heritage text-base sm:text-lg\" style={{ color: 'var(--text-primary)' }}>Legal</h3>\n            <ul className=\"space-y-2 text-sm sm:text-base\">\n              <li><Link href=\"/privacy\" className=\"drop-shadow-sm font-sans transition-colors hover:opacity-80\" style={{ color: 'var(--text-secondary)' }}>Privacy Policy</Link></li>\n              <li><Link href=\"/terms\" className=\"drop-shadow-sm font-sans transition-colors hover:opacity-80\" style={{ color: 'var(--text-secondary)' }}>Terms of Service</Link></li>\n            </ul>\n          </div>\n        </div>\n\n        {/* Copyright Section */}\n        <div className=\"mt-8 sm:mt-10 border-t pt-6 sm:pt-8 text-center text-xs sm:text-sm drop-shadow-sm\" style={{\n          borderColor: 'var(--border-subtle)',\n          color: 'var(--text-muted)'\n        }}>\n          <p className=\"font-sans\">© {new Date().getFullYear()} Vierla, Inc. All rights reserved.</p>\n        </div>\n      </div>\n    </footer>\n  );\n});\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEO,MAAM,uBAAS,qMAAA,CAAA,UAAK,CAAC,IAAI,CAAC,SAAS;IACxC,qBACE,8OAAC;QAAO,WAAU;QAA2F,OAAO;YAClH,gBAAgB;YAChB,sBAAsB;QACxB;kBACE,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;oCAAuE,OAAO;wCAC1F,OAAO;wCACP,YAAY;oCACd;8CAAG;;;;;;8CACH,8OAAC;oCAAE,WAAU;oCAAyD,OAAO;wCAAE,OAAO;oCAAwB;8CAAG;;;;;;;;;;;;sCAMnH,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;oCAAmF,OAAO;wCAAE,OAAO;oCAAsB;8CAAG;;;;;;8CAC1I,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAY,WAAU;gDAA8D,OAAO;oDAAE,OAAO;gDAAwB;0DAAG;;;;;;;;;;;sDAC9I,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;gDAA8D,OAAO;oDAAE,OAAO;gDAAwB;0DAAG;;;;;;;;;;;sDAC7I,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAa,WAAU;gDAA8D,OAAO;oDAAE,OAAO;gDAAwB;0DAAG;;;;;;;;;;;sDAC/I,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;gDAA8D,OAAO;oDAAE,OAAO;gDAAwB;0DAAG;;;;;;;;;;;;;;;;;;;;;;;sCAK/I,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;oCAAmF,OAAO;wCAAE,OAAO;oCAAsB;8CAAG;;;;;;8CAC1I,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;gDAA8D,OAAO;oDAAE,OAAO;gDAAwB;0DAAG;;;;;;;;;;;sDAC3I,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;gDAA8D,OAAO;oDAAE,OAAO;gDAAwB;0DAAG;;;;;;;;;;;;;;;;;;;;;;;sCAKjJ,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;oCAAmF,OAAO;wCAAE,OAAO;oCAAsB;8CAAG;;;;;;8CAC1I,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;gDAA8D,OAAO;oDAAE,OAAO;gDAAwB;0DAAG;;;;;;;;;;;sDAC7I,8OAAC;sDAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;gDAA8D,OAAO;oDAAE,OAAO;gDAAwB;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAMjJ,8OAAC;oBAAI,WAAU;oBAAoF,OAAO;wBACxG,aAAa;wBACb,OAAO;oBACT;8BACE,cAAA,8OAAC;wBAAE,WAAU;;4BAAY;4BAAG,IAAI,OAAO,WAAW;4BAAG;;;;;;;;;;;;;;;;;;;;;;;AAK/D", "debugId": null}}, {"offset": {"line": 472, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/toast.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ToastProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ToastProvider() from the server but ToastProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/toast.tsx <module evaluation>\",\n    \"ToastProvider\",\n);\nexport const useToast = registerClientReference(\n    function() { throw new Error(\"Attempted to call useToast() from the server but useToast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/toast.tsx <module evaluation>\",\n    \"useToast\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yDACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,yDACA", "debugId": null}}, {"offset": {"line": 488, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/toast.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ToastProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ToastProvider() from the server but ToastProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/toast.tsx\",\n    \"ToastProvider\",\n);\nexport const useToast = registerClientReference(\n    function() { throw new Error(\"Attempted to call useToast() from the server but useToast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/toast.tsx\",\n    \"useToast\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,qCACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,qCACA", "debugId": null}}, {"offset": {"line": 504, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 512, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/cookie-consent.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const CookieConsent = registerClientReference(\n    function() { throw new Error(\"Attempted to call CookieConsent() from the server but <PERSON>ieConsent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/cookie-consent.tsx <module evaluation>\",\n    \"CookieConsent\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,kEACA", "debugId": null}}, {"offset": {"line": 524, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/cookie-consent.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const CookieConsent = registerClientReference(\n    function() { throw new Error(\"Attempted to call CookieConsent() from the server but <PERSON>ieConsent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/cookie-consent.tsx\",\n    \"CookieConsent\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,8CACA", "debugId": null}}, {"offset": {"line": 536, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 544, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/providers/theme-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/providers/theme-provider.tsx <module evaluation>\",\n    \"ThemeProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yEACA", "debugId": null}}, {"offset": {"line": 556, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/providers/theme-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/providers/theme-provider.tsx\",\n    \"ThemeProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,qDACA", "debugId": null}}, {"offset": {"line": 568, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 576, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from \"next\";\nimport { <PERSON>, Jo<PERSON>, Play<PERSON>_Display, Dancing_Script } from \"next/font/google\";\nimport \"./globals.css\";\nimport { Navbar } from \"@/components/marketing/navbar\";\nimport { Footer } from \"@/components/marketing/footer\";\nimport { ToastProvider } from \"@/components/ui/toast\";\nimport { CookieConsent } from \"@/components/ui/cookie-consent\";\nimport { ThemeProvider } from \"@/components/providers/theme-provider\";\n\n// Jost for headlines (H1, H2) - Clean, geometric sans-serif for impact\nconst jost = Jost({\n  variable: \"--font-jost\",\n  subsets: [\"latin\"],\n  weight: [\"400\", \"500\", \"600\", \"700\"],\n  display: \"swap\",\n});\n\n// Inter for body text and UI elements - Optimized for screen readability\nconst inter = Inter({\n  variable: \"--font-inter\",\n  subsets: [\"latin\"],\n  weight: [\"400\", \"500\", \"600\", \"700\"],\n  display: \"swap\",\n});\n\n// Tai Heritage Pro alternative - Playfair Display for H2/H3 section titles (elegant serif)\nconst taiHeritage = Playfair_Display({\n  variable: \"--font-tai-heritage\",\n  subsets: [\"latin\"],\n  weight: [\"400\", \"500\", \"600\", \"700\"],\n  display: \"swap\",\n});\n\n// Farsan alternative - Dancing Script for optional accent/signature text (script-like)\nconst farsan = Dancing_Script({\n  variable: \"--font-farsan\",\n  subsets: [\"latin\"],\n  weight: [\"400\", \"500\", \"600\", \"700\"],\n  display: \"swap\",\n});\n\nexport const metadata: Metadata = {\n  title: \"Vierla - The AI-Powered Platform for Modern Business\",\n  description: \"Build, manage, and grow your business with our all-in-one platform featuring AI website builder, smart invoicing, integrated CRM, and actionable analytics.\",\n  icons: {\n    icon: '/favicon.ico',\n    shortcut: '/favicon.ico',\n    apple: '/favicon.ico',\n  },\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" suppressHydrationWarning>\n      <body\n        className={`${jost.variable} ${inter.variable} ${taiHeritage.variable} ${farsan.variable} antialiased\n                   bg-[var(--aurora-bg)] text-light-off-white transition-colors duration-300`}\n      >\n        <ThemeProvider\n          attribute=\"class\"\n          defaultTheme=\"system\"\n          enableSystem\n          disableTransitionOnChange={false}\n        >\n          <ToastProvider>\n            <Navbar />\n            <main className=\"min-h-screen pt-16\">\n              {children}\n            </main>\n            <Footer />\n            <CookieConsent />\n          </ToastProvider>\n        </ThemeProvider>\n      </body>\n    </html>\n  );\n}"], "names": [], "mappings": ";;;;;;;;;AAGA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AAkCO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,OAAO;QACL,MAAM;QACN,UAAU;QACV,OAAO;IACT;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,wBAAwB;kBACtC,cAAA,8OAAC;YACC,WAAW,GAAG,wIAAA,CAAA,UAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,oJAAA,CAAA,UAAW,CAAC,QAAQ,CAAC,CAAC,EAAE,kJAAA,CAAA,UAAM,CAAC,QAAQ,CAAC;4FACL,CAAC;sBAErF,cAAA,8OAAC,6IAAA,CAAA,gBAAa;gBAC<PERSON>,WAAU;gBACV,cAAa;gBACb,YAAY;gBACZ,2BAA2B;0BAE3B,cAAA,8OAAC,0HAAA,CAAA,gBAAa;;sCACZ,8OAAC,kIAAA,CAAA,SAAM;;;;;sCACP,8OAAC;4BAAK,WAAU;sCACb;;;;;;sCAEH,8OAAC,kIAAA,CAAA,SAAM;;;;;sCACP,8OAAC,sIAAA,CAAA,gBAAa;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1B", "debugId": null}}, {"offset": {"line": 675, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 681, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/node_modules/next/dist/client/app-dir/link.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/client/app-dir/link.js <module evaluation>\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 688, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/node_modules/next/dist/client/app-dir/link.js/proxy.cjs"], "sourcesContent": ["const { createClientModuleProxy } = require(\"react-server-dom-turbopack/server\");\n\n__turbopack_context__.n(createClientModuleProxy(\"[project]/node_modules/next/dist/client/app-dir/link.js\"));\n"], "names": [], "mappings": "AAAA,MAAM,EAAE,uBAAuB,EAAE;AAEjC,sBAAsB,CAAC,CAAC,wBAAwB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 694, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/node_modules/next/src/client/app-dir/link.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useOptimistic, useRef } from 'react'\nimport type { UrlObject } from 'url'\nimport { formatUrl } from '../../shared/lib/router/utils/format-url'\nimport { AppRouterContext } from '../../shared/lib/app-router-context.shared-runtime'\nimport { PrefetchKind } from '../components/router-reducer/router-reducer-types'\nimport { useMergedRef } from '../use-merged-ref'\nimport { isAbsoluteUrl } from '../../shared/lib/utils'\nimport { addBasePath } from '../add-base-path'\nimport { warnOnce } from '../../shared/lib/utils/warn-once'\nimport type { PENDING_LINK_STATUS } from '../components/links'\nimport {\n  IDLE_LINK_STATUS,\n  mountLinkInstance,\n  onNavigationIntent,\n  unmountLinkForCurrentNavigation,\n  unmountPrefetchableInstance,\n  type LinkInstance,\n} from '../components/links'\nimport { isLocalURL } from '../../shared/lib/router/utils/is-local-url'\nimport { dispatchNavigateAction } from '../components/app-router-instance'\nimport { errorOnce } from '../../shared/lib/utils/error-once'\n\ntype Url = string | UrlObject\ntype RequiredKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? never : K\n}[keyof T]\ntype OptionalKeys<T> = {\n  [K in keyof T]-?: {} extends Pick<T, K> ? K : never\n}[keyof T]\n\ntype OnNavigateEventHandler = (event: { preventDefault: () => void }) => void\n\ntype InternalLinkProps = {\n  /**\n   * **Required**. The path or URL to navigate to. It can also be an object (similar to `URL`).\n   *\n   * @example\n   * ```tsx\n   * // Navigate to /dashboard:\n   * <Link href=\"/dashboard\">Dashboard</Link>\n   *\n   * // Navigate to /about?name=test:\n   * <Link href={{ pathname: '/about', query: { name: 'test' } }}>\n   *   About\n   * </Link>\n   * ```\n   *\n   * @remarks\n   * - For external URLs, use a fully qualified URL such as `https://...`.\n   * - In the App Router, dynamic routes must not include bracketed segments in `href`.\n   */\n  href: Url\n\n  /**\n   * @deprecated v10.0.0: `href` props pointing to a dynamic route are\n   * automatically resolved and no longer require the `as` prop.\n   */\n  as?: Url\n\n  /**\n   * Replace the current `history` state instead of adding a new URL into the stack.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/about\" replace>\n   *   About (replaces the history state)\n   * </Link>\n   * ```\n   */\n  replace?: boolean\n\n  /**\n   * Whether to override the default scroll behavior. If `true`, Next.js attempts to maintain\n   * the scroll position if the newly navigated page is still visible. If not, it scrolls to the top.\n   *\n   * If `false`, Next.js will not modify the scroll behavior at all.\n   *\n   * @defaultValue `true`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" scroll={false}>\n   *   No auto scroll\n   * </Link>\n   * ```\n   */\n  scroll?: boolean\n\n  /**\n   * Update the path of the current page without rerunning data fetching methods\n   * like `getStaticProps`, `getServerSideProps`, or `getInitialProps`.\n   *\n   * @remarks\n   * `shallow` only applies to the Pages Router. For the App Router, see the\n   * [following documentation](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#using-the-native-history-api).\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/blog\" shallow>\n   *   Shallow navigation\n   * </Link>\n   * ```\n   */\n  shallow?: boolean\n\n  /**\n   * Forces `Link` to pass its `href` to the child component. Useful if the child is a custom\n   * component that wraps an `<a>` tag, or if you're using certain styling libraries.\n   *\n   * @defaultValue `false`\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" passHref>\n   *   <MyStyledAnchor>Dashboard</MyStyledAnchor>\n   * </Link>\n   * ```\n   */\n  passHref?: boolean\n\n  /**\n   * Prefetch the page in the background.\n   * Any `<Link />` that is in the viewport (initially or through scroll) will be prefetched.\n   * Prefetch can be disabled by passing `prefetch={false}`.\n   *\n   * @remarks\n   * Prefetching is only enabled in production.\n   *\n   * - In the **App Router**:\n   *   - `\"auto\"`, `null`, `undefined` (default): Prefetch behavior depends on static vs dynamic routes:\n   *     - Static routes: fully prefetched\n   *     - Dynamic routes: partial prefetch to the nearest segment with a `loading.js`\n   *   - `true`: Always prefetch the full route and data.\n   *   - `false`: Disable prefetching on both viewport and hover.\n   * - In the **Pages Router**:\n   *   - `true` (default): Prefetches the route and data in the background on viewport or hover.\n   *   - `false`: Prefetch only on hover, not on viewport.\n   *\n   * @defaultValue `true` (Pages Router) or `null` (App Router)\n   *\n   * @example\n   * ```tsx\n   * <Link href=\"/dashboard\" prefetch={false}>\n   *   Dashboard\n   * </Link>\n   * ```\n   */\n  prefetch?: boolean | 'auto' | null\n\n  /**\n   * (unstable) Switch to a dynamic prefetch on hover. Effectively the same as\n   * updating the prefetch prop to `true` in a mouse event.\n   */\n  unstable_dynamicOnHover?: boolean\n\n  /**\n   * The active locale is automatically prepended in the Pages Router. `locale` allows for providing\n   * a different locale, or can be set to `false` to opt out of automatic locale behavior.\n   *\n   * @remarks\n   * Note: locale only applies in the Pages Router and is ignored in the App Router.\n   *\n   * @example\n   * ```tsx\n   * // Use the 'fr' locale:\n   * <Link href=\"/about\" locale=\"fr\">\n   *   About (French)\n   * </Link>\n   *\n   * // Disable locale prefix:\n   * <Link href=\"/about\" locale={false}>\n   *   About (no locale prefix)\n   * </Link>\n   * ```\n   */\n  locale?: string | false\n\n  /**\n   * Enable legacy link behavior, requiring an `<a>` tag to wrap the child content\n   * if the child is a string or number.\n   *\n   * @deprecated This will be removed in v16\n   * @defaultValue `false`\n   * @see https://github.com/vercel/next.js/commit/489e65ed98544e69b0afd7e0cfc3f9f6c2b803b7\n   */\n  legacyBehavior?: boolean\n\n  /**\n   * Optional event handler for when the mouse pointer is moved onto the `<Link>`.\n   */\n  onMouseEnter?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is touched.\n   */\n  onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is clicked.\n   */\n  onClick?: React.MouseEventHandler<HTMLAnchorElement>\n\n  /**\n   * Optional event handler for when the `<Link>` is navigated.\n   */\n  onNavigate?: OnNavigateEventHandler\n}\n\n// TODO-APP: Include the full set of Anchor props\n// adding this to the publicly exported type currently breaks existing apps\n\n// `RouteInferType` is a stub here to avoid breaking `typedRoutes` when the type\n// isn't generated yet. It will be replaced when the webpack plugin runs.\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport type LinkProps<RouteInferType = any> = InternalLinkProps\ntype LinkPropsRequired = RequiredKeys<LinkProps>\ntype LinkPropsOptional = OptionalKeys<Omit<InternalLinkProps, 'locale'>>\n\nfunction isModifiedEvent(event: React.MouseEvent): boolean {\n  const eventTarget = event.currentTarget as HTMLAnchorElement | SVGAElement\n  const target = eventTarget.getAttribute('target')\n  return (\n    (target && target !== '_self') ||\n    event.metaKey ||\n    event.ctrlKey ||\n    event.shiftKey ||\n    event.altKey || // triggers resource download\n    (event.nativeEvent && event.nativeEvent.which === 2)\n  )\n}\n\nfunction linkClicked(\n  e: React.MouseEvent,\n  href: string,\n  as: string,\n  linkInstanceRef: React.RefObject<LinkInstance | null>,\n  replace?: boolean,\n  scroll?: boolean,\n  onNavigate?: OnNavigateEventHandler\n): void {\n  const { nodeName } = e.currentTarget\n\n  // anchors inside an svg have a lowercase nodeName\n  const isAnchorNodeName = nodeName.toUpperCase() === 'A'\n\n  if (\n    (isAnchorNodeName && isModifiedEvent(e)) ||\n    e.currentTarget.hasAttribute('download')\n  ) {\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  if (!isLocalURL(href)) {\n    if (replace) {\n      // browser default behavior does not replace the history state\n      // so we need to do it manually\n      e.preventDefault()\n      location.replace(href)\n    }\n\n    // ignore click for browser’s default behavior\n    return\n  }\n\n  e.preventDefault()\n\n  if (onNavigate) {\n    let isDefaultPrevented = false\n\n    onNavigate({\n      preventDefault: () => {\n        isDefaultPrevented = true\n      },\n    })\n\n    if (isDefaultPrevented) {\n      return\n    }\n  }\n\n  React.startTransition(() => {\n    dispatchNavigateAction(\n      as || href,\n      replace ? 'replace' : 'push',\n      scroll ?? true,\n      linkInstanceRef.current\n    )\n  })\n}\n\nfunction formatStringOrUrl(urlObjOrString: UrlObject | string): string {\n  if (typeof urlObjOrString === 'string') {\n    return urlObjOrString\n  }\n\n  return formatUrl(urlObjOrString)\n}\n\n/**\n * A React component that extends the HTML `<a>` element to provide\n * [prefetching](https://nextjs.org/docs/app/building-your-application/routing/linking-and-navigating#2-prefetching)\n * and client-side navigation. This is the primary way to navigate between routes in Next.js.\n *\n * @remarks\n * - Prefetching is only enabled in production.\n *\n * @see https://nextjs.org/docs/app/api-reference/components/link\n */\nexport default function LinkComponent(\n  props: LinkProps & {\n    children: React.ReactNode\n    ref: React.Ref<HTMLAnchorElement>\n  }\n) {\n  const [linkStatus, setOptimisticLinkStatus] = useOptimistic(IDLE_LINK_STATUS)\n\n  let children: React.ReactNode\n\n  const linkInstanceRef = useRef<LinkInstance | null>(null)\n\n  const {\n    href: hrefProp,\n    as: asProp,\n    children: childrenProp,\n    prefetch: prefetchProp = null,\n    passHref,\n    replace,\n    shallow,\n    scroll,\n    onClick,\n    onMouseEnter: onMouseEnterProp,\n    onTouchStart: onTouchStartProp,\n    legacyBehavior = false,\n    onNavigate,\n    ref: forwardedRef,\n    unstable_dynamicOnHover,\n    ...restProps\n  } = props\n\n  children = childrenProp\n\n  if (\n    legacyBehavior &&\n    (typeof children === 'string' || typeof children === 'number')\n  ) {\n    children = <a>{children}</a>\n  }\n\n  const router = React.useContext(AppRouterContext)\n\n  const prefetchEnabled = prefetchProp !== false\n  /**\n   * The possible states for prefetch are:\n   * - null: this is the default \"auto\" mode, where we will prefetch partially if the link is in the viewport\n   * - true: we will prefetch if the link is visible and prefetch the full page, not just partially\n   * - false: we will not prefetch if in the viewport at all\n   * - 'unstable_dynamicOnHover': this starts in \"auto\" mode, but switches to \"full\" when the link is hovered\n   */\n  const appPrefetchKind =\n    prefetchProp === null || prefetchProp === 'auto'\n      ? PrefetchKind.AUTO\n      : PrefetchKind.FULL\n\n  if (process.env.NODE_ENV !== 'production') {\n    function createPropError(args: {\n      key: string\n      expected: string\n      actual: string\n    }) {\n      return new Error(\n        `Failed prop type: The prop \\`${args.key}\\` expects a ${args.expected} in \\`<Link>\\`, but got \\`${args.actual}\\` instead.` +\n          (typeof window !== 'undefined'\n            ? \"\\nOpen your browser's console to view the Component stack trace.\"\n            : '')\n      )\n    }\n\n    // TypeScript trick for type-guarding:\n    const requiredPropsGuard: Record<LinkPropsRequired, true> = {\n      href: true,\n    } as const\n    const requiredProps: LinkPropsRequired[] = Object.keys(\n      requiredPropsGuard\n    ) as LinkPropsRequired[]\n    requiredProps.forEach((key: LinkPropsRequired) => {\n      if (key === 'href') {\n        if (\n          props[key] == null ||\n          (typeof props[key] !== 'string' && typeof props[key] !== 'object')\n        ) {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: props[key] === null ? 'null' : typeof props[key],\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n\n    // TypeScript trick for type-guarding:\n    const optionalPropsGuard: Record<LinkPropsOptional, true> = {\n      as: true,\n      replace: true,\n      scroll: true,\n      shallow: true,\n      passHref: true,\n      prefetch: true,\n      unstable_dynamicOnHover: true,\n      onClick: true,\n      onMouseEnter: true,\n      onTouchStart: true,\n      legacyBehavior: true,\n      onNavigate: true,\n    } as const\n    const optionalProps: LinkPropsOptional[] = Object.keys(\n      optionalPropsGuard\n    ) as LinkPropsOptional[]\n    optionalProps.forEach((key: LinkPropsOptional) => {\n      const valType = typeof props[key]\n\n      if (key === 'as') {\n        if (props[key] && valType !== 'string' && valType !== 'object') {\n          throw createPropError({\n            key,\n            expected: '`string` or `object`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'onClick' ||\n        key === 'onMouseEnter' ||\n        key === 'onTouchStart' ||\n        key === 'onNavigate'\n      ) {\n        if (props[key] && valType !== 'function') {\n          throw createPropError({\n            key,\n            expected: '`function`',\n            actual: valType,\n          })\n        }\n      } else if (\n        key === 'replace' ||\n        key === 'scroll' ||\n        key === 'shallow' ||\n        key === 'passHref' ||\n        key === 'legacyBehavior' ||\n        key === 'unstable_dynamicOnHover'\n      ) {\n        if (props[key] != null && valType !== 'boolean') {\n          throw createPropError({\n            key,\n            expected: '`boolean`',\n            actual: valType,\n          })\n        }\n      } else if (key === 'prefetch') {\n        if (\n          props[key] != null &&\n          valType !== 'boolean' &&\n          props[key] !== 'auto'\n        ) {\n          throw createPropError({\n            key,\n            expected: '`boolean | \"auto\"`',\n            actual: valType,\n          })\n        }\n      } else {\n        // TypeScript trick for type-guarding:\n        // eslint-disable-next-line @typescript-eslint/no-unused-vars\n        const _: never = key\n      }\n    })\n  }\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (props.locale) {\n      warnOnce(\n        'The `locale` prop is not supported in `next/link` while using the `app` router. Read more about app router internalization: https://nextjs.org/docs/app/building-your-application/routing/internationalization'\n      )\n    }\n    if (!asProp) {\n      let href: string | undefined\n      if (typeof hrefProp === 'string') {\n        href = hrefProp\n      } else if (\n        typeof hrefProp === 'object' &&\n        typeof hrefProp.pathname === 'string'\n      ) {\n        href = hrefProp.pathname\n      }\n\n      if (href) {\n        const hasDynamicSegment = href\n          .split('/')\n          .some((segment) => segment.startsWith('[') && segment.endsWith(']'))\n\n        if (hasDynamicSegment) {\n          throw new Error(\n            `Dynamic href \\`${href}\\` found in <Link> while using the \\`/app\\` router, this is not supported. Read more: https://nextjs.org/docs/messages/app-dir-dynamic-href`\n          )\n        }\n      }\n    }\n  }\n\n  const { href, as } = React.useMemo(() => {\n    const resolvedHref = formatStringOrUrl(hrefProp)\n    return {\n      href: resolvedHref,\n      as: asProp ? formatStringOrUrl(asProp) : resolvedHref,\n    }\n  }, [hrefProp, asProp])\n\n  // This will return the first child, if multiple are provided it will throw an error\n  let child: any\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      if (onClick) {\n        console.warn(\n          `\"onClick\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onClick be set on the child of next/link`\n        )\n      }\n      if (onMouseEnterProp) {\n        console.warn(\n          `\"onMouseEnter\" was passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but \"legacyBehavior\" was set. The legacy behavior requires onMouseEnter be set on the child of next/link`\n        )\n      }\n      try {\n        child = React.Children.only(children)\n      } catch (err) {\n        if (!children) {\n          throw new Error(\n            `No children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but one child is required https://nextjs.org/docs/messages/link-no-children`\n          )\n        }\n        throw new Error(\n          `Multiple children were passed to <Link> with \\`href\\` of \\`${hrefProp}\\` but only one child is supported https://nextjs.org/docs/messages/link-multiple-children` +\n            (typeof window !== 'undefined'\n              ? \" \\nOpen your browser's console to view the Component stack trace.\"\n              : '')\n        )\n      }\n    } else {\n      child = React.Children.only(children)\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if ((children as any)?.type === 'a') {\n        throw new Error(\n          'Invalid <Link> with <a> child. Please remove <a> or use <Link legacyBehavior>.\\nLearn more: https://nextjs.org/docs/messages/invalid-new-link-with-extra-anchor'\n        )\n      }\n    }\n  }\n\n  const childRef: any = legacyBehavior\n    ? child && typeof child === 'object' && child.ref\n    : forwardedRef\n\n  // Use a callback ref to attach an IntersectionObserver to the anchor tag on\n  // mount. In the future we will also use this to keep track of all the\n  // currently mounted <Link> instances, e.g. so we can re-prefetch them after\n  // a revalidation or refresh.\n  const observeLinkVisibilityOnMount = React.useCallback(\n    (element: HTMLAnchorElement | SVGAElement) => {\n      if (router !== null) {\n        linkInstanceRef.current = mountLinkInstance(\n          element,\n          href,\n          router,\n          appPrefetchKind,\n          prefetchEnabled,\n          setOptimisticLinkStatus\n        )\n      }\n\n      return () => {\n        if (linkInstanceRef.current) {\n          unmountLinkForCurrentNavigation(linkInstanceRef.current)\n          linkInstanceRef.current = null\n        }\n        unmountPrefetchableInstance(element)\n      }\n    },\n    [prefetchEnabled, href, router, appPrefetchKind, setOptimisticLinkStatus]\n  )\n\n  const mergedRef = useMergedRef(observeLinkVisibilityOnMount, childRef)\n\n  const childProps: {\n    onTouchStart?: React.TouchEventHandler<HTMLAnchorElement>\n    onMouseEnter: React.MouseEventHandler<HTMLAnchorElement>\n    onClick: React.MouseEventHandler<HTMLAnchorElement>\n    href?: string\n    ref?: any\n  } = {\n    ref: mergedRef,\n    onClick(e) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (!e) {\n          throw new Error(\n            `Component rendered inside next/link has to pass click event to \"onClick\" prop.`\n          )\n        }\n      }\n\n      if (!legacyBehavior && typeof onClick === 'function') {\n        onClick(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onClick === 'function'\n      ) {\n        child.props.onClick(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (e.defaultPrevented) {\n        return\n      }\n\n      linkClicked(e, href, as, linkInstanceRef, replace, scroll, onNavigate)\n    },\n    onMouseEnter(e) {\n      if (!legacyBehavior && typeof onMouseEnterProp === 'function') {\n        onMouseEnterProp(e)\n      }\n\n      if (\n        legacyBehavior &&\n        child.props &&\n        typeof child.props.onMouseEnter === 'function'\n      ) {\n        child.props.onMouseEnter(e)\n      }\n\n      if (!router) {\n        return\n      }\n\n      if (!prefetchEnabled || process.env.NODE_ENV === 'development') {\n        return\n      }\n\n      const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n      onNavigationIntent(\n        e.currentTarget as HTMLAnchorElement | SVGAElement,\n        upgradeToDynamicPrefetch\n      )\n    },\n    onTouchStart: process.env.__NEXT_LINK_NO_TOUCH_START\n      ? undefined\n      : function onTouchStart(e) {\n          if (!legacyBehavior && typeof onTouchStartProp === 'function') {\n            onTouchStartProp(e)\n          }\n\n          if (\n            legacyBehavior &&\n            child.props &&\n            typeof child.props.onTouchStart === 'function'\n          ) {\n            child.props.onTouchStart(e)\n          }\n\n          if (!router) {\n            return\n          }\n\n          if (!prefetchEnabled) {\n            return\n          }\n\n          const upgradeToDynamicPrefetch = unstable_dynamicOnHover === true\n          onNavigationIntent(\n            e.currentTarget as HTMLAnchorElement | SVGAElement,\n            upgradeToDynamicPrefetch\n          )\n        },\n  }\n\n  // If child is an <a> tag and doesn't have a href attribute, or if the 'passHref' property is\n  // defined, we specify the current 'href', so that repetition is not needed by the user.\n  // If the url is absolute, we can bypass the logic to prepend the basePath.\n  if (isAbsoluteUrl(as)) {\n    childProps.href = as\n  } else if (\n    !legacyBehavior ||\n    passHref ||\n    (child.type === 'a' && !('href' in child.props))\n  ) {\n    childProps.href = addBasePath(as)\n  }\n\n  let link: React.ReactNode\n\n  if (legacyBehavior) {\n    if (process.env.NODE_ENV === 'development') {\n      errorOnce(\n        '`legacyBehavior` is deprecated and will be removed in a future ' +\n          'release. A codemod is available to upgrade your components:\\n\\n' +\n          'npx @next/codemod@latest new-link .\\n\\n' +\n          'Learn more: https://nextjs.org/docs/app/building-your-application/upgrading/codemods#remove-a-tags-from-link-components'\n      )\n    }\n    link = React.cloneElement(child, childProps)\n  } else {\n    link = (\n      <a {...restProps} {...childProps}>\n        {children}\n      </a>\n    )\n  }\n\n  return (\n    <LinkStatusContext.Provider value={linkStatus}>\n      {link}\n    </LinkStatusContext.Provider>\n  )\n}\n\nconst LinkStatusContext = createContext<\n  typeof PENDING_LINK_STATUS | typeof IDLE_LINK_STATUS\n>(IDLE_LINK_STATUS)\n\nexport const useLinkStatus = () => {\n  return useContext(LinkStatusContext)\n}\n"], "names": ["LinkComponent", "useLinkStatus", "isModifiedEvent", "event", "eventTarget", "currentTarget", "target", "getAttribute", "metaKey", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "nativeEvent", "which", "linkClicked", "e", "href", "as", "linkInstanceRef", "replace", "scroll", "onNavigate", "nodeName", "isAnchorNodeName", "toUpperCase", "hasAttribute", "isLocalURL", "preventDefault", "location", "isDefaultPrevented", "React", "startTransition", "dispatchNavigateAction", "current", "formatStringOrUrl", "urlObjOrString", "formatUrl", "props", "linkStatus", "setOptimisticLinkStatus", "useOptimistic", "IDLE_LINK_STATUS", "children", "useRef", "hrefProp", "asProp", "childrenProp", "prefetch", "prefetchProp", "passHref", "shallow", "onClick", "onMouseEnter", "onMouseEnterProp", "onTouchStart", "onTouchStartProp", "legacyBeh<PERSON>or", "ref", "forwardedRef", "unstable_dynamicOnHover", "restProps", "a", "router", "useContext", "AppRouterContext", "prefetchEnabled", "appPrefetchKind", "PrefetchKind", "AUTO", "FULL", "process", "env", "NODE_ENV", "createPropError", "args", "Error", "key", "expected", "actual", "window", "requiredPropsGuard", "requiredProps", "Object", "keys", "for<PERSON>ach", "_", "optionalPropsGuard", "optionalProps", "valType", "locale", "warnOnce", "pathname", "hasDynamicSegment", "split", "some", "segment", "startsWith", "endsWith", "useMemo", "resolvedHref", "child", "console", "warn", "Children", "only", "err", "type", "childRef", "observeLinkVisibilityOnMount", "useCallback", "element", "mountLinkInstance", "unmountLinkForCurrentNavigation", "unmountPrefetchableInstance", "mergedRef", "useMergedRef", "childProps", "defaultPrevented", "upgradeToDynamicPrefetch", "onNavigationIntent", "__NEXT_LINK_NO_TOUCH_START", "undefined", "isAbsoluteUrl", "addBasePath", "link", "errorOnce", "cloneElement", "LinkStatusContext", "Provider", "value", "createContext"], "mappings": "", "ignoreList": [0], "debugId": null}}]}