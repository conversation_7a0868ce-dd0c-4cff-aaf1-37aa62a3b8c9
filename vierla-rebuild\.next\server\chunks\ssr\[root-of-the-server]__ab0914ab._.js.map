{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AuroraBackground = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackground() from the server but AuroraBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx <module evaluation>\",\n    \"AuroraBackground\",\n);\nexport const AuroraBackgroundLayer = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackgroundLayer() from the server but AuroraBackgroundLayer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx <module evaluation>\",\n    \"AuroraBackgroundLayer\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,qEACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,qEACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/aurora-background.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const AuroraBackground = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackground() from the server but AuroraBackground is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx\",\n    \"AuroraBackground\",\n);\nexport const AuroraBackgroundLayer = registerClientReference(\n    function() { throw new Error(\"Attempted to call AuroraBackgroundLayer() from the server but AuroraBackgroundLayer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/aurora-background.tsx\",\n    \"AuroraBackgroundLayer\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,iDACA;AAEG,MAAM,wBAAwB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvD;IAAa,MAAM,IAAI,MAAM;AAA0P,GACvR,iDACA", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/bento-grid.tsx"], "sourcesContent": ["import { ReactNode } from \"react\";\nimport { ArrowRightIcon } from \"@radix-ui/react-icons\";\nimport Link from \"next/link\";\n\nimport { cn } from \"@/lib/utils\";\nimport { Button } from \"@/components/ui/button\";\n\nconst BentoGrid = ({\n  children,\n  className,\n}: {\n  children: ReactNode;\n  className?: string;\n}) => {\n  return (\n    <div\n      className={cn(\n        \"grid w-full auto-rows-[20rem] grid-cols-3 gap-4\",\n        // 16px gap as specified in design system\n        \"gap-4\", // 16px\n        className,\n      )}\n    >\n      {children}\n    </div>\n  );\n};\n\nconst BentoCard = ({\n  name,\n  className,\n  background,\n  Icon,\n  description,\n  services,\n  href,\n  cta,\n}: {\n  name: string;\n  className: string;\n  background?: ReactNode;\n  Icon: any;\n  description: string;\n  services?: string[];\n  href: string;\n  cta: string;\n}) => (\n  <Link href={href} className={cn(\n      \"group relative col-span-3 flex flex-col justify-between overflow-hidden rounded-xl\",\n      // Theme-aware background and borders\n      \"border shadow-xl transition-all duration-500 ease-out transform-gpu\",\n      // Smooth hover effects for desktop\n      \"hover:scale-[1.02] hover:shadow-2xl\",\n      // Mobile touch behavior - show content on first tap, navigate on second\n      \"mobile-bento-card\",\n      className,\n    )}\n    style={{\n      backgroundColor: 'var(--card-bg)',\n      borderColor: 'var(--border-subtle)',\n      boxShadow: 'var(--shadow-card)'\n    }}\n  >\n    {background && <div>{background}</div>}\n    <div className=\"pointer-events-none z-10 flex transform-gpu flex-col gap-2 p-4 transition-all duration-500\">\n      <Icon className=\"h-10 w-10 origin-left transform-gpu transition-all duration-500 ease-out group-hover:scale-110 mobile-bento-icon\"\n            style={{ color: 'var(--icon-accent)' }} />\n      <h3 className=\"text-lg font-semibold font-tai-heritage leading-tight transition-all duration-500 group-hover:opacity-0 group-hover:transform group-hover:-translate-y-2 mobile-bento-title\"\n          style={{ color: 'var(--text-primary)' }}>\n        {name}\n      </h3>\n      <p className=\"max-w-lg font-sans text-sm leading-relaxed opacity-0 transform translate-y-2 group-hover:opacity-100 group-hover:translate-y-0 transition-all duration-500 delay-100 mobile-bento-description\"\n         style={{ color: 'var(--text-secondary)' }}>{description}</p>\n    </div>\n\n    <div className=\"pointer-events-none absolute inset-0 transform-gpu transition-all duration-500 opacity-0 group-hover:opacity-100 mobile-bento-overlay\"\n         style={{ backgroundColor: 'var(--accent-hover-overlay)' }} />\n  </Link>\n);\n\nexport { BentoCard, BentoGrid };\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;;AAGA,MAAM,YAAY,CAAC,EACjB,QAAQ,EACR,SAAS,EAIV;IACC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,mDACA,yCAAyC;QACzC,SACA;kBAGD;;;;;;AAGP;AAEA,MAAM,YAAY,CAAC,EACjB,IAAI,EACJ,SAAS,EACT,UAAU,EACV,IAAI,EACJ,WAAW,EACX,QAAQ,EACR,IAAI,EACJ,GAAG,EAUJ,iBACC,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAM;QAAM,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAC1B,sFACA,qCAAqC;QACrC,uEACA,mCAAmC;QACnC,uCACA,wEAAwE;QACxE,qBACA;QAEF,OAAO;YACL,iBAAiB;YACjB,aAAa;YACb,WAAW;QACb;;YAEC,4BAAc,8OAAC;0BAAK;;;;;;0BACrB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;wBACV,OAAO;4BAAE,OAAO;wBAAqB;;;;;;kCAC3C,8OAAC;wBAAG,WAAU;wBACV,OAAO;4BAAE,OAAO;wBAAsB;kCACvC;;;;;;kCAEH,8OAAC;wBAAE,WAAU;wBACV,OAAO;4BAAE,OAAO;wBAAwB;kCAAI;;;;;;;;;;;;0BAGjD,8OAAC;gBAAI,WAAU;gBACV,OAAO;oBAAE,iBAAiB;gBAA8B", "debugId": null}}, {"offset": {"line": 170, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/shimmer-button.tsx"], "sourcesContent": ["import React, { CSSProperties } from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nexport interface ShimmerButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  shimmerColor?: string;\n  shimmerSize?: string;\n  borderRadius?: string;\n  shimmerDuration?: string;\n  background?: string;\n  className?: string;\n  children?: React.ReactNode;\n  size?: \"sm\" | \"md\" | \"lg\";\n  variant?: \"primary\" | \"secondary\" | \"accent\";\n}\n\nconst ShimmerButton = React.forwardRef<HTMLButtonElement, ShimmerButtonProps>(\n  (\n    {\n      shimmerColor,\n      shimmerSize = \"0.05em\",\n      shimmerDuration = \"3s\",\n      borderRadius = \"100px\",\n      background,\n      className,\n      children,\n      size = \"md\",\n      variant = \"primary\",\n      ...props\n    },\n    ref,\n  ) => {\n    // Size-based styling\n    const sizeClasses = {\n      sm: \"px-4 py-2 text-sm\",\n      md: \"px-6 py-3 text-base\",\n      lg: \"px-8 py-4 text-lg\"\n    };\n\n    // Color variants with customization support - THEME-AWARE COLORS\n    const getColors = () => {\n      const baseColors = {\n        primary: {\n          // Light Mode: Vierla Forest, Dark Mode: Vierla Gold\n          bg: background || \"var(--theme-primary, #364035)\",\n          shimmer: shimmerColor || \"#F4F1E8\",  /* Warm Cream - Shimmer effect */\n        },\n        secondary: {\n          // Light Mode: Vierla Sage, Dark Mode: Muted Gold/Champagne\n          bg: background || \"var(--theme-secondary, #8B9A8C)\",\n          shimmer: shimmerColor || \"#F4F1E8\",  /* Warm Cream - Shimmer effect */\n        },\n        accent: {\n          // Light Mode: Vierla Forest, Dark Mode: Vierla Gold\n          bg: background || \"var(--theme-primary, #364035)\",\n          shimmer: shimmerColor || \"var(--theme-secondary, #8B9A8C)\",\n        }\n      };\n      return baseColors[variant];\n    };\n\n    const colors = getColors();\n\n    return (\n      <button\n        style={\n          {\n            \"--spread\": \"90deg\",\n            \"--shimmer-color\": colors.shimmer,\n            \"--radius\": borderRadius,\n            \"--speed\": shimmerDuration,\n            \"--cut\": shimmerSize,\n            \"--bg\": colors.bg,\n          } as CSSProperties\n        }\n        className={cn(\n          \"group relative z-0 flex cursor-pointer items-center justify-center overflow-hidden whitespace-nowrap border border-white/10 text-white [background:var(--bg)] [border-radius:var(--radius)] dark:text-black\",\n          \"transform-gpu transition-transform duration-300 ease-in-out active:translate-y-px\",\n          sizeClasses[size],\n          className,\n        )}\n        ref={ref}\n        {...props}\n      >\n        {/* spark container */}\n        <div\n          className={cn(\n            \"-z-30 blur-[2px]\",\n            \"absolute inset-0 overflow-visible [container-type:size]\",\n          )}\n        >\n          {/* spark */}\n          <div className=\"absolute inset-0 h-[100cqh] animate-shimmer-slide [aspect-ratio:1] [border-radius:0] [mask:none]\">\n            {/* spark before */}\n            <div className=\"animate-spin-around absolute -inset-full w-auto rotate-0 [background:conic-gradient(from_calc(270deg-(var(--spread)*0.5)),transparent_0,var(--shimmer-color)_var(--spread),transparent_var(--spread))] [translate:0_0]\" />\n          </div>\n        </div>\n        {children}\n\n        {/* Highlight */}\n        <div\n          className={cn(\n            \"insert-0 absolute size-full\",\n            \"rounded-2xl px-4 py-1.5 text-sm font-medium shadow-[inset_0_-8px_10px_#ffffff1f]\",\n            // transition\n            \"transform-gpu transition-all duration-300 ease-in-out\",\n            // on hover\n            \"group-hover:shadow-[inset_0_-6px_10px_#ffffff3f]\",\n            // on click\n            \"group-active:shadow-[inset_0_-10px_10px_#ffffff3f]\",\n          )}\n        />\n\n        {/* backdrop */}\n        <div\n          className={cn(\n            \"absolute -z-20 [background:var(--bg)] [border-radius:var(--radius)] [inset:var(--cut)]\",\n          )}\n        />\n      </button>\n    );\n  },\n);\n\nShimmerButton.displayName = \"ShimmerButton\";\n\nexport { ShimmerButton };\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAeA,MAAM,8BAAgB,qMAAA,CAAA,UAAK,CAAC,UAAU,CACpC,CACE,EACE,YAAY,EACZ,cAAc,QAAQ,EACtB,kBAAkB,IAAI,EACtB,eAAe,OAAO,EACtB,UAAU,EACV,SAAS,EACT,QAAQ,EACR,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,GAAG,OACJ,EACD;IAEA,qBAAqB;IACrB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,iEAAiE;IACjE,MAAM,YAAY;QAChB,MAAM,aAAa;YACjB,SAAS;gBACP,oDAAoD;gBACpD,IAAI,cAAc;gBAClB,SAAS,gBAAgB;YAC3B;YACA,WAAW;gBACT,2DAA2D;gBAC3D,IAAI,cAAc;gBAClB,SAAS,gBAAgB;YAC3B;YACA,QAAQ;gBACN,oDAAoD;gBACpD,IAAI,cAAc;gBAClB,SAAS,gBAAgB;YAC3B;QACF;QACA,OAAO,UAAU,CAAC,QAAQ;IAC5B;IAEA,MAAM,SAAS;IAEf,qBACE,8OAAC;QACC,OACE;YACE,YAAY;YACZ,mBAAmB,OAAO,OAAO;YACjC,YAAY;YACZ,WAAW;YACX,SAAS;YACT,QAAQ,OAAO,EAAE;QACnB;QAEF,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+MACA,qFACA,WAAW,CAAC,KAAK,EACjB;QAEF,KAAK;QACJ,GAAG,KAAK;;0BAGT,8OAAC;gBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,oBACA;0BAIF,cAAA,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;YAGlB;0BAGD,8OAAC;gBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,+BACA,oFACA,aAAa;gBACb,yDACA,WAAW;gBACX,oDACA,WAAW;gBACX;;;;;;0BAKJ,8OAAC;gBACC,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;;AAKV;AAGF,cAAc,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 273, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/get-started-button.tsx"], "sourcesContent": ["import { ShimmerButton } from \"@/components/ui/shimmer-button\";\nimport { ChevronRight } from \"lucide-react\";\nimport { cn } from \"@/lib/utils\";\n\ninterface GetStartedButtonProps {\n  className?: string;\n  size?: \"sm\" | \"md\" | \"lg\";\n  variant?: \"primary\" | \"secondary\" | \"accent\";\n  shimmerColor?: string;\n  backgroundColor?: string;\n  onClick?: () => void;\n  href?: string;\n}\n\nexport function GetStartedButton({\n  className,\n  size = \"lg\",\n  variant = \"primary\",\n  shimmerColor,\n  backgroundColor,\n  onClick,\n  href,\n}: GetStartedButtonProps) {\n  return (\n    <ShimmerButton\n      className={cn(\"group relative overflow-hidden\", className)}\n      size={size}\n      variant={variant}\n      shimmerColor={shimmerColor}\n      background={backgroundColor}\n      onClick={onClick}\n    >\n      <span className=\"mr-8 transition-opacity duration-500 group-hover:opacity-0\">\n        Request a Demo\n      </span>\n      <i className=\"absolute right-0.5 top-1 bottom-1 rounded-sm z-10 grid w-1/4 place-items-center transition-all duration-500 bg-white/15 group-hover:w-[calc(100%-0.5rem)] group-active:scale-95\">\n        <ChevronRight size={16} strokeWidth={2} aria-hidden=\"true\" style={{ color: 'var(--icon-on-accent)' }} />\n      </i>\n    </ShimmerButton>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAYO,SAAS,iBAAiB,EAC/B,SAAS,EACT,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,YAAY,EACZ,eAAe,EACf,OAAO,EACP,IAAI,EACkB;IACtB,qBACE,8OAAC,sIAAA,CAAA,gBAAa;QACZ,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,kCAAkC;QAChD,MAAM;QACN,SAAS;QACT,cAAc;QACd,YAAY;QACZ,SAAS;;0BAET,8OAAC;gBAAK,WAAU;0BAA6D;;;;;;0BAG7E,8OAAC;gBAAE,WAAU;0BACX,cAAA,8OAAC,sNAAA,CAAA,eAAY;oBAAC,MAAM;oBAAI,aAAa;oBAAG,eAAY;oBAAO,OAAO;wBAAE,OAAO;oBAAwB;;;;;;;;;;;;;;;;;AAI3G", "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/golden-glowing-card-container.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const GoldenGlowingCardContainer = registerClientReference(\n    function() { throw new Error(\"Attempted to call GoldenGlowingCardContainer() from the server but GoldenGlowingCardContainer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glowing-card-container.tsx <module evaluation>\",\n    \"GoldenGlowingCardContainer\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ui/golden-glowing-card-container.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glowing-card-container.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,6BAA6B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5D;IAAa,MAAM,IAAI,MAAM;AAAoQ,GACjS,iFACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmT,GAChV,iFACA", "debugId": null}}, {"offset": {"line": 347, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/ui/golden-glowing-card-container.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const GoldenGlowingCardContainer = registerClientReference(\n    function() { throw new Error(\"Attempted to call GoldenGlowingCardContainer() from the server but GoldenGlowingCardContainer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glowing-card-container.tsx\",\n    \"GoldenGlowingCardContainer\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/ui/golden-glowing-card-container.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/golden-glowing-card-container.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,6BAA6B,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5D;IAAa,MAAM,IAAI,MAAM;AAAoQ,GACjS,6DACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "debugId": null}}, {"offset": {"line": 363, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/components/seo/breadcrumb-schema.tsx"], "sourcesContent": ["import Script from 'next/script'\n\ninterface BreadcrumbItem {\n  name: string\n  url: string\n}\n\ninterface BreadcrumbSchemaProps {\n  items: BreadcrumbItem[]\n}\n\nexport function BreadcrumbSchema({ items }: BreadcrumbSchemaProps) {\n  const breadcrumbSchema = {\n    \"@context\": \"https://schema.org\",\n    \"@type\": \"BreadcrumbList\",\n    \"itemListElement\": items.map((item, index) => ({\n      \"@type\": \"ListItem\",\n      \"position\": index + 1,\n      \"name\": item.name,\n      \"item\": item.url\n    }))\n  }\n\n  return (\n    <Script\n      id=\"breadcrumb-schema\"\n      type=\"application/ld+json\"\n      dangerouslySetInnerHTML={{\n        __html: JSON.stringify(breadcrumbSchema)\n      }}\n    />\n  )\n}\n\n// Helper function to generate breadcrumbs for different pages\nexport function getBreadcrumbsForPage(page: string): BreadcrumbItem[] {\n  const baseBreadcrumb = { name: \"Home\", url: \"https://vierla.com\" }\n  \n  switch (page) {\n    case 'features':\n      return [\n        baseBreadcrumb,\n        { name: \"Features\", url: \"https://vierla.com/features\" }\n      ]\n    case 'pricing':\n      return [\n        baseBreadcrumb,\n        { name: \"Pricing\", url: \"https://vierla.com/pricing\" }\n      ]\n    case 'about':\n      return [\n        baseBreadcrumb,\n        { name: \"About\", url: \"https://vierla.com/about\" }\n      ]\n    case 'contact':\n      return [\n        baseBreadcrumb,\n        { name: \"Contact\", url: \"https://vierla.com/contact\" }\n      ]\n    case 'providers':\n      return [\n        baseBreadcrumb,\n        { name: \"For Providers\", url: \"https://vierla.com/providers\" }\n      ]\n    case 'apply':\n      return [\n        baseBreadcrumb,\n        { name: \"For Providers\", url: \"https://vierla.com/providers\" },\n        { name: \"Apply\", url: \"https://vierla.com/apply\" }\n      ]\n    case 'privacy':\n      return [\n        baseBreadcrumb,\n        { name: \"Privacy Policy\", url: \"https://vierla.com/privacy\" }\n      ]\n    case 'terms':\n      return [\n        baseBreadcrumb,\n        { name: \"Terms of Service\", url: \"https://vierla.com/terms\" }\n      ]\n    default:\n      return [baseBreadcrumb]\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;AAWO,SAAS,iBAAiB,EAAE,KAAK,EAAyB;IAC/D,MAAM,mBAAmB;QACvB,YAAY;QACZ,SAAS;QACT,mBAAmB,MAAM,GAAG,CAAC,CAAC,MAAM,QAAU,CAAC;gBAC7C,SAAS;gBACT,YAAY,QAAQ;gBACpB,QAAQ,KAAK,IAAI;gBACjB,QAAQ,KAAK,GAAG;YAClB,CAAC;IACH;IAEA,qBACE,8OAAC,8HAAA,CAAA,UAAM;QACL,IAAG;QACH,MAAK;QACL,yBAAyB;YACvB,QAAQ,KAAK,SAAS,CAAC;QACzB;;;;;;AAGN;AAGO,SAAS,sBAAsB,IAAY;IAChD,MAAM,iBAAiB;QAAE,MAAM;QAAQ,KAAK;IAAqB;IAEjE,OAAQ;QACN,KAAK;YACH,OAAO;gBACL;gBACA;oBAAE,MAAM;oBAAY,KAAK;gBAA8B;aACxD;QACH,KAAK;YACH,OAAO;gBACL;gBACA;oBAAE,MAAM;oBAAW,KAAK;gBAA6B;aACtD;QACH,KAAK;YACH,OAAO;gBACL;gBACA;oBAAE,MAAM;oBAAS,KAAK;gBAA2B;aAClD;QACH,KAAK;YACH,OAAO;gBACL;gBACA;oBAAE,MAAM;oBAAW,KAAK;gBAA6B;aACtD;QACH,KAAK;YACH,OAAO;gBACL;gBACA;oBAAE,MAAM;oBAAiB,KAAK;gBAA+B;aAC9D;QACH,KAAK;YACH,OAAO;gBACL;gBACA;oBAAE,MAAM;oBAAiB,KAAK;gBAA+B;gBAC7D;oBAAE,MAAM;oBAAS,KAAK;gBAA2B;aAClD;QACH,KAAK;YACH,OAAO;gBACL;gBACA;oBAAE,MAAM;oBAAkB,KAAK;gBAA6B;aAC7D;QACH,KAAK;YACH,OAAO;gBACL;gBACA;oBAAE,MAAM;oBAAoB,KAAK;gBAA2B;aAC7D;QACH;YACE,OAAO;gBAAC;aAAe;IAC3B;AACF", "debugId": null}}, {"offset": {"line": 486, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Files/Services_Startup/Workspace/services-app-web/code/services-web-app/services-app-web-v1/vierla-rebuild/app/features/page.tsx"], "sourcesContent": ["import { Metadata } from 'next'\nimport { AuroraBackgroundLayer } from \"@/components/ui/aurora-background\";\nimport { BentoCard, BentoGrid } from \"@/components/ui/bento-grid\";\nimport { GetStartedButton } from \"@/components/ui/get-started-button\";\nimport { GoldenGlowingCardContainer } from \"@/components/ui/golden-glowing-card-container\";\nimport { StructuredData } from \"@/components/seo/structured-data\";\nimport { BreadcrumbSchema, getBreadcrumbsForPage } from \"@/components/seo/breadcrumb-schema\";\nimport { LayoutTemplate, FileText, Users, BarChart2, Palette, Layers, Brain, HeartHandshake } from \"lucide-react\";\n\nexport const metadata: Metadata = {\n  title: 'Features - Powerful Business Tools',\n  description: 'Discover Vierla\\'s comprehensive business features: AI website builder, smart invoicing, CRM system, analytics dashboard, and brand management tools for beauty professionals.',\n  openGraph: {\n    title: 'Features - Powerful Business Tools | Vierla',\n    description: 'Everything you need to build, manage, and grow your beauty business in one integrated platform.',\n    url: 'https://vierla.com/features',\n    siteName: 'Vierla',\n    locale: 'en_CA',\n    type: 'website',\n  },\n  twitter: {\n    card: 'summary_large_image',\n    title: 'Features - Powerful Business Tools | Vierla',\n    description: 'Everything you need to build, manage, and grow your beauty business in one integrated platform.',\n  },\n};\n\nexport default function Features() {\n  return (\n    <div className=\"page-features relative overflow-hidden\">\n      <StructuredData page=\"features\" />\n      <BreadcrumbSchema items={getBreadcrumbsForPage('features')} />\n      <AuroraBackgroundLayer />\n\n      {/* Hero Section */}\n      <section className=\"relative z-10 w-full px-4 py-20 pt-36\">\n        <div className=\"text-center max-w-6xl mx-auto\">\n          <h1 className=\"text-4xl md:text-6xl font-black mb-6 leading-none text-[#2D2A26] dark:text-[#F4F1E8] drop-shadow-lg font-sans\">\n            POWERFUL FEATURES FOR YOUR BUSINESS\n          </h1>\n          <p className=\"text-xl md:text-2xl text-[#2D2A26] dark:text-[#A9A299] mb-8 leading-relaxed max-w-4xl mx-auto drop-shadow-sm font-inter\">\n            Everything you need to build, manage, and grow your business in one integrated platform.\n          </p>\n        </div>\n      </section>\n\n      {/* Features Bento Grid */}\n      <section className=\"relative z-10 py-20 border-t border-white/20\">\n        <div className=\"container mx-auto px-4\">\n          <BentoGrid className=\"lg:grid-rows-3 max-w-6xl mx-auto min-h-[800px]\">\n            {[\n              {\n                Icon: LayoutTemplate,\n                name: \"Website Builder\",\n                description: \"Create stunning, professional websites with our intuitive drag-and-drop builder. Choose from industry-specific templates, customize your brand colors and fonts, integrate booking systems, and launch your online presence without any coding knowledge. Perfect for beauty professionals who want to showcase their work and attract new clients.\",\n                href: \"/contact\",\n                cta: \"Contact Us\",\n                background: <div className=\"absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full blur-3xl\" />,\n                className: \"lg:row-start-1 lg:row-end-4 lg:col-start-2 lg:col-end-3\",\n              },\n              {\n                Icon: FileText,\n                name: \"Smart Invoicing\",\n                description: \"Generate professional invoices instantly and get paid faster with automated payment reminders and multiple payment options. Track payment status, manage recurring billing for regular clients, apply discounts and taxes automatically, and maintain detailed financial records. Streamline your billing process and improve cash flow with intelligent payment tracking.\",\n                href: \"/contact\",\n                cta: \"Contact Us\",\n                background: <div className=\"absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full blur-3xl\" />,\n                className: \"lg:col-start-1 lg:col-end-2 lg:row-start-1 lg:row-end-3\",\n              },\n              {\n                Icon: Users,\n                name: \"CRM System\",\n                description: \"Manage customer relationships with a comprehensive CRM that tracks client preferences, service history, and communication logs. Set up automated follow-ups, manage leads through your sales pipeline, segment clients for targeted marketing campaigns, and build lasting relationships that drive repeat business and referrals.\",\n                href: \"/contact\",\n                cta: \"Contact Us\",\n                background: <div className=\"absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full blur-3xl\" />,\n                className: \"lg:col-start-1 lg:col-end-2 lg:row-start-3 lg:row-end-4\",\n              },\n              {\n                Icon: BarChart2,\n                name: \"Analytics Dashboard\",\n                description: \"Get comprehensive insights into your business performance with real-time analytics and detailed reporting. Track revenue trends, monitor client acquisition costs, analyze service popularity, measure marketing campaign effectiveness, and identify growth opportunities. Make data-driven decisions to optimize your business operations and maximize profitability.\",\n                href: \"/contact\",\n                cta: \"Contact Us\",\n                background: <div className=\"absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full blur-3xl\" />,\n                className: \"lg:col-start-3 lg:col-end-3 lg:row-start-1 lg:row-end-2\",\n              },\n              {\n                Icon: Palette,\n                name: \"Brand Management\",\n                description: \"Maintain consistent branding across all your business touchpoints with centralized brand management tools. Upload your logo, define brand colors and fonts, create branded templates for invoices and marketing materials, and ensure professional consistency across your website, social media, and client communications. Build a memorable brand that stands out in the beauty industry.\",\n                href: \"/contact\",\n                cta: \"Contact Us\",\n                background: <div className=\"absolute -right-20 -top-20 opacity-60 w-40 h-40 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full blur-3xl\" />,\n                className: \"lg:col-start-3 lg:col-end-3 lg:row-start-2 lg:row-end-4\",\n              },\n            ].map((feature) => (\n              <BentoCard key={feature.name} {...feature} />\n            ))}\n          </BentoGrid>\n        </div>\n      </section>\n\n      {/* The Vierla Advantage Section */}\n      <section className=\"relative z-10 py-16 border-t border-white/20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"text-center mb-12\">\n            <h2 className=\"text-4xl md:text-5xl font-black text-[#2D2A26] dark:text-[#F4F1E8] mb-6 drop-shadow-lg font-jost\">\n              The Vierla Advantage\n            </h2>\n            <p className=\"text-xl text-[#2D2A26] dark:text-[#A9A299] max-w-3xl mx-auto drop-shadow-sm font-inter\">\n              Discover what sets Vierla apart as the ultimate platform for beauty professionals and business owners.\n            </p>\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto\">\n            {/* All-in-One Platform */}\n            <GoldenGlowingCardContainer>\n              <div className=\"text-center p-6\">\n                <div className=\"w-16 h-16 mx-auto mb-6 rounded-full flex items-center justify-center bg-gradient-to-br from-theme-primary/20 to-theme-primary/10 border-2 border-theme-primary/30\">\n                  <Layers className=\"w-8 h-8\" style={{ color: 'var(--theme-primary)' }} />\n                </div>\n                <h3 className=\"text-2xl font-bold text-[#2D2A26] dark:text-[#F4F1E8] mb-4 drop-shadow-lg font-tai-heritage\">All-in-One Platform</h3>\n                <p className=\"text-sm sm:text-base text-[#2D2A26] dark:text-[#A9A299] leading-relaxed drop-shadow-sm font-inter\">\n                  Consolidate your tools for booking, marketing, and payments into one seamless platform. No more juggling multiple subscriptions or learning different systems. Everything you need to run your beauty business is right here.\n                </p>\n              </div>\n            </GoldenGlowingCardContainer>\n\n            {/* AI-Powered Insights */}\n            <GoldenGlowingCardContainer>\n              <div className=\"text-center p-6\">\n                <div className=\"w-16 h-16 mx-auto mb-6 rounded-full flex items-center justify-center bg-gradient-to-br from-theme-primary/20 to-theme-primary/10 border-2 border-theme-primary/30\">\n                  <Brain className=\"w-8 h-8\" style={{ color: 'var(--theme-primary)' }} />\n                </div>\n                <h3 className=\"text-2xl font-bold text-[#2D2A26] dark:text-[#F4F1E8] mb-4 drop-shadow-lg font-tai-heritage\">AI-Powered Insights</h3>\n                <p className=\"text-sm sm:text-base text-[#2D2A26] dark:text-[#A9A299] leading-relaxed drop-shadow-sm font-inter\">\n                  Leverage data to grow your business and understand your clients better. Our AI analyzes booking patterns, client preferences, and market trends to provide actionable insights that drive revenue growth and client satisfaction.\n                </p>\n              </div>\n            </GoldenGlowingCardContainer>\n\n            {/* Dedicated Growth Partner */}\n            <GoldenGlowingCardContainer>\n              <div className=\"text-center p-6\">\n                <div className=\"w-16 h-16 mx-auto mb-6 rounded-full flex items-center justify-center bg-gradient-to-br from-theme-primary/20 to-theme-primary/10 border-2 border-theme-primary/30\">\n                  <HeartHandshake className=\"w-8 h-8\" style={{ color: 'var(--theme-primary)' }} />\n                </div>\n                <h3 className=\"text-2xl font-bold text-[#2D2A26] dark:text-[#F4F1E8] mb-4 drop-shadow-lg font-tai-heritage\">Dedicated Growth Partner</h3>\n                <p className=\"text-sm sm:text-base text-[#2D2A26] dark:text-[#A9A299] leading-relaxed drop-shadow-sm font-inter\">\n                  Access hands-on support focused on your success. Our dedicated team provides personalized guidance, marketing strategies, and business coaching to help you achieve your goals and build a thriving beauty business.\n                </p>\n              </div>\n            </GoldenGlowingCardContainer>\n          </div>\n        </div>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"relative z-10 pt-12 pb-0\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto\">\n            <GoldenGlowingCardContainer interactive={false}>\n              <div className=\"text-center pt-8 pb-4 px-6\">\n                <h2 className=\"font-heading text-3xl leading-[1.1] sm:text-3xl md:text-6xl text-[#2D2A26] dark:text-[#F4F1E8] drop-shadow-lg font-jost mb-6\">\n                  Ready to transform your business?\n                </h2>\n                <p className=\"max-w-[42rem] leading-normal text-[#2D2A26] dark:text-[#A9A299] sm:text-xl sm:leading-8 drop-shadow-sm font-inter mb-8 mx-auto\">\n                  Join beauty entrepreneurs and business owners who trust Vierla to power their business operations.\n                </p>\n                <div className=\"flex justify-center\">\n                  <a href=\"/apply\">\n                    <GetStartedButton />\n                  </a>\n                </div>\n              </div>\n            </GoldenGlowingCardContainer>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,WAAW;QACT,OAAO;QACP,aAAa;QACb,KAAK;QACL,UAAU;QACV,QAAQ;QACR,MAAM;IACR;IACA,SAAS;QACP,MAAM;QACN,OAAO;QACP,aAAa;IACf;AACF;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,wIAAA,CAAA,iBAAc;gBAAC,MAAK;;;;;;0BACrB,8OAAC,0IAAA,CAAA,mBAAgB;gBAAC,OAAO,CAAA,GAAA,0IAAA,CAAA,wBAAqB,AAAD,EAAE;;;;;;0BAC/C,8OAAC,yIAAA,CAAA,wBAAqB;;;;;0BAGtB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAgH;;;;;;sCAG9H,8OAAC;4BAAE,WAAU;sCAA0H;;;;;;;;;;;;;;;;;0BAO3I,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kIAAA,CAAA,YAAS;wBAAC,WAAU;kCAClB;4BACC;gCACE,MAAM,0NAAA,CAAA,iBAAc;gCACpB,MAAM;gCACN,aAAa;gCACb,MAAM;gCACN,KAAK;gCACL,0BAAY,8OAAC;oCAAI,WAAU;;;;;;gCAC3B,WAAW;4BACb;4BACA;gCACE,MAAM,8MAAA,CAAA,WAAQ;gCACd,MAAM;gCACN,aAAa;gCACb,MAAM;gCACN,KAAK;gCACL,0BAAY,8OAAC;oCAAI,WAAU;;;;;;gCAC3B,WAAW;4BACb;4BACA;gCACE,MAAM,oMAAA,CAAA,QAAK;gCACX,MAAM;gCACN,aAAa;gCACb,MAAM;gCACN,KAAK;gCACL,0BAAY,8OAAC;oCAAI,WAAU;;;;;;gCAC3B,WAAW;4BACb;4BACA;gCACE,MAAM,gOAAA,CAAA,YAAS;gCACf,MAAM;gCACN,aAAa;gCACb,MAAM;gCACN,KAAK;gCACL,0BAAY,8OAAC;oCAAI,WAAU;;;;;;gCAC3B,WAAW;4BACb;4BACA;gCACE,MAAM,wMAAA,CAAA,UAAO;gCACb,MAAM;gCACN,aAAa;gCACb,MAAM;gCACN,KAAK;gCACL,0BAAY,8OAAC;oCAAI,WAAU;;;;;;gCAC3B,WAAW;4BACb;yBACD,CAAC,GAAG,CAAC,CAAC,wBACL,8OAAC,kIAAA,CAAA,YAAS;gCAAqB,GAAG,OAAO;+BAAzB,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;0BAOpC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmG;;;;;;8CAGjH,8OAAC;oCAAE,WAAU;8CAAyF;;;;;;;;;;;;sCAKxG,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC,2JAAA,CAAA,6BAA0B;8CACzB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;oDAAU,OAAO;wDAAE,OAAO;oDAAuB;;;;;;;;;;;0DAErE,8OAAC;gDAAG,WAAU;0DAA8F;;;;;;0DAC5G,8OAAC;gDAAE,WAAU;0DAAoG;;;;;;;;;;;;;;;;;8CAOrH,8OAAC,2JAAA,CAAA,6BAA0B;8CACzB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;oDAAU,OAAO;wDAAE,OAAO;oDAAuB;;;;;;;;;;;0DAEpE,8OAAC;gDAAG,WAAU;0DAA8F;;;;;;0DAC5G,8OAAC;gDAAE,WAAU;0DAAoG;;;;;;;;;;;;;;;;;8CAOrH,8OAAC,2JAAA,CAAA,6BAA0B;8CACzB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,0NAAA,CAAA,iBAAc;oDAAC,WAAU;oDAAU,OAAO;wDAAE,OAAO;oDAAuB;;;;;;;;;;;0DAE7E,8OAAC;gDAAG,WAAU;0DAA8F;;;;;;0DAC5G,8OAAC;gDAAE,WAAU;0DAAoG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU3H,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,2JAAA,CAAA,6BAA0B;4BAAC,aAAa;sCACvC,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA+H;;;;;;kDAG7I,8OAAC;wCAAE,WAAU;kDAAiI;;;;;;kDAG9I,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,MAAK;sDACN,cAAA,8OAAC,6IAAA,CAAA,mBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrC", "debugId": null}}]}